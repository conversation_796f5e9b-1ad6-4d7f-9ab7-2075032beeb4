create table postgres_log
(
    log_time               timestamp(3) with time zone,
    user_name              text,
    database_name          text,
    process_id             integer,
    connection_from        text,
    session_id             text   not null,
    session_line_num       bigint not null,
    command_tag            text,
    session_start_time     timestamp with time zone,
    virtual_transaction_id text,
    transaction_id         bigint,
    error_severity         text,
    sql_state_code         text,
    message                text,
    detail                 text,
    hint                   text,
    internal_query         text,
    internal_query_pos     integer,
    context                text,
    query                  text,
    query_pos              integer,
    location               text,
    application_name       text,
    backend_type           text,
    leader_pid             integer,
    query_id               bigint,
    constraint postgres_log_check
        check (false) no inherit
);

alter table postgres_log
    owner to postgres;

grant select on postgres_log to admin;

create table coupons
(
    id                  uuid               not null
        primary key,
    code                varchar(50)        not null
        unique,
    description         text,
    discount_type       discount_type_enum not null,
    discount_value      double precision   not null,
    min_order_value     double precision   default 0,
    max_discount_amount double precision,
    start_date          bigint             not null,
    end_date            bigint             not null,
    usage_limit         integer,
    per_user_limit      integer            default 1,
    status              coupon_status_enum default 'ACTIVE'::coupon_status_enum,
    created_at          bigint             not null,
    updated_at          bigint             not null
);

comment on table coupons is '<PERSON><PERSON>ng quản lý thông tin mã giảm giá (coupons) trong hệ thống';

comment on column coupons.id is 'Định danh mã giảm giá (UUID)';

comment on column coupons.code is 'Mã giảm giá do hệ thống hoặc admin tạo';

comment on column coupons.description is 'Mô tả chi tiết về mã giảm giá';

comment on column coupons.discount_type is 'Loại giảm giá: phần trăm hoặc số tiền cố định';

comment on column coupons.discount_value is 'Giá trị giảm giá tương ứng với loại (%, số tiền)';

comment on column coupons.min_order_value is 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá';

comment on column coupons.max_discount_amount is 'Giảm giá tối đa cho mã giảm giá loại phần trăm';

comment on column coupons.start_date is 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)';

comment on column coupons.end_date is 'Thời điểm kết thúc áp dụng mã (Unix timestamp)';

comment on column coupons.usage_limit is 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống';

comment on column coupons.per_user_limit is 'Số lần một người dùng được sử dụng mã này';

comment on column coupons.status is 'Trạng thái mã giảm giá: ACTIVE, INACTIVE, hoặc EXPIRED';

comment on column coupons.created_at is 'Thời gian tạo mã (Unix timestamp)';

comment on column coupons.updated_at is 'Thời gian cập nhật mã gần nhất (Unix timestamp)';

alter table coupons
    owner to root;

create table webhook_logs
(
    id               uuid                  not null
        primary key,
    transaction_id   bigserial,
    transaction_type transaction_type_enum not null,
    source           varchar(50)           not null,
    payload          jsonb                 not null,
    status           webhook_status_enum default 'PROCESSING'::webhook_status_enum,
    response_code    varchar(20),
    response_message text,
    processed_at     bigint,
    created_at       bigint                not null
);

comment on column webhook_logs.id is 'Định danh log webhook';

comment on column webhook_logs.transaction_id is 'Định danh giao dịch';

comment on column webhook_logs.transaction_type is 'Loại giao dịch (POINT_PURCHASE, POINT_WITHDRAWAL)';

comment on column webhook_logs.source is 'Nguồn webhook';

comment on column webhook_logs.payload is 'Dữ liệu webhook';

comment on column webhook_logs.status is 'Trạng thái xử lý webhook (SUCCESS, FAILED, PROCESSING)';

comment on column webhook_logs.response_code is 'Mã phản hồi';

comment on column webhook_logs.response_message is 'Thông điệp phản hồi';

comment on column webhook_logs.processed_at is 'Thời gian xử lý (Unix timestamp)';

comment on column webhook_logs.created_at is 'Thời gian tạo (Unix timestamp)';

alter table webhook_logs
    owner to root;

create table points
(
    id           serial
        constraint points_pk
            primary key,
    name         varchar(255),
    cash         double precision      not null,
    rate         double precision,
    min          double precision,
    max          double precision,
    point        bigint                not null,
    is_customize boolean default false not null,
    description  varchar(255)
);

comment on column points.name is 'tên của gói rpoint';

comment on column points.cash is 'Số tiền của gói point không phải customize';

comment on column points.rate is 'Tỷ lệ rate';

comment on column points.min is 'Nếu là customize thì có quy định min';

comment on column points.max is 'Nếu là customize thì có quy định số tiền max';

comment on column points.point is 'Số point';

comment on column points.is_customize is 'Có phải gói customize không';

comment on column points.description is 'Mô tả';

alter table points
    owner to root;

create table banks
(
    bank_code varchar(20) not null
        constraint banks_pk
            primary key,
    bank_name varchar(255),
    logo_path varchar(255),
    full_name varchar(255),
    icon_path varchar(255),
    bin       varchar(20)
);

comment on table banks is 'Danh sách ngân hàng';

comment on column banks.bank_code is 'Mã code';

comment on column banks.bank_name is 'Tên ngân hàng';

comment on column banks.logo_path is 'path logo';

comment on column banks.full_name is 'Tên đầy đủ của ngân hàng';

comment on column banks.icon_path is 'path icon';

alter table banks
    owner to root;

create table affiliate_withdraw_history
(
    id                   bigint             not null
        constraint affiliate_withdraw_history_pk
            primary key,
    type                 contract_type_enum not null,
    affiliate_account_id integer            not null,
    amount               double precision   not null,
    balance_before       double precision,
    balance_after        double precision,
    bank_code            varchar(20)        not null,
    account_number       varchar(50)        not null,
    account_name         varchar(255)       not null,
    status               varchar(20)        not null,
    created_at           bigint             not null,
    finish_at            bigint,
    vat_amount           double precision   not null,
    net_payment          double precision   not null,
    reject_reason        varchar(2000),
    purchase_invoice     varchar(255)
);

comment on table affiliate_withdraw_history is 'Danh sách yêu cầu rút tiền của người dùng';

comment on column affiliate_withdraw_history.affiliate_account_id is 'Mã tài khoản affiliate';

comment on column affiliate_withdraw_history.amount is 'Số tiền rút';

comment on column affiliate_withdraw_history.balance_before is 'Số dư trước giao dịch';

comment on column affiliate_withdraw_history.balance_after is 'Số dư sau giao dịch';

comment on column affiliate_withdraw_history.bank_code is 'Mã ngân hàng';

comment on column affiliate_withdraw_history.account_number is 'Số tài khoản ngân hàng';

comment on column affiliate_withdraw_history.account_name is 'Tên tài khoản ngân hàng';

comment on column affiliate_withdraw_history.status is 'Trạng thái yêu cầu';

comment on column affiliate_withdraw_history.created_at is 'Thời gian tạo yêu cầu';

comment on column affiliate_withdraw_history.finish_at is 'Thời gian kết thúc yêu cầu';

comment on column affiliate_withdraw_history.vat_amount is 'Tiền thuế VAT';

comment on column affiliate_withdraw_history.net_payment is 'Tiền thực nhận';

comment on column affiliate_withdraw_history.reject_reason is 'Lý do từ chối';

comment on column affiliate_withdraw_history.purchase_invoice is 'Đường dẫn hóa đơn đầu vào';

alter table affiliate_withdraw_history
    owner to root;

create table employees
(
    id           serial
        constraint employees_pk
            primary key,
    full_name    varchar(255)         not null,
    email        varchar(100)         not null
        constraint employees_pk_2
            unique,
    phone_number varchar(20)          not null
        constraint employees_pk_3
            unique,
    password     varchar(1000)        not null,
    address      varchar(2000)        not null,
    created_at   bigint               not null,
    updated_at   bigint,
    enable       boolean default true not null
);

comment on table employees is 'Tài khoản nhân viên';

comment on column employees.email is 'Email nhân viên';

alter table employees
    owner to root;

create table departments
(
    id         integer not null
        constraint departments_pk
            primary key,
    name       varchar(255),
    created_at bigint,
    update_at  bigint
);

comment on table departments is 'Phòng ban';

comment on column departments.name is 'Tên phòng ban';

comment on column departments.created_at is 'Thời gian tạo';

comment on column departments.update_at is 'Thời gian cập nhật';

alter table departments
    owner to root;

create table employee_roles
(
    id          integer      not null
        constraint employee_roles_pk
            primary key,
    name        varchar(255) not null,
    description varchar(500) not null
);

comment on table employee_roles is 'Danh sách role của nhân viên';

comment on column employee_roles.name is 'Tên role';

comment on column employee_roles.description is 'Mô tả';

alter table employee_roles
    owner to root;

create table employee_has_roles
(
    employee_id integer not null
        constraint employee_has_roles_employees_id_fk
            references employees,
    role_id     integer not null
        constraint employee_has_roles_roles_id_fk
            references employee_roles,
    constraint employee_has_roles_pk
        primary key (employee_id, role_id)
);

alter table employee_has_roles
    owner to root;

create table permissions
(
    id          integer default nextval('api_mapping_id_seq'::regclass) not null
        constraint permissions_pk_2
            primary key,
    action      varchar(255),
    description text,
    created_at  bigint                                                  not null,
    updated_at  bigint,
    module      varchar(50),
    constraint permissions_pk
        unique (action, module)
);

comment on column permissions.action is 'Hành động';

comment on column permissions.description is 'Mô tả quyền';

comment on column permissions.created_at is 'Thời gian tạo';

comment on column permissions.updated_at is 'Thời gian cập nhật';

comment on column permissions.module is 'Module';

alter table permissions
    owner to root;

create table employee_role_has_permission
(
    role_id       integer not null
        constraint employee_role_has_permission_employee_roles_id_fk
            references employee_roles,
    permission_id integer not null
        constraint employee_role_has_permission_api_mapping_id_fk
            references permissions,
    constraint employee_role_has_permission_pk
        primary key (permission_id, role_id)
);

alter table employee_role_has_permission
    owner to root;

create table plans
(
    id           serial
        primary key,
    name         varchar(100) not null,
    description  text,
    created_at   bigint       not null,
    updated_at   bigint       not null,
    package_type package_type default 'TIME_ONLY'::package_type
);

comment on table plans is 'Bảng lưu gói dịch vụ chính (subscription-based)';

comment on column plans.id is 'Định danh gói dịch vụ (UUID)';

comment on column plans.name is 'Tên gói (Basic, Pro, Enterprise...)';

comment on column plans.description is 'Mô tả chi tiết gói';

comment on column plans.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column plans.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table plans
    owner to root;

create table plan_pricing
(
    id            serial
        primary key,
    plan_id       serial
        references plans
            on delete cascade,
    billing_cycle varchar(50) not null,
    price         numeric     not null,
    created_at    bigint      not null,
    updated_at    bigint,
    usage_limit   bigint,
    usage_unit    varchar(50),
    is_active     boolean     not null,
    unique (plan_id, billing_cycle)
);

comment on table plan_pricing is 'Bảng lưu các mức giá khác nhau cho mỗi plan theo chu kỳ thanh toán';

comment on column plan_pricing.plan_id is 'Khóa ngoại tới bảng plans';

comment on column plan_pricing.billing_cycle is 'Chu kỳ thanh toán (month, 6_months, year,...)';

comment on column plan_pricing.price is 'Giá của plan ứng với chu kỳ thanh toán này';

comment on column plan_pricing.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column plan_pricing.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

comment on column plan_pricing.usage_limit is 'Giới hạn dung lượng (có thể là NULL nếu là TIME_ONLY). Dùng BIGINT để lưu giá trị lớn (vd: bytes, requests)';

comment on column plan_pricing.usage_unit is 'Đơn vị của giá trị gói';

alter table plan_pricing
    owner to root;

create table user_roles
(
    id         serial
        constraint user_roles_pk
            primary key,
    type       varchar(255)
        constraint user_roles_pk_2
            unique,
    name       varchar(255),
    created_at bigint,
    updated_at bigint
);

alter table user_roles
    owner to root;

create table user_roles_has_permission
(
    role_id       integer not null
        constraint user_roles_has_permission_user_roles_id_fk
            references user_roles,
    permission_id integer not null
        constraint user_roles_has_permission_permissions_id_fk
            references permissions,
    constraint user_roles_has_permission_pk
        primary key (permission_id, role_id)
);

alter table user_roles_has_permission
    owner to root;

create table plan_pricing_has_role
(
    plan_pricing_id integer not null
        constraint plan_pricing_has_role_plan_pricing_id_fk
            references plan_pricing,
    role_id         integer not null
        constraint plan_pricing_has_role_user_roles_id_fk
            references user_roles,
    constraint plan_pricing_has_role_pk
        primary key (plan_pricing_id, role_id)
);

comment on table plan_pricing_has_role is 'Bảng gói dịch vụ và quyền của gói dịch vụ đó';

comment on column plan_pricing_has_role.plan_pricing_id is 'mã gói dịch vụ';

comment on column plan_pricing_has_role.role_id is 'mã quyền ';

alter table plan_pricing_has_role
    owner to root;

create table system_configuration
(
    id                        integer not null
        constraint system_configuration_pk
            primary key,
    bank_code                 varchar(20)
        constraint system_configuration_banks_bank_code_fk
            references banks,
    created_at                bigint,
    updated_at                bigint,
    account_number            varchar(255),
    account_name              varchar(255),
    active                    boolean,
    fee_percentage            double precision,
    purchase_invoice_template varchar(255)
);

comment on table system_configuration is 'Cấu hình của hệ thống';

comment on column system_configuration.bank_code is 'Mã ngân hàng';

comment on column system_configuration.created_at is 'Thời gian tạo';

comment on column system_configuration.updated_at is 'Thời gian cập nhật';

comment on column system_configuration.account_number is 'Số tài khoản';

comment on column system_configuration.account_name is 'Tên tài khoản';

comment on column system_configuration.active is 'Kích hoạt hay không';

comment on column system_configuration.fee_percentage is 'Lưu trữ phần trăm phí sàn, ví dụ: 2.50 nghĩa là 2.50%';

comment on column system_configuration.purchase_invoice_template is 'Link mẫu hóa đơn đầu vào';

alter table system_configuration
    owner to root;

create table products
(
    id              bigserial
        constraint products_pk
            primary key,
    type            varchar(20),
    name            varchar(500),
    description     text,
    price           double precision,
    approval_status varchar(10),
    status          boolean,
    created_at      bigint,
    updated_at      bigint
);

comment on table products is 'Bảng sản phẩm trong chợ';

comment on column products.name is 'Tên sản phẩm';

comment on column products.description is 'Mô tả';

comment on column products.price is 'Giá point';

comment on column products.approval_status is 'Có thể là ''PENDING'' (chờ duyệt), ''APPROVED'' (đã duyệt), ''BLOCK'' (bị cấm), ''REJECTED'' (từ chối)';

comment on column products.status is 'Trạng thái hoạt động';

comment on column products.created_at is 'Thời gian tạo';

comment on column products.updated_at is 'Thời gian cập nhật';

alter table products
    owner to root;

create table market_order_line
(
    id                   bigint  not null
        constraint market_order_line_pk
            primary key,
    product_id           bigint
        constraint market_order_line_products_id_fk
            references products,
    created_at           bigint,
    updated_at           bigint,
    point                bigint,
    product_name         varchar(500),
    platform_fee_percent double precision,
    seller_receive_price bigint,
    quantity             integer not null
);

comment on column market_order_line.product_id is 'Mã sản phẩm';

comment on column market_order_line.updated_at is 'Thời gian cập nhật';

comment on column market_order_line.point is 'Số point của sản phẩm';

comment on column market_order_line.product_name is 'Tên sản phẩm';

comment on column market_order_line.platform_fee_percent is 'Phần trăm phí sàn mức';

comment on column market_order_line.seller_receive_price is 'Giá người bán nhận được sau khi trừ phí sàn';

comment on column market_order_line.quantity is 'Số lượng';

alter table market_order_line
    owner to root;

create table admin_template_email
(
    id           serial
        constraint admin_template_email_pk_2
            primary key,
    subject      varchar(255),
    category     varchar(100)
        constraint admin_template_email_pk
            unique,
    content      text,
    created_at   bigint,
    updated_at   bigint,
    placeholders json,
    name         varchar(100),
    created_by   bigint,
    updated_by   bigint
);

comment on table admin_template_email is 'Danh sách template email bên admin';

comment on column admin_template_email.subject is 'Tiêu đề';

comment on column admin_template_email.category is 'Danh mục email';

comment on column admin_template_email.content is 'Nội dung email';

comment on column admin_template_email.created_at is 'Thời gian tạo';

comment on column admin_template_email.updated_at is 'Thời gian cập nhật';

comment on column admin_template_email.placeholders is 'Danh sách các placeholder';

comment on column admin_template_email.name is 'Tên mẫu';

alter table admin_template_email
    owner to root;

create table affiliate_rank
(
    id            integer default nextval('affiliate_rank_rank_id_seq'::regclass) not null
        primary key,
    commission    double precision,
    min_condition bigint,
    max_condition bigint,
    rank_name     varchar(45),
    rank_badge    varchar(255)
);

comment on table affiliate_rank is 'Bảng rank của affiliate';

comment on column affiliate_rank.id is 'mã rank';

comment on column affiliate_rank.commission is 'Phần trăm hoa hồng';

comment on column affiliate_rank.min_condition is 'Số lượng người giới thiệu tối thiểu';

comment on column affiliate_rank.max_condition is 'Số lượng người giới thiệu tối đa';

comment on column affiliate_rank.rank_name is 'Tên rank';

comment on column affiliate_rank.rank_badge is 'Logo rank';

alter table affiliate_rank
    owner to root;

create table admin_template_sms
(
    id           serial
        primary key,
    category     varchar(100)
        unique,
    content      text,
    created_at   bigint,
    updated_at   bigint,
    placeholders json,
    name         varchar(100)
);

comment on table admin_template_sms is 'Danh sách template sms bên admin';

comment on column admin_template_sms.category is 'Danh mục sms';

comment on column admin_template_sms.content is 'Nội dung sms';

comment on column admin_template_sms.created_at is 'Thời gian tạo';

comment on column admin_template_sms.updated_at is 'Thời gian cập nhật';

comment on column admin_template_sms.placeholders is 'Danh sách các placeholder';

comment on column admin_template_sms.name is 'Tên mẫu';

alter table admin_template_sms
    owner to root;

create table admin_audience
(
    id         bigserial
        primary key,
    email      varchar(255),
    phone      varchar(20),
    created_at bigint not null,
    updated_at bigint
);

comment on table admin_audience is 'Bảng khách hàng của admin';

comment on column admin_audience.email is 'Email người dùng';

comment on column admin_audience.phone is 'Số điện thoại';

comment on column admin_audience.created_at is 'Ngày tạo';

comment on column admin_audience.updated_at is 'Ngày cập nhật';

alter table admin_audience
    owner to root;

create table admin_tags
(
    id         bigserial
        primary key,
    name       varchar(255),
    color      varchar(7),
    created_at bigint,
    updated_at bigint,
    created_by integer
        constraint admin_tags_employees_id_fk
            references employees,
    updated_by integer
        constraint admin_tags_employees_id_fk_2
            references employees
);

alter table admin_tags
    owner to root;

create table admin_audience_has_tags
(
    audience_id bigint not null
        constraint admin_audience_has_tags_admin_audience_id_fk
            references admin_audience,
    tag_id      bigint not null
        constraint admin_audience_has_tags_admin_tags_id_fk
            references admin_tags,
    constraint admin_audience_has_tags_pk
        primary key (tag_id, audience_id)
);

alter table admin_audience_has_tags
    owner to root;

create table admin_segments
(
    id          bigserial
        primary key,
    name        varchar(255),
    description text,
    criteria    jsonb,
    created_at  bigint,
    updated_at  bigint
);

comment on table admin_segments is 'Phân khúc khách hàng của admin';

comment on column admin_segments.name is 'Tên tập khách hàng';

comment on column admin_segments.description is 'Mô tả';

comment on column admin_segments.criteria is 'Lưu trữ điều kiện lọc khách hàng khi tạo segment';

comment on column admin_segments.created_at is 'Thời gian tạo';

comment on column admin_segments.updated_at is 'Thời gian cập nhật';

alter table admin_segments
    owner to root;

create table admin_audience_custom_fields
(
    id          bigserial
        primary key,
    field_name  varchar(255),
    field_value jsonb,
    field_type  varchar(20),
    created_at  bigint,
    updated_at  bigint,
    audience_id integer
);

comment on table admin_audience_custom_fields is 'Bảng danh sách trường tùy chỉnh của audience của admin';

comment on column admin_audience_custom_fields.field_name is 'Tên trường';

comment on column admin_audience_custom_fields.field_value is 'Giá trị';

comment on column admin_audience_custom_fields.field_type is 'Kiểu giá trị';

comment on column admin_audience_custom_fields.created_at is 'Thời gian tạo';

comment on column admin_audience_custom_fields.updated_at is 'Thời gian cập nhật';

alter table admin_audience_custom_fields
    owner to root;

create table field_type_configurations
(
    id                    serial
        primary key,
    field_type            varchar(50)                                                             not null
        unique,
    display_name          varchar(100)                                                            not null,
    description           text,
    validation_rules      jsonb,
    allowed_properties    jsonb,
    default_configuration jsonb,
    metadata              jsonb   default '{}'::jsonb,
    is_active             boolean default true,
    is_system             boolean default false,
    created_by            integer
        references employees,
    updated_by            integer
        references employees,
    created_at            bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at            bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table field_type_configurations is 'Cấu hình chi tiết cho từng loại trường';

alter table field_type_configurations
    owner to root;

create index idx_field_type_configurations_created_by
    on field_type_configurations (created_by);

create index idx_field_type_configurations_updated_by
    on field_type_configurations (updated_by);

create index idx_field_type_configurations_is_active
    on field_type_configurations (is_active);

create table admin_campaigns
(
    id           bigserial
        primary key,
    title        varchar(255),
    description  text,
    platform     varchar(255),
    content      text,
    server       jsonb,
    scheduled_at bigint,
    subject      varchar(255),
    created_at   bigint,
    status       varchar(20),
    employee_id  integer
        constraint user_campaigns_users_id_fk
            references employees,
    updated_at   bigint
);

comment on table admin_campaigns is 'Bảng chiến dịch của admin';

comment on column admin_campaigns.title is 'Tiêu đề';

comment on column admin_campaigns.description is 'Mô tả';

comment on column admin_campaigns.platform is 'Nền tảng';

comment on column admin_campaigns.content is 'Nội dung';

comment on column admin_campaigns.server is 'Thông tin máy chủ gửi';

comment on column admin_campaigns.scheduled_at is 'Thời gian dự kiến gửi chiến dịch';

comment on column admin_campaigns.subject is 'Nội dung tiêu đề với chiến dịch là email ';

comment on column admin_campaigns.created_at is 'Ngày tạo';

comment on column admin_campaigns.status is 'Trạng thái';

comment on column admin_campaigns.employee_id is 'Mã nhân viên';

comment on column admin_campaigns.updated_at is 'Ngày cập nhật';

alter table admin_campaigns
    owner to root;

create table admin_campaign_history
(
    id          bigserial
        primary key,
    campaign_id bigint
        constraint admin_campaign_history_admin_campaigns_id_fk
            references admin_campaigns,
    audience_id bigint
        constraint admin_campaign_history_admin_audience_id_fk
            references admin_audience,
    status      varchar(20),
    sent_at     bigint,
    created_at  bigint
);

comment on table admin_campaign_history is 'Bảng lịch sử chăm sóc khách hàng của admin';

comment on column admin_campaign_history.campaign_id is 'Mã chiến dịch';

comment on column admin_campaign_history.audience_id is 'Mã khách hàng';

comment on column admin_campaign_history.status is 'Trạng thái';

comment on column admin_campaign_history.sent_at is 'Thời gian gửi';

comment on column admin_campaign_history.created_at is 'Thời gian tạo';

alter table admin_campaign_history
    owner to root;

create table system_api_key
(
    id         serial
        constraint system_api_key_pk
            primary key,
    info       jsonb,
    provider   varchar(30),
    note       varchar(1000),
    created_at bigserial,
    updated_at bigserial,
    created_by integer
        constraint system_api_key_employees_id_fk
            references employees,
    updated_by integer
        constraint system_api_key_employees_id_fk_2
            references employees,
    type       varchar(30)
);

comment on table system_api_key is 'Lưu trữkey của hệ thống';

comment on column system_api_key.info is 'Thông tin cấu hình';

comment on column system_api_key.provider is 'nhà cung cấp';

comment on column system_api_key.note is 'ghi chú';

comment on column system_api_key.created_at is 'thời gian tạo';

comment on column system_api_key.updated_at is 'thời gian cập nhật';

comment on column system_api_key.created_by is 'nhân viên tạo ';

comment on column system_api_key.updated_by is 'nhân viên cập nhật';

comment on column system_api_key.type is 'loại sử dụng';

alter table system_api_key
    owner to root;

create table providers
(
    id                   serial
        primary key,
    name                 varchar(255)                                                            not null,
    type                 varchar(50)                                                             not null,
    provider_info        jsonb   default '{"website": null, "logo_url": null, "description": null}'::jsonb,
    required_credentials jsonb   default '[{"example": "abcd1234xyz", "data_type": "string", "field_name": "api_key", "description": "Khóa API được cấp bởi nhà cung cấp vận chuyển", "is_required": true, "placeholder": "Nhập API Key", "display_name": "API Key", "validation_rules": {"pattern": null, "max_length": 100, "min_length": 6}}, {"example": "s3cr3t@key!123", "data_type": "string", "field_name": "api_secret", "description": "Mã bí mật API được cấp bởi nhà cung cấp vận chuyển", "is_required": true, "placeholder": "Nhập API Secret", "display_name": "API Secret", "validation_rules": {"pattern": null, "max_length": 100, "min_length": 8}}]'::jsonb,
    is_active            boolean default true,
    created_by           integer
        references employees,
    update_by            integer
        references employees,
    created_at           bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at           bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table providers is 'Quản lý thông tin nhà cung cấp dịch vụ vận chuyển';

comment on column providers.name is 'Tên nhà cung cấp';

comment on column providers.provider_info is 'Thông tin chi tiết nhà cung cấp';

comment on column providers.required_credentials is 'Danh sách các thông tin xác thực cần thiết';

comment on column providers.is_active is 'Trạng thái hoạt động';

comment on column providers.created_by is 'Người tạo';

alter table providers
    owner to root;

create table llm_key
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    provider_id integer
        references providers,
    content     jsonb                                                                  not null,
    create_by   integer
        references employees,
    update_by   integer
        references employees,
    create_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table llm_key is 'Bảng lưu trữ khóa API cho các mô hình ngôn ngữ lớn (LLM)';

comment on column llm_key.id is 'ID duy nhất của khóa API, là UUID';

comment on column llm_key.provider_id is 'ID tham chiếu đến nhà cung cấp dịch vụ LLM';

comment on column llm_key.content is 'Nội dung chi tiết của khóa API, bao gồm khóa, cài đặt và giới hạn';

comment on column llm_key.create_by is 'ID nhân viên tạo khóa';

comment on column llm_key.update_by is 'ID nhân viên cập nhật khóa gần nhất';

comment on column llm_key.create_at is 'Thời điểm tạo khóa (milliseconds)';

comment on column llm_key.update_at is 'Thời điểm cập nhật khóa gần nhất (milliseconds)';

alter table llm_key
    owner to root;

create index idx_llm_key_provider_id
    on llm_key (provider_id);

create index idx_llm_key_create_by
    on llm_key (create_by);

create index idx_llm_key_update_by
    on llm_key (update_by);

create index idx_llm_key_is_active
    on llm_key ((content ->> 'is_active'::text));

create index idx_llm_key_api_version
    on llm_key ((content ->> 'api_version'::text));

create table vector_store_key
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    provider_id integer
        references providers,
    content     jsonb                                                                  not null,
    storage     bigint default 0                                                       not null,
    create_by   integer
        references employees,
    update_by   integer
        references employees,
    create_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table vector_store_key is 'Bảng lưu trữ khóa API cho các dịch vụ kho vector';

comment on column vector_store_key.id is 'ID duy nhất của khóa API, là UUID';

comment on column vector_store_key.provider_id is 'ID tham chiếu đến nhà cung cấp dịch vụ kho vector';

comment on column vector_store_key.content is 'Nội dung chi tiết của khóa API, bao gồm khóa và cài đặt';

comment on column vector_store_key.storage is 'Dung lượng lưu trữ đã sử dụng tính bằng bytes';

comment on column vector_store_key.create_by is 'ID nhân viên tạo khóa';

comment on column vector_store_key.update_by is 'ID nhân viên cập nhật khóa gần nhất';

comment on column vector_store_key.create_at is 'Thời điểm tạo khóa (milliseconds)';

comment on column vector_store_key.update_at is 'Thời điểm cập nhật khóa gần nhất (milliseconds)';

alter table vector_store_key
    owner to root;

create table users
(
    id                       serial
        constraint users_pk
            primary key,
    full_name                varchar(100),
    email                    varchar(100)
        constraint users_pk_2
            unique,
    phone_number             varchar(45)
        constraint users_pk_3
            unique,
    is_active                boolean   default true                    not null,
    is_verify_email          boolean   default false                   not null,
    created_at               bigint,
    updated_at               bigint,
    citizen_issue_place      varchar(100),
    citizen_issue_date       date,
    is_first_password_change boolean   default false                   not null,
    country                  integer,
    address                  varchar(1000),
    tax_code                 varchar(20),
    points_balance           bigint    default 0                       not null,
    type                     user_type default 'INDIVIDUAL'::user_type not null,
    platform                 varchar(20),
    citizen_id               varchar(20),
    avatar                   varchar(255),
    password                 varchar(1000),
    date_of_birth            date,
    gender                   gender,
    bank_code                varchar(20),
    account_number           varchar(50),
    account_holder           varchar(255),
    bank_branch              varchar(255),
    role                     varchar(20)                               not null,
    is_verify_phone          boolean,
    vector_store_key         uuid
        constraint users_vector_store_key_id_fk
            references vector_store_key,
    alert_threshold          bigint,
    was_rpoint_alerted       boolean
);

comment on table users is 'Bảng tài khoản của người dùng';

comment on column users.full_name is 'tên đầy đủ của người dùng';

comment on column users.email is 'email của người dùng';

comment on column users.phone_number is 'số điện thoại của người dùng';

comment on column users.is_active is 'trạng thái tài khoản';

comment on column users.is_verify_email is 'trạng thái xác thực';

comment on column users.created_at is 'thời gian tạo tài khoản';

comment on column users.updated_at is 'thời gian cập nhật thông tin';

comment on column users.citizen_issue_place is 'Nơi cấp chứng minh nhân dân';

comment on column users.citizen_issue_date is 'ngày cấp chứng minh nhân dân';

comment on column users.is_first_password_change is 'đánh dấu lần thay đổi mật khẩu đầu tiên';

comment on column users.country is 'đất nước';

comment on column users.address is 'địa chỉ';

comment on column users.tax_code is 'mã số thuế';

comment on column users.points_balance is 'Số dư points hiện tại';

comment on column users.type is 'loại tài khoản ''INDIVIDUAL'' hoặc''BUSINESS''';

comment on column users.platform is 'nền tảng đăng ký';

comment on column users.citizen_id is 'căn cước công dân';

comment on column users.avatar is 'avatar của người dùng';

comment on column users.password is 'mật khẩu đã được mã hóa của người dùng';

comment on column users.date_of_birth is 'Ngày sinh';

comment on column users.gender is 'Giới tính';

comment on column users.bank_code is 'mã code của ngân hàng';

comment on column users.account_number is 'Số tài khoản ngân hàng';

comment on column users.account_holder is 'tên tài khoản ngân hàng';

comment on column users.bank_branch is 'chi nhánh ngân hàng';

comment on column users.role is 'vai trò';

comment on column users.is_verify_phone is 'xác thực số điện thoại';

comment on column users.vector_store_key is 'key dùng cho tạo vector store';

comment on column users.alert_threshold is 'mức rpoint cảnh báo';

comment on column users.was_rpoint_alerted is 'đã cảnh báo rpoint hay chưa';

alter table users
    owner to root;

create table business_info
(
    id                                serial
        constraint business_info_pk
            primary key,
    user_id                           integer      not null
        constraint business_info_pk_2
            unique
        constraint business_info_users_id_fk
            references users,
    business_name                     varchar(255) not null,
    business_email                    varchar(100) not null,
    business_phone                    varchar(20)  not null,
    business_registration_certificate varchar(255),
    tax_code                          varchar(20)  not null,
    created_at                        bigint       not null,
    updated_at                        bigint,
    representative_position           varchar(255),
    representative_name               varchar(100),
    status                            varchar(20)  not null
);

comment on column business_info.user_id is 'mã tài khoản';

comment on column business_info.business_name is 'tên công ty';

comment on column business_info.business_email is 'email công ty';

comment on column business_info.business_phone is 'số điện thoại công ty';

comment on column business_info.business_registration_certificate is 'url giấy phép kinh doanh';

comment on column business_info.tax_code is 'mã số thuế';

comment on column business_info.created_at is 'thời gian tạo';

comment on column business_info.updated_at is 'thời gian chỉnh sửa';

comment on column business_info.representative_position is 'vị trí người đại diện';

comment on column business_info.representative_name is 'họ tên người đại diện';

comment on column business_info.status is 'trạng thái xác thực';

alter table business_info
    owner to root;

create table two_factor_auth
(
    id                                integer default nextval('user_2fa_settings_id_seq'::regclass) not null
        constraint two_factor_auth_pk_2
            primary key,
    user_id                           integer
        constraint two_factor_auth_pk
            unique
        constraint two_factor_auth_users_id_fk
            references users,
    otp_sms_enabled                   boolean default false,
    otp_email_enabled                 boolean default false                                         not null,
    google_authenticator_enabled      boolean default false                                         not null,
    google_authenticator_secret       varchar(255),
    created_at                        bigint,
    updated_at                        bigint,
    is_google_authenticator_confirmed boolean default false                                         not null
);

comment on column two_factor_auth.user_id is 'mã tài khoản';

comment on column two_factor_auth.otp_sms_enabled is 'xác thực sms';

comment on column two_factor_auth.otp_email_enabled is 'xác thực email';

comment on column two_factor_auth.google_authenticator_enabled is 'xác thực google authenticator';

comment on column two_factor_auth.google_authenticator_secret is 'khóa bí mật google authenticator';

comment on column two_factor_auth.created_at is 'thời gian tạo';

comment on column two_factor_auth.updated_at is 'thời gian cập nhật';

comment on column two_factor_auth.is_google_authenticator_confirmed is 'confirm google authenticator';

alter table two_factor_auth
    owner to root;

create table device_info
(
    id               uuid         not null
        primary key,
    user_id          integer
        references users,
    fingerprint      varchar(255) not null,
    ip_address       varchar(45),
    user_agent       text,
    browser          varchar(255),
    operating_system varchar(255),
    is_trusted       boolean default false,
    last_login       bigint,
    created_at       bigint       not null,
    updated_at       bigint       not null
);

alter table device_info
    owner to root;

create table auth_verification_logs
(
    id            bigserial
        primary key,
    user_id       integer
        references users,
    auth_method   auth_method_enum not null,
    status        auth_status_enum not null,
    ip_address    varchar(45),
    user_agent    text,
    code_sent_at  bigint,
    verified_at   bigint,
    attempt_count integer default 0,
    created_at    bigint           not null
);

alter table auth_verification_logs
    owner to root;

create table point_purchase_transactions
(
    id             bigserial
        primary key,
    user_id        integer
        references users,
    amount         double precision not null,
    points_amount  integer          not null,
    point_name     varchar(255),
    point_id       integer,
    currency       varchar(10)        default 'VND'::character varying,
    status         transaction_status default 'PENDING'::transaction_status,
    payment_method varchar(50)      not null,
    reference_id   varchar(100),
    description    text,
    retry_count    integer            default 0,
    last_retry_at  bigint,
    created_at     bigint           not null,
    updated_at     bigint           not null,
    completed_at   bigint,
    balance_before integer          not null,
    balance_after  integer          not null,
    coupon_id      integer,
    coupon_amount  double precision
);

comment on column point_purchase_transactions.id is 'Định danh giao dịch mua điểm';

comment on column point_purchase_transactions.user_id is 'Định danh người dùng';

comment on column point_purchase_transactions.amount is 'Số tiền';

comment on column point_purchase_transactions.points_amount is 'Số lượng points mua';

comment on column point_purchase_transactions.point_name is 'Tên loại point (nếu có)';

comment on column point_purchase_transactions.point_id is 'ID loại point (nếu có)';

comment on column point_purchase_transactions.currency is 'Loại tiền tệ';

comment on column point_purchase_transactions.status is 'Trạng thái giao dịch (PENDING, CONFIRMED, FAILED, REFUNDED)';

comment on column point_purchase_transactions.payment_method is 'Phương thức thanh toán';

comment on column point_purchase_transactions.reference_id is 'Mã tham chiếu từ cổng thanh toán';

comment on column point_purchase_transactions.description is 'Mô tả giao dịch';

comment on column point_purchase_transactions.retry_count is 'Số lần thử lại thanh toán';

comment on column point_purchase_transactions.last_retry_at is 'Thời gian thử lại cuối cùng (Unix timestamp)';

comment on column point_purchase_transactions.created_at is 'Thời gian tạo giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.updated_at is 'Thời gian cập nhật giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.completed_at is 'Thời gian hoàn thành giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.balance_before is 'Số dư trước giao dịch';

comment on column point_purchase_transactions.balance_after is 'Số dư sau giao dịch';

comment on column point_purchase_transactions.coupon_amount is 'Tiền khuyến mãi, giảm giá';

alter table point_purchase_transactions
    owner to root;

create table affiliate_accounts
(
    user_id           integer                                                            not null
        unique
        references users,
    status            affiliate_account_status default 'STEP0'::affiliate_account_status not null,
    total_earned      numeric(15, 2)           default 0,
    total_paid_out    numeric(15, 2)           default 0,
    available_balance numeric(15, 2)           default 0,
    created_at        bigint                                                             not null,
    updated_at        bigint                                                             not null,
    id                serial
        constraint affiliate_accounts_pk
            primary key
);

comment on table affiliate_accounts is 'Thông tin tài khoản affiliate của người dùng';

comment on column affiliate_accounts.user_id is 'Liên kết đến người dùng tương ứng (users.id)';

comment on column affiliate_accounts.status is 'Trạng thái tài khoản affiliate theo từng bước quy trình duyệt/kích hoạt';

comment on column affiliate_accounts.total_earned is 'Tổng số tiền người dùng đã kiếm được từ affiliate';

comment on column affiliate_accounts.total_paid_out is 'Tổng số tiền đã được rút ra hoặc thanh toán cho người dùng';

comment on column affiliate_accounts.available_balance is 'Số dư hiện tại còn lại (total_earned - total_paid_out)';

comment on column affiliate_accounts.created_at is 'Thời gian tạo bản ghi (Unix timestamp)';

comment on column affiliate_accounts.updated_at is 'Thời gian cập nhật bản ghi gần nhất (Unix timestamp)';

alter table affiliate_accounts
    owner to root;

create table affiliate_contracts
(
    id               bigserial
        primary key,
    user_id          integer
        references users,
    contract_type    contract_type_enum not null,
    status           contract_status_enum default 'DRAFT'::contract_status_enum,
    terms_accepted   boolean              default false,
    document_path    varchar(255),
    sign_method      sign_method_enum,
    employeeid       integer,
    rejection_reason varchar(2000),
    created_at       bigint             not null,
    updated_at       bigint             not null,
    approved_at      bigint
);

alter table affiliate_contracts
    owner to root;

create table affiliate_clicks
(
    id                   bigserial
        primary key,
    affiliate_account_id integer
        references affiliate_accounts,
    referral_code        varchar(50) not null,
    ip_address           varchar(45),
    user_agent           text,
    referrer_url         text,
    landing_page         text,
    click_time           bigint      not null
);

comment on table affiliate_clicks is 'Lưu thông tin các lượt click từ affiliate links';

comment on column affiliate_clicks.id is 'Định danh duy nhất của lượt click (UUID)';

comment on column affiliate_clicks.affiliate_account_id is 'Tài khoản affiliate đã tạo lượt click (liên kết với bảng users)';

comment on column affiliate_clicks.referral_code is 'Mã giới thiệu của affiliate dùng trong lượt click';

comment on column affiliate_clicks.ip_address is 'Địa chỉ IP của người click';

comment on column affiliate_clicks.user_agent is 'Thông tin trình duyệt của người click';

comment on column affiliate_clicks.referrer_url is 'Trang giới thiệu dẫn tới lượt click';

comment on column affiliate_clicks.landing_page is 'Trang đích sau khi click';

comment on column affiliate_clicks.click_time is 'Thời điểm diễn ra lượt click (Unix timestamp)';

alter table affiliate_clicks
    owner to root;

create table invoice
(
    id               bigserial
        constraint invoice_pk
            primary key,
    order_id         bigserial
        constraint invoice_point_purchase_transactions_id_fk
            references point_purchase_transactions,
    invoice_path_pdf varchar(255),
    inv_pattern      varchar(20),
    inv_serial       varchar(20),
    created_at       bigint,
    updated_at       bigint,
    buyer_full_name  varchar(255),
    company_name     varchar(255),
    tax_code         varchar(20),
    address          varchar(500),
    payment_method   varchar(50),
    currency         varchar(10),
    account_number   varchar(50),
    bank_code        varchar(20),
    exchange_rate    double precision,
    status           varchar(20),
    vat_amount       double precision,
    item_name        varchar(255),
    unit_of_measure  integer,
    quantity         integer,
    unit_price       double precision,
    amount           double precision,
    vat_rate         double precision,
    total_amount     double precision
);

comment on table invoice is 'hóa đơn mua point';

comment on column invoice.order_id is 'mã đơn hàng';

comment on column invoice.invoice_path_pdf is 'Đường link PDF của hóa đơn';

comment on column invoice.inv_pattern is 'Mẫu số hóa đơn';

comment on column invoice.inv_serial is 'Mẫu số ký hiệu';

comment on column invoice.created_at is 'Thời điểm tạo hóa đơn (Unix timestamp)';

comment on column invoice.updated_at is 'Thời điểm cập nhật gần nhất (Unix timestamp)';

comment on column invoice.buyer_full_name is 'Họ tên người mua hàng';

comment on column invoice.company_name is 'Tên đơn vị (doanh nghiệp)';

comment on column invoice.tax_code is 'Mã số thuế của đơn vị mua hàng';

comment on column invoice.address is 'Địa chỉ người mua';

comment on column invoice.payment_method is 'Hình thức thanh toán (tiền mặt, chuyển khoản,...)';

comment on column invoice.currency is 'Đơn vị tiền tệ sử dụng trong hóa đơn (VD: VND, USD)';

comment on column invoice.account_number is 'Tài khoản ngân hàng của người mua (nếu có)';

comment on column invoice.bank_code is 'mã bank code';

comment on column invoice.exchange_rate is 'Tỷ giá nếu dùng đơn vị tiền tệ khác VND';

comment on column invoice.status is 'Trạng thái hóa đơn: PENDING (chưa xuất), ISSUED (đã xuất)';

comment on column invoice.vat_amount is 'Số tiền thuế GTGT phải nộp';

comment on column invoice.item_name is 'Tên hàng hóa hoặc dịch vụ';

comment on column invoice.unit_of_measure is 'Đơn vị tính (VD: cái, kg, giờ...)';

comment on column invoice.quantity is 'Số lượng hàng hóa/dịch vụ';

comment on column invoice.unit_price is 'Đơn giá của từng đơn vị sản phẩm';

comment on column invoice.amount is 'Thành tiền (subtotal: quantity * unit_price)';

comment on column invoice.vat_rate is 'Thuế suất giá trị gia tăng (GTGT) theo %';

comment on column invoice.total_amount is 'Tổng tiền thanh toán (đã bao gồm VAT)';

alter table invoice
    owner to root;

create table affiliate_customer_order
(
    order_id             bigint not null
        constraint affiliate_customer_order_pk
            primary key,
    commission           double precision,
    affiliate_account_id integer
        constraint affiliate_customer_order_affiliate_accounts_id_fk
            references affiliate_accounts,
    rank_id              integer
);

comment on table affiliate_customer_order is 'Đơn hàng affiliate';

comment on column affiliate_customer_order.order_id is 'Mã đơn hàng';

comment on column affiliate_customer_order.commission is 'Phần trăm hoa hồng';

comment on column affiliate_customer_order.affiliate_account_id is 'tài khoản affiliate';

comment on column affiliate_customer_order.rank_id is 'Mã rank affiliate không cần nối khóa phụ';

alter table affiliate_customer_order
    owner to root;

create table subscriptions
(
    id              bigserial
        primary key,
    user_id         serial
        references users,
    plan_pricing_id serial
        references plan_pricing,
    start_date      bigint                                                    not null,
    end_date        bigint                                                    not null,
    auto_renew      boolean             default true,
    status          subscription_status default 'ACTIVE'::subscription_status not null,
    created_at      bigint                                                    not null,
    updated_at      bigint                                                    not null,
    usage_limit     bigint,
    current_usage   bigint,
    remaining_value bigint,
    usage_unit      varchar(50),
    constraint subscriptions_pk
        unique (plan_pricing_id, user_id)
);

comment on table subscriptions is 'Bảng quản lý gói dịch vụ user đăng ký';

comment on column subscriptions.id is 'Định danh Subscription (UUID)';

comment on column subscriptions.user_id is 'Người dùng sở hữu subscription';

comment on column subscriptions.plan_pricing_id is 'Tùy chọn giá/chu kỳ thanh toán cụ thể mà user đăng ký (plan_pricing.id)';

comment on column subscriptions.start_date is 'Ngày bắt đầu subscription (Unix timestamp)';

comment on column subscriptions.end_date is 'Ngày hết hạn subscription (Unix timestamp)';

comment on column subscriptions.auto_renew is 'Có tự động gia hạn không (TRUE/FALSE)';

comment on column subscriptions.status is 'Trạng thái subscription (ACTIVE, CANCELLED,...)';

comment on column subscriptions.created_at is 'Thời điểm tạo';

comment on column subscriptions.updated_at is 'Thời điểm cập nhật';

comment on column subscriptions.usage_limit is 'Tổng số lượng tài nguyên được phép sử dụng';

comment on column subscriptions.current_usage is 'Số lượng tài nguyên đã sử dụng';

comment on column subscriptions.remaining_value is 'Số lượng tài nguyên còn lại';

comment on column subscriptions.usage_unit is 'Đơn vị của tài nguyên';

alter table subscriptions
    owner to root;

create table usage_logs
(
    id              bigserial
        primary key,
    subscription_id bigserial
        references subscriptions,
    feature         varchar(100) not null,
    amount          numeric      not null,
    usage_time      bigint       not null,
    created_at      bigint       not null
);

comment on table usage_logs is 'Ghi nhận mức sử dụng user, phục vụ tính phí usage-based';

comment on column usage_logs.subscription_id is 'Subscription mà usage thuộc về';

comment on column usage_logs.feature is 'Tên tính năng, map với plan_features.feature';

comment on column usage_logs.amount is 'Số lượng usage (GB, request,...)';

comment on column usage_logs.usage_time is 'Thời điểm usage phát sinh (Unix timestamp)';

comment on column usage_logs.created_at is 'Thời điểm ghi log (Unix timestamp)';

alter table usage_logs
    owner to root;

create table user_has_roles
(
    user_id integer not null
        constraint user_has_roles_users_id_fk
            references users,
    role_id integer not null
        constraint user_has_roles_user_roles_id_fk
            references user_roles,
    constraint user_has_roles_pk
        primary key (user_id, role_id)
);

alter table user_has_roles
    owner to root;

create table affiliate_point_conversion_history
(
    id                   serial
        constraint affiliate_point_conversion_history_pk
            primary key,
    points_converted     bigint,
    affiliate_account_id integer
        constraint affiliate_point_conversion_history___fk_affiliate_account
            references affiliate_accounts,
    conversion_rate      double precision,
    amount               double precision,
    created_at           bigint,
    status               point_conversion_history_status
);

comment on table affiliate_point_conversion_history is 'Bảng lịch sử đổi tiền affiliate sang point hệ thống';

comment on column affiliate_point_conversion_history.points_converted is 'số point đổi được';

comment on column affiliate_point_conversion_history.affiliate_account_id is 'mã tài khoản affiliate_accounts';

comment on column affiliate_point_conversion_history.conversion_rate is 'tỷ lệ chuyển đổi';

comment on column affiliate_point_conversion_history.amount is 'số tiền rút';

comment on column affiliate_point_conversion_history.created_at is 'thời giam tạo';

comment on column affiliate_point_conversion_history.status is 'trạng thái ''SUCCESS'', ''PENDING'', ''FAILED''';

alter table affiliate_point_conversion_history
    owner to root;

create table user_company_in_sepay
(
    id         serial
        constraint user_company_in_sepay_pk
            primary key,
    company_id varchar(20),
    user_id    integer
        constraint user_company_in_sepay_pk_2
            unique
        constraint user_company_in_sepay_users_id_fk
            references users,
    created_at bigint,
    updated_at bigint
);

comment on table user_company_in_sepay is 'bảng này là đại diện company trên sepay-hub đối với mỗi người dùng';

comment on column user_company_in_sepay.company_id is 'mã công ty trên sepay-hub';

comment on column user_company_in_sepay.user_id is 'mã người dùng';

comment on column user_company_in_sepay.created_at is 'thời gian tạo';

comment on column user_company_in_sepay.updated_at is 'thời gian cập nhật';

alter table user_company_in_sepay
    owner to root;

create table payment_gateway
(
    account_id            varchar(30) not null,
    company_id            integer
        constraint electronic_payment_gateway_user_user_id_fk
            references user_company_in_sepay,
    bank_code             varchar(20),
    account_number        varchar(30),
    identification_number varchar(30),
    phone_number          varchar(20),
    label                 varchar(50),
    status                varchar(30),
    request_id            varchar(100),
    account_holder_name   varchar(50),
    merchant_address      varchar(1000),
    merchant_name         varchar(500),
    id                    serial
        constraint electronic_payment_gateway_pk
            primary key,
    is_va                 boolean,
    main_id               integer,
    va_id                 varchar(30),
    can_create_va         boolean default false
);

comment on table payment_gateway is 'tài khoản ngân hàng liên kết';

comment on column payment_gateway.account_id is 'mã tài khoản ngân hàng trên sepay';

comment on column payment_gateway.bank_code is 'mã ngân hàng';

comment on column payment_gateway.account_number is 'số tài khoản';

comment on column payment_gateway.identification_number is 'căn cước công dân';

comment on column payment_gateway.phone_number is 'số điện thoại đăng ký tài khoản ngân hàng';

comment on column payment_gateway.label is 'nhãn';

comment on column payment_gateway.status is 'trạng thái';

comment on column payment_gateway.request_id is 'mã liên kết';

comment on column payment_gateway.account_holder_name is 'họ tên tài khoản ngân hàng';

comment on column payment_gateway.merchant_address is 'nơi điểm bán';

comment on column payment_gateway.merchant_name is 'tên điểm bán';

comment on column payment_gateway.is_va is 'có phải tài khoản VA hay không';

comment on column payment_gateway.main_id is 'nếu là tài khoản VA thì tài khoản gốc là mã này';

comment on column payment_gateway.va_id is 'mã va';

comment on column payment_gateway.can_create_va is 'tài khoản này có tạo được tài khoản VA hay không';

alter table payment_gateway
    owner to root;

create table email_server_configurations
(
    id                  serial
        constraint email_server_configurations_pk
            primary key,
    user_id             integer
        constraint email_server_configurations_users_id_fk
            references users,
    server_name         varchar(100),
    host                varchar(255),
    port                integer,
    username            varchar(255),
    password            varchar(255),
    use_ssl             boolean,
    additional_settings json,
    created_at          bigint,
    updated_at          bigint
);

comment on table email_server_configurations is 'Lưu trữ thông tin cấu hình cụ thể cho máy chủ Email (SMTP server).';

comment on column email_server_configurations.user_id is 'Mã người dùng';

comment on column email_server_configurations.server_name is 'Tên hiển thị của cấu hình, ví dụ: “Mailgun Server #1” hoặc “AWS SES”';

comment on column email_server_configurations.host is 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…';

comment on column email_server_configurations.port is 'Cổng SMTP, ví dụ: 465, 587, …';

comment on column email_server_configurations.username is 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng).';

comment on column email_server_configurations.password is 'Mật khẩu hoặc token xác thực cho SMTP.';

comment on column email_server_configurations.use_ssl is 'Xác định có sử dụng SSL/TLS hay không';

comment on column email_server_configurations.additional_settings is 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.';

alter table email_server_configurations
    owner to root;

create table sms_server_configurations
(
    id                  serial
        constraint sms_server_configurations_pk
            primary key,
    user_id             integer
        constraint sms_server_configurations_users_id_fk
            references users,
    provider_name       varchar(100),
    created_at          bigint,
    updated_at          bigint,
    api_key             varchar(255),
    endpoint            varchar(255),
    additional_settings json
);

comment on table sms_server_configurations is 'Lưu trữ thông tin cấu hình cho máy chủ SMS. Thông thường, các nhà cung cấp SMS (Twilio, Vonage, v.v.) cung cấp một API key/token thay vì yêu cầu host/port như SMTP.';

comment on column sms_server_configurations.provider_name is 'Tên nhà cung cấp SMS, ví dụ: “Twilio”, “Vonage”, “Nexmo”, …';

comment on column sms_server_configurations.api_key is 'Khoá (key) hoặc token của nhà cung cấp.';

comment on column sms_server_configurations.endpoint is 'URL endpoint để gọi API (có thể NULL nếu nhà cung cấp đã cố định).';

comment on column sms_server_configurations.additional_settings is 'Lưu các cấu hình tùy biến, ví dụ: “Message Service SID”, “Short Code”, “Alphanumeric Sender ID”, v.v.';

alter table sms_server_configurations
    owner to root;

create table carts
(
    id         integer default nextval('cards_id_seq'::regclass) not null
        constraint cards_pk
            primary key,
    user_id    integer
        constraint cards_pk_2
            unique
        constraint cards_users_id_fk
            references users,
    created_at bigint,
    updated_at bigint
);

comment on table carts is 'Giỏ hàng';

comment on column carts.user_id is 'Mã người dùng';

comment on column carts.created_at is 'Thời gian tạo';

comment on column carts.updated_at is 'Thời gian cập nhật';

alter table carts
    owner to root;

create table cart_items
(
    id         serial
        constraint cart_items_pk
            primary key,
    cart_id    integer
        constraint cart_items_cards_id_fk
            references carts,
    product_id integer
        constraint cart_items_products_id_fk
            references products,
    quantity   integer
);

comment on table cart_items is 'Danh sách các sản phẩm trong giỏ hàng';

comment on column cart_items.cart_id is 'Mã giỏ hàng';

comment on column cart_items.product_id is 'Mã sản phẩm';

comment on column cart_items.quantity is 'Số lượng';

alter table cart_items
    owner to root;

create table market_order
(
    id          bigserial
        constraint market_order_pk
            primary key,
    user_id     integer
        constraint market_order_users_id_fk
            references users,
    total_point bigint,
    created_at  bigint,
    updated_at  bigint
);

comment on column market_order.user_id is 'Mã người dùng';

comment on column market_order.total_point is 'Tổng số R-Point thanh toán';

comment on column market_order.created_at is 'Thời giantạo';

comment on column market_order.updated_at is 'Thời gian cập nhật';

alter table market_order
    owner to root;

create table user_manage_notification
(
    id                            serial
        constraint user_manage_notification_pk
            primary key,
    user_id                       integer
        constraint user_manage_notification_pk_2
            unique
        constraint user_manage_notification_users_id_fk
            references users,
    receive_account_system_emails boolean default true not null,
    receive_billing_emails        boolean default true not null,
    receive_new_feature_emails    boolean default true not null,
    receive_affiliate_emails      boolean default true not null,
    receive_documentation_emails  boolean,
    receive_promotional_emails    boolean default true
);

comment on table user_manage_notification is 'Bảng tùy chỉnh nhận email thông báo của người dùng';

alter table user_manage_notification
    owner to root;

create table rule_contract
(
    id                 serial
        constraint rule_contract_pk
            primary key,
    user_id            integer
        constraint rule_contract_users_id_fk
            references users,
    status             contract_status_enum not null,
    type               contract_type_enum,
    contract_url_pdf   varchar(255),
    created_at         bigint,
    user_signature_at  bigint,
    admin_signature_at bigint,
    reject_reason      text
);

comment on table rule_contract is 'Hợp đồng nguyên tắc';

comment on column rule_contract.user_id is 'Mã người dùng';

comment on column rule_contract.status is 'Trạng thái hợp đồng';

comment on column rule_contract.type is 'Loại hợp đồng';

comment on column rule_contract.contract_url_pdf is 'Đường dẫn hợp đồng';

comment on column rule_contract.created_at is 'Thời gian tạo hợp đồng';

comment on column rule_contract.user_signature_at is 'Thời gian đối tác ký hợp đồng';

comment on column rule_contract.admin_signature_at is 'Thời gian quản trị ký hợp đồng';

comment on column rule_contract.reject_reason is 'Lý do từ chối hợp đồng';

alter table rule_contract
    owner to root;

create table blogs
(
    id                 serial
        constraint blogs_pk
            primary key,
    title              varchar(500),
    content            varchar(255),
    point              bigint,
    view_count         bigint,
    thumbnail_url      varchar(255),
    tags               jsonb,
    created_at         bigint,
    updated_at         bigint,
    user_id            integer
        constraint blogs_users_id_fk
            references users,
    employee_id        integer
        constraint blogs_employees_id_fk
            references employees,
    employee_moderator integer
        constraint blogs_employees_id_fk_2
            references employees,
    author_type        varchar(10),
    status             varchar(20)          not null,
    enable             boolean default true not null,
    "like"             bigint
);

comment on table blogs is 'Bảng danh sách bài viết';

comment on column blogs.title is 'Tiêu đề vài viết';

comment on column blogs.content is 'Đường link của file content trên CDN';

comment on column blogs.view_count is 'Số lượt xem';

comment on column blogs.thumbnail_url is 'Ảnh tiêu đề';

comment on column blogs.tags is 'Nhãn';

comment on column blogs.created_at is 'Thời gian tạo';

comment on column blogs.updated_at is 'Thời gian cập nhật';

comment on column blogs.user_id is 'Id của tác giả nếu đây là bài viết của người dùng';

comment on column blogs.employee_id is 'Mã của nhân viên nếu đây là bài viết của hệ thống';

comment on column blogs.employee_moderator is 'nhân viên kiểm duyệt bài viết nếu đây là bài viết của người dùng, nếu là bài viết của hệ thống thì sẽ là null';

comment on column blogs.author_type is 'Loại của tác bài viết ''USER'' hoặc ''SYSTEM''';

comment on column blogs.status is 'Trạng thái của bài viết DRAFT, PENDING, APPROVED';

comment on column blogs.enable is 'Trạng thái của bài viết';

comment on column blogs."like" is 'số lượt like';

alter table blogs
    owner to root;

create table blog_purchases
(
    id                   serial
        constraint blog_purchases_pk
            primary key,
    user_id              integer
        constraint blog_purchases_users_id_fk
            references users,
    blog_id              integer
        constraint blog_purchases_blogs_id_fk
            references blogs,
    point                bigint,
    purchased_at         bigint,
    platform_fee_percent double precision,
    seller_receive_price bigint
);

comment on table blog_purchases is 'Bảng lưu thông tin mua blog';

comment on column blog_purchases.user_id is 'Mã người mua';

comment on column blog_purchases.blog_id is 'Mã bài viết';

comment on column blog_purchases.point is 'Số point thời điểm mua';

comment on column blog_purchases.purchased_at is 'Thời gian mua hàng';

comment on column blog_purchases.platform_fee_percent is 'Phần trăm phí sàn';

comment on column blog_purchases.seller_receive_price is 'Giá người bán nhận được sau khi trừ phí sàn';

alter table blog_purchases
    owner to root;

create table blog_comments
(
    id                bigserial
        constraint blog_comments_pk
            primary key,
    blog_id           integer
        constraint blog_comments_blogs_id_fk
            references blogs,
    user_id           integer
        constraint blog_comments_users_id_fk
            references users,
    created_at        bigint,
    content           varchar(1000),
    author_type       varchar(10),
    employee_id       integer
        constraint blog_comments_employees_id_fk
            references employees,
    parent_comment_id bigint
        constraint blog_comments_blog_comments_id_fk
            references blog_comments
);

comment on column blog_comments.blog_id is 'Mã bài viết';

comment on column blog_comments.user_id is 'Mã người dùng nhắn tin';

comment on column blog_comments.created_at is 'Thời gian tạo tin nhắn';

comment on column blog_comments.content is 'Nội dung tin nhắn';

comment on column blog_comments.author_type is 'Loại tài khoản nhắn tin ''USER'' hoặc ''SYSTEM''';

comment on column blog_comments.employee_id is 'Mã nhân viên';

comment on column blog_comments.parent_comment_id is 'Mã của bình luận cha nếu có';

alter table blog_comments
    owner to root;

create table user_template_email
(
    id           bigserial
        constraint user_template_email_pk
            primary key,
    content      text,
    tags         jsonb,
    name         varchar(255),
    subject      varchar(255),
    created_at   bigint,
    user_id      integer
        constraint user_template_email_users_id_fk
            references users,
    updated_at   bigint,
    placeholders json
);

comment on table user_template_email is 'Bảng template email của người dùng';

comment on column user_template_email.content is 'Nội dung email';

comment on column user_template_email.tags is 'Nhãn cho email';

comment on column user_template_email.name is 'Tên email';

comment on column user_template_email.subject is 'Tiêu đề email';

comment on column user_template_email.created_at is 'Thời gian tạo';

comment on column user_template_email.updated_at is 'Thời gian cập nhật';

comment on column user_template_email.placeholders is 'Biến truyền vào';

alter table user_template_email
    owner to root;

create table user_audience
(
    id         bigserial
        constraint user_audience_pk
            primary key,
    email      varchar(255),
    phone      varchar(20),
    created_at bigint not null,
    updated_at bigint,
    user_id    integer
        constraint user_audience_users_id_fk
            references users
);

comment on table user_audience is 'Bảng khách hàng của người dùng';

comment on column user_audience.email is 'Email người dùng';

comment on column user_audience.phone is 'Số điện thoại';

comment on column user_audience.created_at is 'Ngày tạo';

comment on column user_audience.updated_at is 'Ngày cập nhật';

comment on column user_audience.user_id is 'Mã khách hàng';

alter table user_audience
    owner to root;

create table user_audience_custom_fields
(
    id          bigint default nextval('audience_custom_fields_id_seq'::regclass) not null
        constraint user_audience_custom_fields_pk
            primary key,
    field_name  varchar(255),
    field_value jsonb,
    field_type  varchar(20),
    created_at  bigint,
    updated_at  bigint,
    audience_id integer
        constraint user_audience_custom_fields_user_audience_id_fk
            references user_audience
);

comment on table user_audience_custom_fields is 'Bảng danh sách trường tùy chỉnh của audience';

comment on column user_audience_custom_fields.field_name is 'Tên trường';

comment on column user_audience_custom_fields.field_value is 'Giá trị';

comment on column user_audience_custom_fields.field_type is 'Kiểu giá trị';

comment on column user_audience_custom_fields.created_at is 'Thời gian tạo';

comment on column user_audience_custom_fields.updated_at is 'Thời gian cập nhật';

alter table user_audience_custom_fields
    owner to root;

create table user_tags
(
    id         bigserial
        constraint user_tags_pk
            primary key,
    user_id    integer
        constraint user_tags_users_id_fk
            references users,
    name       varchar(255),
    color      varchar(7),
    created_at bigint,
    updated_at bigint
);

alter table user_tags
    owner to root;

create table user_audience_has_tags
(
    audience_id bigint not null
        constraint user_audience_has_tags_user_audience_id_fk
            references user_audience,
    tag_id      bigint not null
        constraint user_audience_has_tags_user_tags_id_fk
            references user_tags,
    constraint user_audience_has_tags_pk
        primary key (audience_id, tag_id)
);

alter table user_audience_has_tags
    owner to root;

create table user_segments
(
    id          bigserial
        constraint user_segments_pk
            primary key,
    user_id     integer
        constraint user_segments_users_id_fk
            references users,
    name        varchar(255),
    description text,
    criteria    jsonb,
    created_at  bigint,
    updated_at  bigint
);

comment on table user_segments is 'Phân khúc khách hàng của người dùng';

comment on column user_segments.user_id is 'Mã khách hàng';

comment on column user_segments.name is 'Tên tập khách hàng';

comment on column user_segments.description is 'Mô tả';

comment on column user_segments.criteria is 'Lưu trữ điều kiện lọc khách hàng khi tạo segment';

comment on column user_segments.created_at is 'Thời gian tạo';

comment on column user_segments.updated_at is 'Thời gian cập nhật';

alter table user_segments
    owner to root;

create table user_campaigns
(
    id           bigserial
        constraint user_campaigns_pk
            primary key,
    title        varchar(255),
    description  text,
    platform     varchar(255),
    content      text,
    server       jsonb,
    scheduled_at bigint,
    subject      varchar(255),
    created_at   bigint,
    status       varchar(20),
    user_id      integer
        constraint user_campaigns_users_id_fk
            references users,
    updated_at   bigint
);

comment on table user_campaigns is 'Bảng chiến dịch của người dùng';

comment on column user_campaigns.title is 'Tiêu đề';

comment on column user_campaigns.description is 'Mô tả';

comment on column user_campaigns.platform is 'Nền tảng';

comment on column user_campaigns.content is 'Nội dung';

comment on column user_campaigns.server is 'Thông tin máy chủ gửi';

comment on column user_campaigns.scheduled_at is 'Thời gian dự kiến gửi chiến dịch';

comment on column user_campaigns.subject is 'Nội dung tiêu đề với chiến dịch là email ';

comment on column user_campaigns.created_at is 'Ngày tạo';

comment on column user_campaigns.status is 'Trạng thái';

comment on column user_campaigns.user_id is 'Mã người dùng';

comment on column user_campaigns.updated_at is 'Ngày cập nhật';

alter table user_campaigns
    owner to root;

create table user_campaign_history
(
    id          bigint default nextval('campaign_history_id_seq'::regclass) not null
        constraint user_campaign_history_pk
            primary key,
    campaign_id bigint
        constraint user_campaign_history_user_campaigns_id_fk
            references user_campaigns,
    audience_id bigint
        constraint user_campaign_history_user_audience_id_fk
            references user_audience,
    status      varchar(20),
    sent_at     bigint,
    created_at  bigint
);

comment on table user_campaign_history is 'Bảng lịch sử chăm sóc khách hàng';

comment on column user_campaign_history.campaign_id is 'Mã chiến dịch';

comment on column user_campaign_history.audience_id is 'Mã khách hàng';

comment on column user_campaign_history.status is 'Trạng thái';

comment on column user_campaign_history.sent_at is 'Thời gian gửi';

comment on column user_campaign_history.created_at is 'Thời gian tạo';

alter table user_campaign_history
    owner to root;

create table user_memory
(
    id         uuid   default uuid_generate_v4()                                      not null
        primary key,
    user_id    integer                                                                not null
        constraint fk_user
            references users
            on delete cascade,
    fact       text                                                                   not null,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    embedding  vector(1536)
);

comment on table user_memory is 'Lưu trữ thông tin ghi nhớ (facts) của người dùng với vector embedding để tìm kiếm ngữ nghĩa';

comment on column user_memory.id is 'ID định danh duy nhất cho mỗi ghi nhớ';

comment on column user_memory.user_id is 'ID của người dùng sở hữu ghi nhớ này';

comment on column user_memory.fact is 'Nội dung thông tin ghi nhớ';

comment on column user_memory.created_at is 'Thời điểm ghi nhớ được tạo (epoch milliseconds)';

comment on column user_memory.embedding is 'Vector embedding của nội dung ghi nhớ để hỗ trợ tìm kiếm ngữ nghĩa';

alter table user_memory
    owner to root;

create index idx_user_memory_user_id
    on user_memory (user_id);

create index idx_user_memory_created_at
    on user_memory (created_at);

create index idx_user_memory_embedding
    on user_memory using ivfflat (embedding vector_cosine_ops);

create table user_product_field_templates
(
    id         serial
        primary key,
    name       varchar(255)                                                            not null,
    fields     jsonb                                                                   not null,
    is_active  boolean default true,
    category   varchar(50),
    is_default boolean default false,
    created_by integer                                                                 not null
        references users,
    created_at bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_product_field_templates is 'Template trường tùy chỉnh cho sản phẩm';

comment on column user_product_field_templates.fields is 'Các trường tùy chỉnh (dạng JSON)';

comment on column user_product_field_templates.is_active is 'Trạng thái kích hoạt';

comment on column user_product_field_templates.category is 'Danh mục template';

comment on column user_product_field_templates.is_default is 'Template mặc định';

comment on column user_product_field_templates.created_by is 'Người tạo template';

alter table user_product_field_templates
    owner to root;

create index idx_user_product_templates_created_by
    on user_product_field_templates (created_by);

create index idx_user_product_templates_is_active
    on user_product_field_templates (is_active);

create table user_classification_field_templates
(
    id         serial
        primary key,
    name       varchar(255)                                                            not null,
    fields     jsonb                                                                   not null,
    is_active  boolean default true,
    category   varchar(50),
    is_default boolean default false,
    created_by integer                                                                 not null
        references users,
    created_at bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_classification_field_templates is 'Template trường tùy chỉnh cho phân loại sản phẩm';

alter table user_classification_field_templates
    owner to root;

create index idx_user_classification_templates_created_by
    on user_classification_field_templates (created_by);

create index idx_user_classification_templates_is_active
    on user_classification_field_templates (is_active);

create table user_products
(
    id                      bigint  default nextval('user_products_id_seq'::regclass)               not null
        primary key,
    name                    varchar(255)                                                            not null,
    name_embedding          vector(1536),
    price                   jsonb                                                                   not null,
    type_price              varchar(50)                                                             not null,
    description             text,
    description_embedding   vector(1536),
    image_url               varchar(512),
    tags                    jsonb,
    tags_embedding          vector(1536),
    custom_fields           jsonb,
    custom_fields_embedding vector(1536),
    template_id             integer
        references user_product_field_templates,
    created_by              integer
        references users,
    created_at              bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at              bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_shipping             boolean default false                                                   not null,
    shipping_config         jsonb
);

comment on table user_products is 'Bảng quản lý sản phẩm của người dùng';

comment on column user_products.name is 'Tên sản phẩm';

comment on column user_products.name_embedding is 'Vector nhúng từ tên sản phẩm';

comment on column user_products.price is 'Giá sản phẩm (dưới dạng JSON)';

comment on column user_products.type_price is 'Kiểu giá (ví dụ: cố định, theo giờ...)';

comment on column user_products.description is 'Mô tả sản phẩm';

comment on column user_products.description_embedding is 'Vector nhúng mô tả';

comment on column user_products.image_url is 'URL hình ảnh sản phẩm';

comment on column user_products.tags is 'Các tag mô tả sản phẩm';

comment on column user_products.tags_embedding is 'Vector nhúng từ tag';

comment on column user_products.custom_fields is 'Trường tùy chỉnh sản phẩm (JSON)';

comment on column user_products.custom_fields_embedding is 'Vector nhúng trường tùy chỉnh';

comment on column user_products.template_id is 'ID template trường tùy chỉnh';

comment on column user_products.created_by is 'Người tạo sản phẩm';

comment on column user_products.created_at is 'Thời gian tạo (millis)';

comment on column user_products.updated_at is 'Thời gian cập nhật (millis)';

comment on column user_products.is_shipping is 'Sản phẩm có vận chuyển không';

comment on column user_products.shipping_config is 'Cấu hình vận chuyển;';

alter table user_products
    owner to root;

create index idx_user_products_template_id
    on user_products (template_id);

create index idx_user_products_created_by
    on user_products (created_by);

create table user_classifications
(
    id                     serial
        primary key,
    product_id             integer                                                                not null
        references user_products
            on delete cascade,
    type                   varchar(255)                                                           not null,
    custom_fields          jsonb,
    template_id            integer
        references user_classification_field_templates,
    type_embedding         vector(1536),
    custom_field_embedding vector(1536),
    created_at             bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at             bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_classifications is 'Bảng phân loại sản phẩm người dùng';

comment on column user_classifications.type is 'Tên phân loại';

comment on column user_classifications.custom_fields is 'Trường tùy chỉnh của phân loại';

comment on column user_classifications.template_id is 'Template trường phân loại';

comment on column user_classifications.type_embedding is 'Vector nhúng phân loại';

comment on column user_classifications.custom_field_embedding is 'Vector nhúng trường tùy chỉnh';

alter table user_classifications
    owner to root;

create index idx_user_classifications_product_id
    on user_classifications (product_id);

create index idx_user_classifications_template_id
    on user_classifications (template_id);

create table user_predefined_fields
(
    id            serial
        primary key,
    name          varchar(100)                                                            not null,
    label         varchar(100)                                                            not null,
    field_type    varchar(50)                                                             not null
        constraint fk_field_type
            references field_type_configurations (field_type),
    category      varchar(50),
    configuration jsonb                                                                   not null,
    description   text,
    is_active     boolean default true,
    created_by    integer
        references users,
    created_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_predefined_fields is 'Trường được định nghĩa sẵn có thể dùng trong template';

alter table user_predefined_fields
    owner to root;

create index idx_user_predefined_fields_field_type
    on user_predefined_fields (field_type);

create index idx_user_predefined_fields_created_by
    on user_predefined_fields (created_by);

create index idx_user_predefined_fields_is_active
    on user_predefined_fields (is_active);

create table user_keys
(
    id          integer default nextval('user_configurations_id_seq'::regclass)         not null
        constraint user_configurations_pkey
            primary key,
    user_id     integer                                                                 not null
        constraint user_configurations_user_id_fkey
            references users
            on delete cascade,
    provider_id integer                                                                 not null
        constraint user_configurations_provider_id_fkey
            references providers,
    credentials jsonb   default '{"api_key": null, "api_secret": null}'::jsonb,
    settings    jsonb   default '{"is_active": true, "is_default": false}'::jsonb,
    created_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    constraint user_configurations_user_id_provider_id_key
        unique (user_id, provider_id)
);

comment on table user_keys is 'Cấu hình của người dùng cho nhà cung cấp vận chuyển';

comment on column user_keys.user_id is 'ID người dùng';

comment on column user_keys.provider_id is 'ID nhà cung cấp vận chuyển';

comment on column user_keys.credentials is 'Thông tin xác thực';

comment on column user_keys.settings is 'Cài đặt người dùng';

alter table user_keys
    owner to root;

create index idx_user_configurations_user
    on user_keys (user_id);

create index idx_user_configurations_provider
    on user_keys (provider_id);

create index idx_user_configurations_status
    on user_keys ((settings ->> 'is_active'::text));

create table config_finetuning
(
    id         uuid    default uuid_generate_v4()                                      not null
        primary key,
    training   jsonb                                                                   not null,
    validation jsonb                                                                   not null,
    is_active  boolean default false,
    create_by  integer
        references users,
    create_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table config_finetuning is 'Bảng lưu trữ cấu hình cho quá trình fine-tuning mô hình';

comment on column config_finetuning.id is 'ID cấu hình fine-tuning, là UUID duy nhất';

comment on column config_finetuning.training is 'Dữ liệu huấn luyện và tham số cấu hình';

comment on column config_finetuning.validation is 'Dữ liệu kiểm thử và các metric đánh giá';

comment on column config_finetuning.is_active is 'Trạng thái kích hoạt của cấu hình';

comment on column config_finetuning.create_by is 'ID người tạo cấu hình';

comment on column config_finetuning.create_at is 'Thời điểm tạo cấu hình (milliseconds)';

comment on column config_finetuning.update_at is 'Thời điểm cập nhật cấu hình gần nhất (milliseconds)';

alter table config_finetuning
    owner to root;

create index idx_config_finetuning_is_active
    on config_finetuning (is_active);

create index idx_config_finetuning_create_by
    on config_finetuning (create_by);

create index idx_config_finetuning_create_at
    on config_finetuning (create_at);

create index idx_config_finetuning_training_model
    on config_finetuning ((training ->> 'model_id'::text));

create index idx_config_finetuning_training_dataset
    on config_finetuning ((training ->> 'dataset_id'::text));

create index idx_vector_store_key_provider_id
    on vector_store_key (provider_id);

create index idx_vector_store_key_create_by
    on vector_store_key (create_by);

create index idx_vector_store_key_update_by
    on vector_store_key (update_by);

create index idx_vector_store_key_storage
    on vector_store_key (storage);

create table facebook_personal
(
    user_id              integer      not null,
    access_token         varchar(1000),
    facebook_personal_id varchar(255) not null,
    expiration_date      timestamp,
    id                   integer generated always as identity
        constraint facebook_personal_pk
            primary key,
    expiration_date_unix bigint default (EXTRACT(epoch FROM now()) * (1000)::numeric)
);

alter table facebook_personal
    owner to root;

create table order_plan_history
(
    id            bigserial
        constraint order_plan_history_pk
            primary key,
    plan_name     varchar(255),
    point         bigint default 0 not null,
    user_id       integer
        constraint order_plan_history_users_id_fk
            references users,
    billing_cycle varchar(50),
    usage_limit   bigint,
    usage_unit    varchar(50),
    created_at    bigint           not null
);

comment on table order_plan_history is 'lịch sử đơn hàng gói dịch vụ';

alter table order_plan_history
    owner to root;

create table function_agents
(
    id          uuid        default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                                not null,
    description text,
    tags        jsonb,
    created_by  integer                                                                     not null,
    updated_by  integer                                                                     not null,
    created_at  bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    access_type varchar(20) default 'PRIVATE'::character varying                            not null,
    is_active   boolean     default true                                                    not null,
    key         uuid
);

comment on table function_agents is 'Bảng lưu trữ thông tin cơ bản của function do admin tạo, không chứa code hay status';

comment on column function_agents.id is 'UUID định danh duy nhất cho mỗi function';

comment on column function_agents.name is 'Tên của function';

comment on column function_agents.description is 'Mô tả chi tiết về chức năng và mục đích sử dụng';

comment on column function_agents.tags is 'Danh sách các tag dùng để phân loại và tìm kiếm function';

comment on column function_agents.created_by is 'ID của nhân viên tạo function, liên kết với bảng employees';

comment on column function_agents.updated_by is 'ID của nhân viên sửa function, liên kết với bảng employees';

comment on column function_agents.created_at is 'Thời điểm tạo, dạng UNIX timestamp với millisecond';

comment on column function_agents.updated_at is 'Thời điểm cập nhật cuối, dạng UNIX timestamp với millisecond';

comment on column function_agents.access_type is 'Loại quyền truy cập: PRIVATE (yêu cầu mua), PUBLIC (tất cả user có thể sử dụng)';

comment on column function_agents.is_active is 'Trạng thái kích hoạt của function';

comment on column function_agents.key is 'Key để call function';

alter table function_agents
    owner to root;

create index idx_function_agents_created_by
    on function_agents (created_by);

create index idx_function_agents_access_type
    on function_agents (access_type);

create index idx_function_agents_is_active
    on function_agents (is_active);

create index idx_function_agents_tags
    on function_agents using gin (tags);

create table function_agent_versions
(
    id                 uuid        default uuid_generate_v4()                                      not null
        primary key,
    function_agent_id  uuid
        references function_agents
            on delete cascade,
    version_number     integer                                                                     not null,
    version_name       varchar(255),
    status             varchar(20) default 'DRAFT'::character varying                              not null,
    code_definition    jsonb                                                                       not null,
    change_description text,
    created_by         integer                                                                     not null,
    updated_by         integer                                                                     not null,
    created_at         bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_default         boolean     default false,
    name               varchar(64)                                                                 not null,
    description        text,
    prefix             varchar(32)                                                                 not null,
    unique (function_agent_id, version_number)
);

comment on table function_agent_versions is 'Bảng lưu trữ các phiên bản của function, chứa code, trạng thái và lịch sử thay đổi';

comment on column function_agent_versions.id is 'UUID định danh duy nhất cho mỗi phiên bản function';

comment on column function_agent_versions.function_agent_id is 'ID của function mà phiên bản này thuộc về, tham chiếu đến bảng function_agents';

comment on column function_agent_versions.version_number is 'Số thứ tự phiên bản, tăng dần theo thời gian';

comment on column function_agent_versions.version_name is 'Tên định danh cho phiên bản, dễ nhớ hơn số';

comment on column function_agent_versions.status is 'Trạng thái của phiên bản: DRAFT (bản nháp), TESTED (đã test), PUBLISHED (đã xuất bản), ARCHIVED (đã lưu trữ)';

comment on column function_agent_versions.code_definition is 'Định nghĩa code JSON của function, bao gồm cả các tham số và logic';

comment on column function_agent_versions.change_description is 'Mô tả những thay đổi so với phiên bản trước đó';

comment on column function_agent_versions.created_by is 'ID của nhân viên tạo phiên bản, liên kết với bảng employees';

comment on column function_agent_versions.updated_by is 'ID của nhân viên sửa phiên bản, liên kết với bảng employees';

comment on column function_agent_versions.created_at is 'Thời điểm tạo, dạng UNIX timestamp với millisecond';

comment on column function_agent_versions.updated_at is 'Thời điểm cập nhật cuối, dạng UNIX timestamp với millisecond';

comment on column function_agent_versions.is_default is 'Đánh dấu phiên bản mặc định nếu không có phiên bản nào được chọn';

alter table function_agent_versions
    owner to root;

create index idx_function_agent_versions_function_id
    on function_agent_versions (function_agent_id);

create index idx_function_agent_versions_status
    on function_agent_versions (status);

create index idx_function_agent_versions_is_default
    on function_agent_versions (is_default)
    where (is_default = true);

create index idx_function_agent_versions_code
    on function_agent_versions using gin (code_definition);

create table strategy_agents
(
    id          serial
        primary key,
    name        varchar(255)                                                            not null,
    description text,
    tags        jsonb,
    created_by  integer                                                                 not null
        references employees,
    updated_by  integer                                                                 not null
        references employees,
    created_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_active   boolean default true                                                    not null
);

comment on table strategy_agents is 'Bảng lưu trữ các chiến lược xử lý của agent, định nghĩa cách thức agent tương tác và xử lý yêu cầu';

comment on column strategy_agents.id is 'ID định danh duy nhất cho mỗi chiến lược, tự động tăng';

comment on column strategy_agents.name is 'Tên của chiến lược xử lý, dễ nhớ và mô tả ngắn gọn';

comment on column strategy_agents.description is 'Mô tả chi tiết về chiến lược xử lý, cách thức hoạt động và tình huống áp dụng';

comment on column strategy_agents.tags is 'Danh sách các tag dùng để phân loại và tìm kiếm chiến lược (dạng JSONB)';

comment on column strategy_agents.created_by is 'ID của nhân viên tạo chiến lược, liên kết với bảng employees';

comment on column strategy_agents.updated_by is 'ID của nhân viên cập nhật gần nhất, liên kết với bảng employees';

comment on column strategy_agents.created_at is 'Thời điểm tạo chiến lược, dạng UNIX timestamp với millisecond';

comment on column strategy_agents.updated_at is 'Thời điểm cập nhật cuối cùng, dạng UNIX timestamp với millisecond';

comment on column strategy_agents.is_active is 'Trạng thái hoạt động của chiến lược: TRUE - đang hoạt động, FALSE - đã vô hiệu hóa';

alter table strategy_agents
    owner to root;

create index idx_strategy_agents_created_by
    on strategy_agents (created_by);

create index idx_strategy_agents_is_active
    on strategy_agents (is_active);

create index idx_strategy_agents_name
    on strategy_agents (name);

create index idx_strategy_agents_tags
    on strategy_agents using gin (tags);

create table strategy_agent_versions
(
    id                 serial
        primary key,
    strategy_agent_id  integer                                                                 not null
        references strategy_agents
            on delete cascade,
    version_number     integer                                                                 not null,
    version_name       varchar(255),
    status             varchar(20)                                                             not null,
    hidden_content     jsonb                                                                   not null,
    editable_content   jsonb                                                                   not null,
    config_agent       jsonb                                                                   not null,
    change_description text,
    created_by         integer                                                                 not null
        references employees,
    updated_by         integer                                                                 not null
        references employees,
    created_at         bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_default         boolean default false,
    unique (strategy_agent_id, version_number)
);

comment on table strategy_agent_versions is 'Bảng lưu trữ các phiên bản của chiến lược agent, bao gồm nội dung, cấu hình, trạng thái và lịch sử thay đổi';

comment on column strategy_agent_versions.id is 'ID định danh duy nhất cho mỗi phiên bản chiến lược, tự động tăng';

comment on column strategy_agent_versions.strategy_agent_id is 'ID của chiến lược agent mà phiên bản này thuộc về, tham chiếu đến bảng strategy_agents';

comment on column strategy_agent_versions.version_number is 'Số thứ tự phiên bản, tăng dần theo thời gian, giúp theo dõi tiến trình phát triển';

comment on column strategy_agent_versions.version_name is 'Tên định danh cho phiên bản, dễ nhớ hơn số (ví dụ: "v1.0", "v2.0-beta")';

comment on column strategy_agent_versions.status is 'Trạng thái phát triển của phiên bản: DRAFT (bản nháp), TESTED (đã kiểm thử), PUBLISHED (đã xuất bản), ARCHIVED (đã lưu trữ)';

comment on column strategy_agent_versions.hidden_content is 'Nội dung ẩn của chiến lược mà người dùng không thể xem hoặc chỉnh sửa, thường là logic xử lý nâng cao';

comment on column strategy_agent_versions.editable_content is 'Nội dung template của chiến lược mà người dùng có thể tùy chỉnh, như câu hỏi, phản hồi mẫu';

comment on column strategy_agent_versions.config_agent is 'Cấu hình chi tiết cho agent khi sử dụng chiến lược này, bao gồm tham số và điều kiện';

comment on column strategy_agent_versions.change_description is 'Mô tả những thay đổi so với phiên bản trước đó, giúp theo dõi lịch sử phát triển';

comment on column strategy_agent_versions.created_by is 'ID của nhân viên tạo phiên bản, tham chiếu đến bảng employees';

comment on column strategy_agent_versions.updated_by is 'ID của nhân viên cập nhật gần nhất phiên bản, tham chiếu đến bảng employees';

comment on column strategy_agent_versions.created_at is 'Thời điểm tạo phiên bản, dạng UNIX timestamp với millisecond';

comment on column strategy_agent_versions.updated_at is 'Thời điểm cập nhật cuối cùng, dạng UNIX timestamp với millisecond';

comment on column strategy_agent_versions.is_default is 'Đánh dấu phiên bản mặc định nếu không có phiên bản nào được chọn rõ ràng';

alter table strategy_agent_versions
    owner to root;

create index idx_strategy_agent_versions_strategy_agent_id
    on strategy_agent_versions (strategy_agent_id);

create index idx_strategy_agent_versions_status
    on strategy_agent_versions (status);

create index idx_strategy_agent_versions_is_default
    on strategy_agent_versions (is_default)
    where (is_default = true);

create index idx_strategy_agent_versions_created_by
    on strategy_agent_versions (created_by);

create index idx_strategy_agent_versions_config
    on strategy_agent_versions using gin (config_agent);

create table url_resources
(
    id                uuid   default uuid_generate_v4()                                      not null
        primary key,
    url               varchar(2048)                                                          not null,
    url_embedding     vector(1536),
    title             varchar(512)                                                           not null,
    title_embedding   vector(1536),
    content           text                                                                   not null,
    content_embedding vector(1536)                                                           not null,
    type              varchar(50),
    tags              jsonb,
    owned_by          integer                                                                not null
        constraint url_resources_users_fk
            references users,
    created_at        bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at        bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table url_resources is 'Bảng lưu thông tin tài nguyên URL';

comment on column url_resources.id is 'Mã định danh tài nguyên URL';

comment on column url_resources.url is 'Đường dẫn URL';

comment on column url_resources.url_embedding is 'Vector embedding cho URL (1536d)';

comment on column url_resources.title is 'Tiêu đề của tài nguyên URL';

comment on column url_resources.title_embedding is 'Vector embedding cho tiêu đề (1536d)';

comment on column url_resources.content is 'Nội dung về tài nguyên URL';

comment on column url_resources.content_embedding is 'Vector embedding cho content URL (1536d)';

comment on column url_resources.type is 'Loại tài nguyên URL (web, doc, etc.)';

comment on column url_resources.tags is 'Các thẻ phân loại URL (dạng JSONB)';

comment on column url_resources.owned_by is 'Mã người sở hữu tài nguyên URL (FK tới users)';

comment on column url_resources.created_at is 'Thời điểm tạo bản ghi (unix timestamp)';

comment on column url_resources.updated_at is 'Thời điểm cập nhật bản ghi (unix timestamp)';

alter table url_resources
    owner to root;

create table media_resources
(
    id                    uuid   default uuid_generate_v4()                                      not null
        primary key,
    name                  varchar(255)                                                           not null,
    description           text                                                                   not null,
    size                  bigint                                                                 not null,
    tags                  jsonb,
    storage_key           varchar(512)                                                           not null
        constraint media_resources_storage_key_unique
            unique,
    owned_by              integer                                                                not null
        constraint media_resources_users_fk
            references users,
    created_at            bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    name_embedding        vector(1536),
    description_embedding vector(1536),
    updated_at            bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table media_resources is 'Bảng lưu thông tin tài nguyên media';

comment on column media_resources.id is 'Mã định danh media';

comment on column media_resources.name is 'Tên media';

comment on column media_resources.description is 'Mô tả về tài nguyên media';

comment on column media_resources.size is 'Kích thước media (byte)';

comment on column media_resources.tags is 'Các thẻ phân loại media (dạng JSONB)';

comment on column media_resources.storage_key is 'Khóa định danh trên hệ thống lưu trữ';

comment on column media_resources.owned_by is 'Mã người sở hữu media (FK tới users)';

comment on column media_resources.created_at is 'Thời điểm tạo bản ghi (unix timestamp)';

comment on column media_resources.name_embedding is 'Vector embedding cho tên media (1536d)';

comment on column media_resources.description_embedding is 'Vector embedding cho mô tả media (1536d)';

comment on column media_resources.updated_at is 'Thời điểm cập nhật bản ghi (unix timestamp)';

alter table media_resources
    owner to root;

create table user_agents
(
    id              uuid default uuid_generate_v4() not null
        primary key,
    strategy_config jsonb,
    convert_config  jsonb,
    created_by      integer
        references users
);

comment on table user_agents is 'Bảng quản lý thông tin chi tiết các agent của người dùng';

comment on column user_agents.id is 'UUID định danh duy nhất, tham chiếu từ agents.detail_id khi source_type là user';

comment on column user_agents.strategy_config is 'Cấu hình chiến lược xử lý dạng JSONB cho agent người dùng';

comment on column user_agents.convert_config is 'Cấu hình chuyển đổi dạng JSONB cho agent người dùng';

comment on column user_agents.created_by is 'ID người dùng tạo ra agent này';

alter table user_agents
    owner to root;

create table type_agents
(
    id          serial
        primary key,
    name        varchar(255)                                                           not null,
    description text,
    created_by  integer                                                                not null
        references employees,
    updated_by  integer                                                                not null
        references employees,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table type_agents is 'Bảng quản lý các loại agent trong hệ thống, định nghĩa các nhóm chức năng mà agent có thể thực hiện';

comment on column type_agents.id is 'ID định danh duy nhất cho mỗi loại agent, tự động tăng';

comment on column type_agents.name is 'Tên của loại agent, ví dụ: Customer Support, Sales Agent, Technical Assistant';

comment on column type_agents.description is 'Mô tả chi tiết về loại agent, bao gồm phạm vi hoạt động, chức năng và mục đích';

comment on column type_agents.created_by is 'ID nhân viên tạo loại agent, tham chiếu đến bảng employees';

comment on column type_agents.updated_by is 'ID nhân viên cập nhật loại agent gần nhất, tham chiếu đến bảng employees';

comment on column type_agents.created_at is 'Thời điểm tạo loại agent (timestamp millis)';

comment on column type_agents.updated_at is 'Thời điểm cập nhật loại agent gần nhất (timestamp millis)';

alter table type_agents
    owner to root;

create index idx_type_agents_name
    on type_agents (name);

create index idx_type_agents_created_by
    on type_agents (created_by);

create index idx_type_agents_updated_by
    on type_agents (updated_by);

create table type_agent_mappings
(
    type_agent_id integer not null
        constraint type_agent_mappings_group_id_fkey
            references type_agents
            on delete cascade,
    function_id   uuid    not null
        references function_agents
            on delete cascade,
    primary key (type_agent_id, function_id)
);

comment on table type_agent_mappings is 'Bảng ánh xạ nhiều-nhiều giữa loại agent và function, xác định mỗi loại agent có thể sử dụng những function nào';

comment on column type_agent_mappings.type_agent_id is 'ID loại agent, khóa ngoại tham chiếu đến bảng type_agents.id, xóa loại agent cũng sẽ xóa tất cả bản ghi liên kết';

comment on column type_agent_mappings.function_id is 'ID function, khóa ngoại tham chiếu đến bảng function_agents.id, xóa function cũng sẽ xóa tất cả bản ghi liên kết';

alter table type_agent_mappings
    owner to root;

create index idx_type_agent_mappings_function_id
    on type_agent_mappings (function_id);

create index idx_type_agent_mappings_type_agent_id
    on type_agent_mappings (type_agent_id);

create table knowledge_files
(
    id          uuid    default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                            not null,
    storage_key varchar(512)                                                            not null
        unique,
    owner_type  varchar(20)                                                             not null,
    owned_by    integer                                                                 not null,
    is_owner    boolean default true,
    is_for_sale boolean default false                                                   not null,
    created_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    storage     bigint  default 0                                                       not null
);

comment on table knowledge_files is 'Bảng lưu trữ thông tin về các file tri thức trong hệ thống, bao gồm thông tin về quyền sở hữu và trạng thái bán hàng';

comment on column knowledge_files.id is 'Mã định danh duy nhất cho mỗi file tri thức, tự động tăng';

comment on column knowledge_files.name is 'Tên hiển thị của file tri thức, dùng để hiển thị cho người dùng';

comment on column knowledge_files.storage_key is 'Khóa định danh file trên hệ thống lưu trữ, dùng để truy xuất nội dung file từ storage system';

comment on column knowledge_files.owner_type is 'Loại người sở hữu file: "user" (người dùng thông thường) hoặc "employee" (nhân viên hệ thống)';

comment on column knowledge_files.owned_by is 'ID của người sở hữu file, tham chiếu đến bảng users hoặc employees tùy theo owner_type';

comment on column knowledge_files.is_for_sale is 'Trạng thái đăng bán file trên chợ tri thức: TRUE - đang được đăng bán, FALSE - không đăng bán';

comment on column knowledge_files.created_at is 'Thời điểm tạo bản ghi file, dạng UNIX timestamp với millisecond';

comment on column knowledge_files.storage is 'Dung lượng của file tri thức tính bằng byte, dùng để tính toán không gian lưu trữ và giới hạn';

alter table knowledge_files
    owner to root;

create index idx_knowledge_files_owner
    on knowledge_files (owner_type, owned_by);

create index idx_knowledge_files_created_at
    on knowledge_files (created_at);

create index idx_knowledge_files_for_sale
    on knowledge_files (is_for_sale)
    where (is_for_sale = true);

create index idx_knowledge_files_name
    on knowledge_files (name);

create index idx_knowledge_files_storage
    on knowledge_files (storage);

create table vector_stores
(
    id         varchar(100)                                                           not null
        primary key,
    name       varchar(255)                                                           not null,
    storage    bigint default 0                                                       not null,
    owner_type varchar(20)                                                            not null,
    owner_id   integer                                                                not null,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table vector_stores is 'Lưu thông tin các kho vector phục vụ AI/RAG';

comment on column vector_stores.id is 'ID duy nhất (UUID hoặc mã định danh)';

comment on column vector_stores.name is 'Tên vector store';

comment on column vector_stores.storage is 'Dung lượng đã dùng (bytes/tokens)';

comment on column vector_stores.owner_type is 'Loại chủ sở hữu: user hoặc employee';

comment on column vector_stores.owner_id is 'ID của user hoặc employee';

comment on column vector_stores.created_at is 'Thời điểm tạo';

comment on column vector_stores.update_at is 'Thời điểm cập nhật';

alter table vector_stores
    owner to root;

create index idx_vector_stores_owner
    on vector_stores (owner_type, owner_id);

create index idx_vector_stores_name
    on vector_stores (name);

create table vector_store_files
(
    vector_store_id varchar(100) not null
        references vector_stores
            on delete cascade,
    file_id         uuid         not null
        references knowledge_files
            on delete cascade,
    primary key (vector_store_id, file_id)
);

comment on table vector_store_files is 'Bảng trung gian lưu cặp vector_store và file';

comment on column vector_store_files.vector_store_id is 'ID của vector_stores';

comment on column vector_store_files.file_id is 'ID của knowledge_files';

alter table vector_store_files
    owner to root;

create index idx_file_id
    on vector_store_files (file_id);

create table agents
(
    id              uuid        default uuid_generate_v4()                                      not null
        primary key,
    name            varchar(255)                                                                not null,
    source_type     varchar(50) default 'user'::character varying                               not null,
    agent_type_id   integer
        references type_agents,
    model_config    jsonb,
    vector_store_id varchar(100)
        references vector_stores,
    detail_id       uuid                                                                        not null,
    is_active       boolean     default true,
    created_at      bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint      default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    unique (source_type, detail_id)
);

comment on table agents is 'Bảng tổng hợp quản lý tất cả các loại agent trong hệ thống';

comment on column agents.id is 'UUID định danh duy nhất cho mỗi agent';

comment on column agents.name is 'Tên hiển thị của agent';

comment on column agents.source_type is 'Loại nguồn gốc của agent (user, admin, system, v.v)';

comment on column agents.agent_type_id is 'ID tham chiếu đến loại chức năng của agent trong bảng type_agents';

comment on column agents.model_config is 'Cấu hình AI model dạng JSONB cho agent (temperature, tokens, system prompt, v.v)';

comment on column agents.vector_store_id is 'ID của kho vector sử dụng bởi agent để truy vấn ngữ nghĩa';

comment on column agents.detail_id is 'ID tham chiếu đến entity chi tiết tương ứng với source_type (user_agents, admin_agents, v.v)';

comment on column agents.is_active is 'Trạng thái hoạt động của agent';

comment on column agents.created_at is 'Thời điểm tạo (timestamp millis)';

comment on column agents.updated_at is 'Thời điểm cập nhật gần nhất (timestamp millis)';

alter table agents
    owner to root;

create table user_websites
(
    id           bigserial
        primary key,
    user_id      integer      not null,
    website_name varchar(255) not null,
    host         varchar(255) not null,
    verify       boolean   default false,
    created_at   timestamp default CURRENT_TIMESTAMP,
    agent_id     uuid
        constraint user_websites_agents_id_fk
            references agents
);

comment on table user_websites is 'Lưu trữ danh sách website của người dùng, bao gồm tên, host và trạng thái xác minh';

comment on column user_websites.id is 'Khóa chính, tự động tăng';

comment on column user_websites.user_id is 'ID của người dùng sở hữu website';

comment on column user_websites.website_name is 'Tên website do người dùng đặt';

comment on column user_websites.host is 'Tên miền hoặc địa chỉ host của website';

comment on column user_websites.verify is 'Trạng thái xác minh của website (TRUE nếu đã xác minh)';

comment on column user_websites.created_at is 'Thời điểm tạo bản ghi';

alter table user_websites
    owner to root;

create table admin_agents
(
    id         uuid default uuid_generate_v4() not null
        primary key,
    parent_id  uuid
        constraint fk_admin_agents_parent_id
            references agents,
    created_by integer
        references employees,
    updated_by integer
        references employees
);

comment on table admin_agents is 'Bảng quản lý thông tin chi tiết các agent của quản trị viên';

comment on column admin_agents.id is 'UUID định danh duy nhất, tham chiếu từ agents.detail_id khi source_type là admin';

comment on column admin_agents.parent_id is 'ID agent cha (nếu có)';

comment on column admin_agents.created_by is 'ID nhân viên tạo ra agent này';

comment on column admin_agents.updated_by is 'ID nhân viên cập nhật agent gần nhất';

alter table admin_agents
    owner to root;

create table memory_agents
(
    id         uuid   default uuid_generate_v4()                                      not null
        primary key,
    agent_id   uuid
        references agents,
    fact       text                                                                   not null,
    embedding  vector(1536),
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table memory_agents is 'Bảng lưu trữ bộ nhớ của agent theo dạng fact';

comment on column memory_agents.id is 'UUID định danh duy nhất cho mỗi bản ghi bộ nhớ';

comment on column memory_agents.agent_id is 'ID của agent sở hữu bộ nhớ này';

comment on column memory_agents.fact is 'Nội dung thông tin, sự kiện được lưu trong bộ nhớ';

comment on column memory_agents.embedding is 'Vector nhúng (1536 chiều) biểu diễn ngữ nghĩa của fact';

comment on column memory_agents.created_at is 'Thời điểm tạo bộ nhớ (timestamp millis)';

comment on column memory_agents.updated_at is 'Thời điểm cập nhật bộ nhớ gần nhất (timestamp millis)';

alter table memory_agents
    owner to root;

create table agent_urls
(
    agent_id uuid not null
        references agents,
    url_id   uuid not null
        references url_resources,
    primary key (agent_id, url_id)
);

comment on table agent_urls is 'Bảng liên kết giữa agent và tài nguyên URL';

comment on column agent_urls.agent_id is 'ID của agent';

comment on column agent_urls.url_id is 'ID của tài nguyên URL';

alter table agent_urls
    owner to root;

create table agent_media
(
    agent_id uuid not null
        references agents,
    media_id uuid not null
        references media_resources,
    primary key (agent_id, media_id)
);

comment on table agent_media is 'Bảng liên kết giữa agent và tài nguyên media';

comment on column agent_media.agent_id is 'ID của agent';

comment on column agent_media.media_id is 'ID của tài nguyên media';

alter table agent_media
    owner to root;

create table agent_functions
(
    agent_id    uuid not null
        references agents,
    function_id uuid not null
        references function_agents,
    primary key (agent_id, function_id)
);

comment on table agent_functions is 'Bảng liên kết giữa agent và các hàm được agent sử dụng';

comment on column agent_functions.agent_id is 'ID của agent';

comment on column agent_functions.function_id is 'ID của hàm từ bảng function_agents';

alter table agent_functions
    owner to root;

create table facebook_page
(
    facebook_personal_id integer      not null
        constraint facebook_page_facebook_personal_id_fk
            references facebook_personal,
    page_access_token    varchar(1000),
    page_name            text,
    facebook_page_id     varchar(255) not null
        constraint facebook_page_pk
            primary key,
    is_active            boolean,
    agent_id             uuid
        references agents,
    avatar_page          varchar(255),
    is_error             boolean
);

comment on table facebook_page is 'Bảng quản lý thông tin trang Facebook được kết nối với agent';

comment on column facebook_page.facebook_personal_id is 'ID tài khoản Facebook cá nhân liên kết';

comment on column facebook_page.page_access_token is 'Access token của trang Facebook';

comment on column facebook_page.page_name is 'Tên trang Facebook';

comment on column facebook_page.facebook_page_id is 'ID duy nhất của trang Facebook';

comment on column facebook_page.is_active is 'Trạng thái hoạt động của trang';

comment on column facebook_page.agent_id is 'ID agent được kết nối với trang Facebook';

comment on column facebook_page.avatar_page is 'URL avatar của trang Facebook';

comment on column facebook_page.is_error is 'Trạng thái lỗi của trang Facebook';

alter table facebook_page
    owner to root;

create table user_function_agents
(
    id                bigserial
        primary key,
    user_id           integer                                                                 not null,
    function_agent_id uuid                                                                    not null
        references function_agents
            on delete cascade,
    version_id        uuid
        references function_agent_versions,
    purchase_date     bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_active         boolean default true                                                    not null,
    unique (user_id, function_agent_id, version_id)
);

comment on table user_function_agents is 'Bảng lưu trữ quyền sở hữu function của người dùng';

comment on column user_function_agents.id is 'ID tự tăng định danh duy nhất cho bản ghi quyền sở hữu';

comment on column user_function_agents.user_id is 'ID người dùng sở hữu function, tham chiếu đến bảng users';

comment on column user_function_agents.function_agent_id is 'ID function mà người dùng sở hữu, tham chiếu đến bảng function_agents';

comment on column user_function_agents.version_id is 'Phiên bản cụ thể mà người dùng sở hữu, người dùng có thể sở hữu nhiều phiên bản';

comment on column user_function_agents.purchase_date is 'Thời điểm mua function, dạng UNIX timestamp với millisecond';

comment on column user_function_agents.is_active is 'Trạng thái kích hoạt, function có thể bị vô hiệu hóa trong trường hợp vi phạm hoặc hết hạn';

alter table user_function_agents
    owner to root;

create index idx_user_function_agents_user_id
    on user_function_agents (user_id);

create index idx_user_function_agents_function_id
    on user_function_agents (function_agent_id);

create index idx_user_function_agents_version_id
    on user_function_agents (version_id);

create index idx_user_function_agents_is_active
    on user_function_agents (is_active);

create table user_strategy_agents
(
    id                serial
        primary key,
    user_id           integer                                                                 not null,
    strategy_agent_id integer                                                                 not null
        references strategy_agents
            on delete cascade,
    version_id        integer                                                                 not null
        references strategy_agent_versions,
    purchase_date     bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_active         boolean default true                                                    not null,
    unique (user_id, strategy_agent_id, version_id)
);

comment on table user_strategy_agents is 'Bảng quản lý quyền sử dụng chiến lược agent của người dùng, xác định người dùng nào có quyền truy cập vào chiến lược và phiên bản nào';

comment on column user_strategy_agents.id is 'ID định danh duy nhất cho mỗi bản ghi quyền sử dụng, tự động tăng';

comment on column user_strategy_agents.user_id is 'ID của người dùng được cấp quyền sử dụng chiến lược, tham chiếu đến bảng users';

comment on column user_strategy_agents.strategy_agent_id is 'ID của chiến lược agent được cấp quyền, tham chiếu đến bảng strategy_agents';

comment on column user_strategy_agents.version_id is 'ID phiên bản cụ thể của chiến lược mà người dùng được cấp quyền, tham chiếu đến bảng strategy_agent_versions';

comment on column user_strategy_agents.purchase_date is 'Thời điểm cấp quyền truy cập cho người dùng, dạng UNIX timestamp với millisecond';

comment on column user_strategy_agents.is_active is 'Trạng thái kích hoạt quyền sử dụng: TRUE - đang kích hoạt, FALSE - đã vô hiệu hóa';

alter table user_strategy_agents
    owner to root;

create index idx_user_strategy_agents_user_id
    on user_strategy_agents (user_id);

create index idx_user_strategy_agents_strategy_agent_id
    on user_strategy_agents (strategy_agent_id);

create index idx_user_strategy_agents_version_id
    on user_strategy_agents (version_id);

create index idx_user_strategy_agents_is_active
    on user_strategy_agents (is_active);

create index idx_user_strategy_agents_access_time
    on user_strategy_agents (purchase_date);

create table user_conversation_thread
(
    thread_id  uuid   default uuid_generate_v4()                                      not null
        constraint conversation_thread_pkey
            primary key,
    name       varchar(255)                                                           not null,
    user_id    integer                                                                not null
        constraint fk_user
            references users
            on delete cascade,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_conversation_thread is 'Lưu trữ thông tin về các cuộc hội thoại của người dùng';

comment on column user_conversation_thread.thread_id is 'ID định danh duy nhất cho mỗi cuộc hội thoại';

comment on column user_conversation_thread.name is 'Tên của cuộc hội thoại';

comment on column user_conversation_thread.user_id is 'ID của người dùng sở hữu cuộc hội thoại';

comment on column user_conversation_thread.created_at is 'Thời điểm cuộc hội thoại được tạo (epoch milliseconds)';

comment on column user_conversation_thread.updated_at is 'Thời điểm cuộc hội thoại được cập nhật lần cuối (epoch milliseconds)';

alter table user_conversation_thread
    owner to root;

create index idx_conversation_thread_user_id
    on user_conversation_thread (user_id);

create index idx_conversation_thread_created_at
    on user_conversation_thread (created_at);

create table user_messages
(
    message_id uuid   default uuid_generate_v4()                                      not null
        constraint messages_pkey
            primary key,
    thread_id  uuid                                                                   not null
        constraint fk_thread
            references user_conversation_thread
            on delete cascade,
    role       varchar(50)                                                            not null
        constraint messages_role_check
            check ((role)::text = ANY
        (ARRAY [('user'::character varying)::text, ('assistant'::character varying)::text])),
    content    jsonb                                                                  not null,
    timestamp  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_messages is 'Lưu trữ các tin nhắn trong cuộc hội thoại';

comment on column user_messages.message_id is 'ID định danh duy nhất cho mỗi tin nhắn';

comment on column user_messages.thread_id is 'ID của cuộc hội thoại chứa tin nhắn này';

comment on column user_messages.role is 'Vai trò của người gửi tin nhắn (user, assistant, system)';

comment on column user_messages.content is 'Nội dung tin nhắn dạng JSONB để hỗ trợ nhiều loại nội dung (văn bản, hình ảnh, v.v.)';

comment on column user_messages.timestamp is 'Thời điểm tin nhắn được gửi (epoch milliseconds)';

alter table user_messages
    owner to root;

create index idx_message_thread_id
    on user_messages (thread_id);

create index idx_message_timestamp
    on user_messages (timestamp);

create index idx_message_role
    on user_messages (role);

create index idx_message_content
    on user_messages using gin (content);

create table base_models
(
    id                      varchar(100)                                                            not null
        primary key,
    name                    varchar(100)                                                            not null,
    description             text,
    provider_id             integer
        references providers,
    base_input_rate         integer default 0,
    base_output_rate        integer default 0,
    base_train_rate         integer default 0,
    fine_tuning_input_rate  integer default 0,
    fine_tuning_output_rate integer default 0,
    fine_tuning_train_rate  integer default 0,
    token_count             integer default 0,
    config                  jsonb   default '{"top_p": true, "function": true, "file_search": true, "temperature": true, "response_format": ["text", "json_object", "json_schema"], "code_interpreter": true, "reasoning_effort": ["low", "medium", "high"]}'::jsonb,
    created_by              integer
        references employees,
    updated_by              integer
        references employees,
    created_at              bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at              bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table base_models is 'Bảng lưu trữ thông tin về các mô hình nền tảng được hỗ trợ trong hệ thống';

comment on column base_models.id is 'ID định danh duy nhất cho mô hình, thường là mã của nhà cung cấp';

comment on column base_models.name is 'Tên hiển thị của mô hình nền tảng';

comment on column base_models.description is 'Mô tả chi tiết về mô hình và khả năng của nó';

comment on column base_models.provider_id is 'ID nhà cung cấp mô hình, tham chiếu đến bảng providers';

comment on column base_models.base_input_rate is 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản';

comment on column base_models.base_output_rate is 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản';

comment on column base_models.base_train_rate is 'Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản';

comment on column base_models.fine_tuning_input_rate is 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning';

comment on column base_models.fine_tuning_output_rate is 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning';

comment on column base_models.fine_tuning_train_rate is 'Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning';

comment on column base_models.token_count is 'Số token tối đa mô hình có thể xử lý trong một lần request';

comment on column base_models.config is 'Cấu hình và khả năng của mô hình dạng JSONB';

comment on column base_models.created_by is 'ID nhân viên tạo bản ghi mô hình';

comment on column base_models.updated_by is 'ID nhân viên cập nhật bản ghi mô hình gần nhất';

comment on column base_models.created_at is 'Thời điểm tạo bản ghi (timestamp millis)';

comment on column base_models.updated_at is 'Thời điểm cập nhật bản ghi gần nhất (timestamp millis)';

alter table base_models
    owner to root;

create index idx_base_models_provider_id
    on base_models (provider_id);

create index idx_base_models_name
    on base_models (name);

create table data_fine_tuning_models
(
    id              uuid default uuid_generate_v4() not null
        primary key,
    train_file      varchar(100),
    validation_file varchar(100),
    method          jsonb,
    metadata        jsonb
);

comment on table data_fine_tuning_models is 'Bảng lưu trữ chi tiết kỹ thuật của mô hình fine-tuning';

comment on column data_fine_tuning_models.id is 'UUID định danh duy nhất cho dữ liệu chi tiết của mô hình fine-tuning';

comment on column data_fine_tuning_models.train_file is 'Đường dẫn hoặc ID file dữ liệu huấn luyện';

comment on column data_fine_tuning_models.validation_file is 'Đường dẫn hoặc ID file dữ liệu kiểm thử';

comment on column data_fine_tuning_models.method is 'Phương pháp fine-tuning được sử dụng (dạng JSONB)';

comment on column data_fine_tuning_models.metadata is 'Thông tin bổ sung về quá trình fine-tuning (dạng JSONB)';

alter table data_fine_tuning_models
    owner to root;

create table fine_tuning_models
(
    id            varchar(100)                                                            not null
        primary key,
    name          varchar(64)                                                             not null,
    description   text,
    token         integer default 0,
    status        varchar(50),
    detail_id     uuid
        references data_fine_tuning_models,
    base_model_id varchar(100)
        references base_models,
    owner_type    varchar(50)                                                             not null,
    owner_by      integer                                                                 not null,
    created_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table fine_tuning_models is 'Bảng quản lý các mô hình đã được fine-tuning từ các mô hình nền tảng';

comment on column fine_tuning_models.id is 'ID định danh duy nhất cho mô hình fine-tuning';

comment on column fine_tuning_models.name is 'Tên hiển thị của mô hình fine-tuning';

comment on column fine_tuning_models.description is 'Mô tả chi tiết về mô hình và mục đích sử dụng';

comment on column fine_tuning_models.token is 'Số lượng token đã sử dụng trong quá trình fine-tuning';

comment on column fine_tuning_models.status is 'Trạng thái hiện tại của mô hình (ví dụ: pending, training, completed, failed)';

comment on column fine_tuning_models.detail_id is 'Tham chiếu đến bảng chi tiết kỹ thuật của mô hình fine-tuning';

comment on column fine_tuning_models.base_model_id is 'ID của mô hình nền tảng được sử dụng để fine-tuning';

comment on column fine_tuning_models.owner_type is 'Loại người sở hữu mô hình (user, organization, system)';

comment on column fine_tuning_models.owner_by is 'ID của người sở hữu mô hình';

comment on column fine_tuning_models.created_at is 'Thời điểm tạo mô hình (timestamp millis)';

comment on column fine_tuning_models.updated_at is 'Thời điểm cập nhật mô hình gần nhất (timestamp millis)';

alter table fine_tuning_models
    owner to root;

create index idx_fine_tuning_models_base_model_id
    on fine_tuning_models (base_model_id);

create index idx_fine_tuning_models_owner
    on fine_tuning_models (owner_type, owner_by);

create index idx_fine_tuning_models_status
    on fine_tuning_models (status);

create table data_fine_tuning
(
    id              uuid    default uuid_generate_v4()                                      not null
        primary key,
    train_data      jsonb,
    validation_data jsonb,
    is_active       boolean default true,
    owner_type      varchar(50),
    owner_by        integer,
    created_at      bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table data_fine_tuning is 'Bảng lưu trữ dữ liệu dùng cho quá trình fine-tuning các mô hình';

comment on column data_fine_tuning.id is 'UUID định danh duy nhất cho bộ dữ liệu fine-tuning';

comment on column data_fine_tuning.train_data is 'Dữ liệu huấn luyện dạng JSONB';

comment on column data_fine_tuning.validation_data is 'Dữ liệu kiểm thử dạng JSONB';

comment on column data_fine_tuning.is_active is 'Trạng thái kích hoạt của bộ dữ liệu';

comment on column data_fine_tuning.owner_type is 'Loại người sở hữu dữ liệu (user, organization, system)';

comment on column data_fine_tuning.owner_by is 'ID của người sở hữu dữ liệu';

comment on column data_fine_tuning.created_at is 'Thời điểm tạo bộ dữ liệu (timestamp millis)';

comment on column data_fine_tuning.updated_at is 'Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis)';

alter table data_fine_tuning
    owner to root;

create index idx_data_fine_tuning_owner
    on data_fine_tuning (owner_type, owner_by);

create index idx_data_fine_tuning_is_active
    on data_fine_tuning (is_active);

create table employee_conversation_thread
(
    thread_id       uuid   default uuid_generate_v4()                                      not null
        primary key,
    name            varchar(255)                                                           not null,
    employee_id     integer                                                                not null
        references employees
            on delete cascade,
    created_at      bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    vector_store_id varchar(255)
        references vector_stores
);

alter table employee_conversation_thread
    owner to root;

create table employee_messages
(
    message_id uuid   default uuid_generate_v4()                                      not null
        primary key,
    thread_id  uuid                                                                   not null
        references employee_conversation_thread,
    role       varchar(50)                                                            not null,
    content    jsonb                                                                  not null,
    timestamp  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table employee_messages is 'Lưu trữ các tin nhắn trong cuộc hội thoại';

comment on column employee_messages.message_id is 'ID định danh duy nhất cho mỗi tin nhắn';

comment on column employee_messages.thread_id is 'ID của cuộc hội thoại chứa tin nhắn này';

comment on column employee_messages.role is 'Vai trò của người gửi tin nhắn (user, assistant, system)';

comment on column employee_messages.content is 'Nội dung tin nhắn dạng JSONB để hỗ trợ nhiều loại nội dung (văn bản, hình ảnh, v.v.)';

comment on column employee_messages.timestamp is 'Thời điểm tin nhắn được gửi (epoch milliseconds)';

alter table employee_messages
    owner to root;

create table employee_memory
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    employee_id integer                                                                not null
        references employees
            on delete cascade,
    fact        text                                                                   not null,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    embedding   vector(1536)
);

alter table employee_memory
    owner to root;

create table user_warehouses
(
    id             serial
        primary key,
    name           varchar(255)                                                            not null,
    warehouse_type varchar(50)                                                             not null,
    description    text,
    location       varchar(255),
    is_active      boolean default true,
    created_by     integer,
    created_at     bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at     bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_warehouses is 'Bảng quản lý kho hàng của người dùng';

comment on column user_warehouses.id is 'ID kho hàng';

comment on column user_warehouses.name is 'Tên kho hàng';

comment on column user_warehouses.warehouse_type is 'Loại kho (vật lý hoặc ảo)';

comment on column user_warehouses.description is 'Mô tả kho hàng';

comment on column user_warehouses.location is 'Vị trí kho hàng';

comment on column user_warehouses.is_active is 'Trạng thái hoạt động của kho';

comment on column user_warehouses.created_by is 'Người tạo kho';

comment on column user_warehouses.created_at is 'Thời gian tạo kho (epoch milliseconds)';

comment on column user_warehouses.updated_at is 'Thời gian cập nhật kho (epoch milliseconds)';

alter table user_warehouses
    owner to root;

create table physical_warehouses
(
    id            serial
        primary key,
    warehouse_id  integer                                                                not null
        unique
        references user_warehouses
            on delete cascade,
    address       text,
    square_meters numeric(10, 2),
    manager       varchar(100),
    contact_phone varchar(20),
    contact_email varchar(100),
    created_at    bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at    bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table physical_warehouses is 'Thông tin chi tiết kho vật lý';

comment on column physical_warehouses.id is 'ID kho vật lý';

comment on column physical_warehouses.warehouse_id is 'ID tham chiếu đến bảng user_warehouses';

comment on column physical_warehouses.address is 'Địa chỉ kho';

comment on column physical_warehouses.square_meters is 'Diện tích kho (m2)';

comment on column physical_warehouses.manager is 'Tên người quản lý kho';

comment on column physical_warehouses.contact_phone is 'Số điện thoại liên hệ';

comment on column physical_warehouses.contact_email is 'Email liên hệ';

comment on column physical_warehouses.created_at is 'Thời gian tạo';

comment on column physical_warehouses.updated_at is 'Thời gian cập nhật';

alter table physical_warehouses
    owner to root;

create table virtual_warehouses
(
    id                 serial
        primary key,
    warehouse_id       integer                                                                not null
        unique
        references user_warehouses
            on delete cascade,
    storage_path       varchar(512),
    storage_type       varchar(50),
    total_capacity     bigint,
    used_capacity      bigint,
    access_credentials jsonb,
    created_at         bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table virtual_warehouses is 'Thông tin chi tiết kho ảo';

comment on column virtual_warehouses.id is 'ID kho ảo';

comment on column virtual_warehouses.warehouse_id is 'ID tham chiếu đến bảng user_warehouses';

comment on column virtual_warehouses.storage_path is 'Đường dẫn lưu trữ';

comment on column virtual_warehouses.storage_type is 'Loại lưu trữ (ex: cloud, local)';

comment on column virtual_warehouses.total_capacity is 'Dung lượng tổng (bytes)';

comment on column virtual_warehouses.used_capacity is 'Dung lượng đã sử dụng (bytes)';

comment on column virtual_warehouses.access_credentials is 'Thông tin xác thực truy cập';

comment on column virtual_warehouses.created_at is 'Thời gian tạo';

comment on column virtual_warehouses.updated_at is 'Thời gian cập nhật';

alter table virtual_warehouses
    owner to root;

create table inventory
(
    id                 serial
        primary key,
    product_id         integer                                                                 not null
        references user_products
            on delete cascade,
    warehouse_id       integer                                                                 not null
        references user_warehouses
            on delete cascade,
    quantity           integer default 0                                                       not null,
    reserved_quantity  integer default 0,
    available_quantity integer generated always as ((quantity - reserved_quantity)) stored,
    last_count_date    timestamp,
    created_at         bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    unique (product_id, warehouse_id)
);

comment on table inventory is 'Thông tin tồn kho của sản phẩm tại các kho hàng';

comment on column inventory.id is 'ID bản ghi tồn kho';

comment on column inventory.product_id is 'ID sản phẩm';

comment on column inventory.warehouse_id is 'ID kho hàng';

comment on column inventory.quantity is 'Số lượng tổng của sản phẩm';

comment on column inventory.reserved_quantity is 'Số lượng đã được giữ trước';

comment on column inventory.available_quantity is 'Số lượng còn khả dụng (calculated)';

comment on column inventory.last_count_date is 'Ngày kiểm kê gần nhất';

comment on column inventory.created_at is 'Thời gian tạo';

comment on column inventory.updated_at is 'Thời gian cập nhật';

alter table inventory
    owner to root;

create table software_files
(
    id           serial
        primary key,
    product_id   integer                                                                 not null
        references user_products
            on delete cascade,
    warehouse_id integer                                                                 not null
        references user_warehouses
            on delete cascade,
    file_name    varchar(255)                                                            not null,
    file_size    bigint,
    file_type    varchar(50),
    checksum     varchar(128),
    is_current   boolean default false,
    upload_date  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    created_by   integer,
    created_at   bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at   bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    unique (product_id, warehouse_id)
);

comment on table software_files is 'Thông tin file phần mềm được lưu trữ trong kho';

comment on column software_files.id is 'ID file phần mềm';

comment on column software_files.product_id is 'ID sản phẩm liên quan';

comment on column software_files.warehouse_id is 'ID kho lưu file';

comment on column software_files.file_name is 'Tên file';

comment on column software_files.file_size is 'Kích thước file (bytes)';

comment on column software_files.file_type is 'Loại file (ex: zip, exe)';

comment on column software_files.checksum is 'Checksum để kiểm tra tính toàn vẹn';

comment on column software_files.is_current is 'Đánh dấu là phiên bản hiện tại hay không';

comment on column software_files.upload_date is 'Ngày tải lên';

comment on column software_files.created_by is 'Người tải lên';

comment on column software_files.created_at is 'Thời gian tạo';

comment on column software_files.updated_at is 'Thời gian cập nhật';

alter table software_files
    owner to root;

create table inventory_transactions
(
    id               serial
        primary key,
    inventory_id     integer                                                                not null
        references inventory
            on delete cascade,
    transaction_type varchar(50)                                                            not null,
    quantity         integer                                                                not null,
    reference_id     integer,
    reference_type   varchar(50),
    notes            text,
    transaction_date bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    created_by       integer,
    created_at       bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table inventory_transactions is 'Giao dịch liên quan đến tồn kho';

comment on column inventory_transactions.id is 'ID giao dịch';

comment on column inventory_transactions.inventory_id is 'ID bản ghi tồn kho liên quan';

comment on column inventory_transactions.transaction_type is 'Loại giao dịch (nhập, xuất, điều chỉnh)';

comment on column inventory_transactions.quantity is 'Số lượng thay đổi';

comment on column inventory_transactions.reference_id is 'ID tham chiếu (nếu có)';

comment on column inventory_transactions.reference_type is 'Loại tham chiếu (ví dụ: order, return)';

comment on column inventory_transactions.notes is 'Ghi chú';

comment on column inventory_transactions.transaction_date is 'Thời gian giao dịch';

comment on column inventory_transactions.created_by is 'Người tạo giao dịch';

comment on column inventory_transactions.created_at is 'Thời gian tạo bản ghi';

alter table inventory_transactions
    owner to root;

create table user_convert_customers
(
    id         bigserial
        primary key,
    avatar     varchar(255),
    name       varchar(255),
    email      jsonb,
    phone      varchar(20),
    platform   varchar(50),
    timezone   varchar(50),
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    user_id    integer
        references users,
    agent_id   uuid
        references agents,
    metadata   jsonb  default '[]'::jsonb                                             not null
);

comment on table user_convert_customers is 'Khách hàng được chuyển đổi từ nền tảng khác';

comment on column user_convert_customers.id is 'ID khách hàng';

comment on column user_convert_customers.avatar is 'Ảnh đại diện';

comment on column user_convert_customers.name is 'Tên khách hàng';

comment on column user_convert_customers.email is 'Email khách hàng (dạng JSON)';

comment on column user_convert_customers.phone is 'Số điện thoại khách hàng';

comment on column user_convert_customers.platform is 'Nền tảng nguồn (Facebook, Web,...)';

comment on column user_convert_customers.timezone is 'Múi giờ của khách hàng';

comment on column user_convert_customers.created_at is 'Thời gian tạo';

comment on column user_convert_customers.updated_at is 'Thời gian cập nhật';

comment on column user_convert_customers.user_id is 'Người dùng sở hữu khách hàng';

comment on column user_convert_customers.agent_id is 'ID agent hỗ trợ khách hàng';

comment on column user_convert_customers.metadata is 'Trường tùy chỉnh';

alter table user_convert_customers
    owner to root;

create table customer_facebook
(
    id                       bigserial
        primary key,
    page_scoped_id           varchar(20) not null
        unique,
    page_id                  varchar(255),
    name                     text,
    avatar                   varchar(255),
    gender                   varchar(45),
    user_convert_customer_id bigint
        references user_convert_customers
);

comment on table customer_facebook is 'Thông tin khách hàng đến từ Facebook';

comment on column customer_facebook.id is 'ID khách hàng Facebook';

comment on column customer_facebook.page_scoped_id is 'ID riêng của khách hàng trên page';

comment on column customer_facebook.page_id is 'ID trang Facebook';

comment on column customer_facebook.name is 'Tên người dùng';

comment on column customer_facebook.avatar is 'Ảnh đại diện';

comment on column customer_facebook.gender is 'Giới tính';

comment on column customer_facebook.user_convert_customer_id is 'Tham chiếu đến khách hàng chuyển đổi';

alter table customer_facebook
    owner to root;

create table customer_web
(
    id                       bigserial
        primary key,
    domain                   text,
    path                     text,
    device                   varchar(200),
    os                       varchar(200),
    ip                       varchar(100),
    browser                  varchar(400),
    end_session_unix         bigint,
    start_session_unix       bigint default (EXTRACT(epoch FROM now()) * (1000)::numeric),
    favicon                  varchar(500),
    user_convert_customer_id integer
        references user_convert_customers
);

comment on table customer_web is 'Thông tin khách truy cập từ website';

comment on column customer_web.id is 'ID khách truy cập web';

comment on column customer_web.domain is 'Tên miền truy cập';

comment on column customer_web.path is 'Đường dẫn trang';

comment on column customer_web.device is 'Thiết bị sử dụng';

comment on column customer_web.os is 'Hệ điều hành';

comment on column customer_web.ip is 'Địa chỉ IP';

comment on column customer_web.browser is 'Trình duyệt';

comment on column customer_web.end_session_unix is 'Thời điểm kết thúc phiên truy cập';

comment on column customer_web.start_session_unix is 'Thời điểm bắt đầu phiên truy cập';

comment on column customer_web.favicon is 'Favicon của trang web';

comment on column customer_web.user_convert_customer_id is 'ID khách hàng chuyển đổi';

alter table customer_web
    owner to root;

create table platform_customer_threads
(
    id                       bigserial
        primary key,
    platform                 varchar(50)  not null,
    user_convert_customer_id bigint
        references user_convert_customers,
    thread_id                varchar(100) not null,
    agent_id                 uuid
        references agents,
    platform_id              varchar(200)
);

comment on table platform_customer_threads is 'Quản lý luồng hội thoại của khách hàng trên các nền tảng';

comment on column platform_customer_threads.id is 'ID bản ghi luồng hội thoại';

comment on column platform_customer_threads.platform is 'Tên nền tảng (Facebook, Web...)';

comment on column platform_customer_threads.user_convert_customer_id is 'ID khách hàng chuyển đổi';

comment on column platform_customer_threads.thread_id is 'ID hội thoại trên nền tảng';

comment on column platform_customer_threads.agent_id is 'ID nhân viên phụ trách hội thoại';

comment on column platform_customer_threads.platform_id is 'ID cụ thể của nền tảng (ex: Facebook page ID)';

alter table platform_customer_threads
    owner to root;

create table user_converts
(
    id                       bigserial
        primary key,
    user_convert_customer_id bigint
        references user_convert_customers,
    user_id                  integer
        references users,
    content                  jsonb,
    created_at               bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at               bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_converts is 'Lưu lại nội dung chuyển đổi khách hàng';

comment on column user_converts.id is 'ID bản ghi chuyển đổi';

comment on column user_converts.user_convert_customer_id is 'Khách hàng được chuyển đổi';

comment on column user_converts.user_id is 'Người dùng thực hiện chuyển đổi';

comment on column user_converts.content is 'Nội dung chuyển đổi (JSON)';

comment on column user_converts.created_at is 'Thời gian tạo';

comment on column user_converts.updated_at is 'Thời gian cập nhật';

alter table user_converts
    owner to root;

create table user_orders
(
    id                       bigserial
        primary key,
    user_convert_customer_id bigint
        references user_convert_customers,
    user_id                  integer
        references users,
    product_info             jsonb,
    bill_info                jsonb,
    logistic_info            jsonb,
    created_at               bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at               bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_orders is 'Thông tin đơn hàng của khách hàng';

comment on column user_orders.id is 'ID đơn hàng';

comment on column user_orders.user_convert_customer_id is 'Khách hàng đặt đơn';

comment on column user_orders.user_id is 'Người dùng sở hữu đơn hàng';

comment on column user_orders.product_info is 'Thông tin sản phẩm (JSON)';

comment on column user_orders.bill_info is 'Thông tin hóa đơn (JSON)';

comment on column user_orders.logistic_info is 'Thông tin vận chuyển (JSON)';

comment on column user_orders.created_at is 'Thời gian tạo đơn hàng';

comment on column user_orders.updated_at is 'Thời gian cập nhật đơn hàng';

alter table user_orders
    owner to root;

create table api_llm_expense
(
    id           uuid    default uuid_generate_v4()                                      not null
        primary key,
    input_token  integer default 0,
    input_rate   integer default 1,
    output_token integer default 0,
    output_rate  integer default 1,
    cached_token integer default 0,
    cached_rate  integer default 1,
    type         varchar(20),
    metadata     jsonb,
    user_id      integer
        references users,
    create_at    bigint  default ((EXTRACT(epoch FROM now()) + (1000)::numeric))::bigint not null
);

comment on table api_llm_expense is 'Bảng lưu thông tin chi phí sử dụng api_llm của người dùng';

comment on column api_llm_expense.id is 'Mã định danh duy nhất của bản ghi chi phí (UUID)';

comment on column api_llm_expense.input_token is 'Số lượng token đầu vào đã sử dụng';

comment on column api_llm_expense.input_rate is 'Tỷ lệ quy đổi token đầu vào (mặc định là 1)';

comment on column api_llm_expense.output_token is 'Số lượng token đầu ra đã sử dụng';

comment on column api_llm_expense.output_rate is 'Tỷ lệ quy đổi token đầu ra (mặc định là 1)';

comment on column api_llm_expense.cached_token is 'Số lượng token lấy từ cache (ví dụ: từ bộ nhớ đệm AI)';

comment on column api_llm_expense.cached_rate is 'Tỷ lệ quy đổi token cache (mặc định là 1)';

comment on column api_llm_expense.metadata is 'Dữ liệu phụ bổ sung dưới dạng JSONB (ví dụ: loại model, cấu hình...)';

comment on column api_llm_expense.user_id is 'Mã người dùng liên kết, tham chiếu đến bảng users';

comment on column api_llm_expense.create_at is 'Thời điểm tạo bản ghi (UNIX timestamp - milliseconds)';

alter table api_llm_expense
    owner to root;

create table agent_products
(
    agent_id        uuid   not null
        references agents,
    user_product_id bigint not null
        references user_products,
    primary key (agent_id, user_product_id)
);

comment on table agent_products is 'Thông tin sản phảm agent quản lý';

comment on column agent_products.agent_id is 'Agent của người dùng';

comment on column agent_products.user_product_id is 'Sản phẩm của người dùng';

alter table agent_products
    owner to root;

create table routers
(
    id   serial
        primary key,
    name varchar(255) not null,
    path varchar(255) not null
);

comment on table routers is 'Bảng lưu thông tin các route hoặc page trong hệ thống';

comment on column routers.id is 'Khóa chính của bảng routers';

comment on column routers.name is 'Tên của route, dùng để hiển thị hoặc quản lý';

comment on column routers.path is 'Đường dẫn URL tương ứng với route';

alter table routers
    owner to root;

create table page_keywords
(
    id        serial
        primary key,
    keywords  jsonb   not null,
    router_id integer not null
        references routers
            on delete cascade,
    user_id   integer not null
        references users
            on delete cascade
);

comment on table page_keywords is 'Bảng lưu từ khóa liên quan đến từng trang (router) và người dùng';

comment on column page_keywords.id is 'Khóa chính của bảng page_keywords, sẽ được AI tự động tạo ra';

comment on column page_keywords.keywords is 'Danh sách từ khóa ở dạng JSON, dùng để chuyển trang';

comment on column page_keywords.router_id is 'Tham chiếu đến bảng routers, xác định trang nào chứa từ khóa này';

comment on column page_keywords.user_id is 'Tham chiếu đến bảng users';

alter table page_keywords
    owner to root;

create table folders
(
    id           serial
        primary key,
    warehouse_id integer                                                                not null
        references virtual_warehouses (warehouse_id)
            on delete cascade,
    parent_id    integer
        references folders
            on delete cascade,
    name         varchar(255)                                                           not null,
    created_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    unique (warehouse_id, parent_id, name)
);

comment on table folders is 'Các thư mục ảo trong kho';

comment on column folders.parent_id is 'Nếu NULL là thư mục gốc';

alter table folders
    owner to root;

create table files
(
    id           serial
        primary key,
    warehouse_id integer                                                                not null
        references virtual_warehouses (warehouse_id)
            on delete cascade,
    folder_id    integer
                                                                                        references folders
                                                                                            on delete set null,
    name         varchar(255)                                                           not null,
    storage_key  varchar(1024)                                                          not null,
    mime_type    varchar(100),
    extension    varchar(20),
    size_bytes   bigint,
    created_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    unique (warehouse_id, folder_id, name)
);

comment on table files is 'Metadata các file trong kho ảo';

comment on column files.storage_key is 'Đường dẫn hoặc key thực tế trên hệ thống storage';

alter table files
    owner to root;

create table admin_marketing_send_history
(
    id                   bigint not null
        constraint admin_marketing_send_history_pk
            primary key,
    send_by              integer
        constraint admin_marketing_send_history_employees_id_fk
            references employees,
    channels             varchar(20),
    message_info_content json,
    "to"                 varchar(255),
    sent_at              bigint,
    error_message        text,
    status               varchar(20)
);

comment on table admin_marketing_send_history is 'Lịch sử chăm sóc khách hàng đơn của admin';

comment on column admin_marketing_send_history.send_by is 'nhân viên gửi tin nhắn';

comment on column admin_marketing_send_history.channels is 'SMS EMAIL ZALO API';

comment on column admin_marketing_send_history.message_info_content is 'Nội dung của tin nhắn';

comment on column admin_marketing_send_history."to" is 'Người nhận';

comment on column admin_marketing_send_history.sent_at is 'Thời gian gửi';

comment on column admin_marketing_send_history.error_message is 'Thông tin lỗi';

comment on column admin_marketing_send_history.status is 'SENT FAILED QUEUED';

alter table admin_marketing_send_history
    owner to root;

create table user_marketing_send_history
(
    id                   bigint not null
        primary key,
    send_by              integer
        constraint user_marketing_send_history_employees_id_fk
            references users,
    channels             varchar(20),
    message_info_content json,
    "to"                 varchar(255),
    sent_at              bigint,
    error_message        text,
    audience_id          bigint,
    status               varchar(20)
);

comment on table user_marketing_send_history is 'Lịch sử chăm sóc khách hàng đơn của người dùng';

comment on column user_marketing_send_history.send_by is 'người dùng gửi tin nhắn';

comment on column user_marketing_send_history.channels is 'SMS EMAIL ZALO API';

comment on column user_marketing_send_history.message_info_content is 'Nội dung của tin nhắn';

comment on column user_marketing_send_history."to" is 'Người nhận';

comment on column user_marketing_send_history.sent_at is 'Thời gian gửi';

comment on column user_marketing_send_history.error_message is 'Thông tin lỗi';

comment on column user_marketing_send_history.audience_id is 'Mã audience nếu có';

comment on column user_marketing_send_history.status is 'SENT FAILED QUEUED';

alter table user_marketing_send_history
    owner to root;

create type user_type as enum ('INDIVIDUAL', 'BUSINESS');

alter type user_type owner to root;

create type gender as enum ('MALE', 'FEMALE', 'OTHER');

alter type gender owner to root;

create type auth_method_enum as enum ('PASSWORD', 'SMS', 'EMAIL', 'ZALO', 'TOTP', 'RECOVERY', 'FACEBOOK');

alter type auth_method_enum owner to root;

create type auth_status_enum as enum ('SUCCESS', 'FAILED', 'EXPIRED');

alter type auth_status_enum owner to root;

create type discount_type_enum as enum ('PERCENTAGE', 'FIXED_AMOUNT');

alter type discount_type_enum owner to root;

create type coupon_status_enum as enum ('ACTIVE', 'INACTIVE', 'EXPIRED');

alter type coupon_status_enum owner to root;

create type transaction_status as enum ('PENDING', 'CONFIRMED', 'FAILED', 'REFUNDED');

alter type transaction_status owner to root;

create type transaction_type_enum as enum ('POINT_PURCHASE', 'POINT_WITHDRAWAL');

alter type transaction_type_enum owner to root;

create type webhook_status_enum as enum ('SUCCESS', 'FAILED', 'PROCESSING');

alter type webhook_status_enum owner to root;

create type affiliate_account_status as enum ('ACTIVE', 'REJECT', 'PENDING', 'RE_REGISTER', 'STEP0', 'STEP1', 'STEP2', 'STEP3', 'STEP4');

alter type affiliate_account_status owner to root;

create type contract_type_enum as enum ('INDIVIDUAL', 'BUSINESS');

alter type contract_type_enum owner to root;

create type contract_status_enum as enum ('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED');

alter type contract_status_enum owner to root;

create type sign_method_enum as enum ('OTP', 'UPLOAD');

alter type sign_method_enum owner to root;

create type ticket_status as enum ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED');

alter type ticket_status owner to root;

create type ticket_priority as enum ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

alter type ticket_priority owner to root;

create type sender_type_enum as enum ('USER', 'STAFF');

alter type sender_type_enum owner to root;

create type subscription_status as enum ('ACTIVE', 'CANCELLED', 'EXPIRED', 'PENDING');

alter type subscription_status owner to root;

create type invoice_status as enum ('PENDING', 'PAID', 'FAILED', 'CANCELLED', 'REFUNDED');

alter type invoice_status owner to root;

create type payment_status as enum ('PENDING', 'SUCCESS', 'FAILED');

alter type payment_status owner to root;

create type package_type as enum ('TIME_ONLY', 'HYBRID');

alter type package_type owner to root;

create type point_conversion_history_status as enum ('PENDING', 'SUCCESS', 'FAILED');

alter type point_conversion_history_status owner to root;

-- Unknown how to generate base type type

alter type vector owner to root;

-- Unknown how to generate base type type

alter type halfvec owner to root;

-- Unknown how to generate base type type

alter type sparsevec owner to root;