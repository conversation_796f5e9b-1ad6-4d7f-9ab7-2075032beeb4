# Biểu <PERSON> Use Case Hệ Thống ERP

## PlantUML Version

```plantuml
@startuml ERP System Use Case

skinparam actorStyle awesome
left to right direction

' Actors
actor "<PERSON><PERSON><PERSON>" as Employee
actor "<PERSON><PERSON><PERSON><PERSON>" as Manager
actor "<PERSON><PERSON><PERSON><PERSON>" as Admin
actor "<PERSON><PERSON><PERSON><PERSON>" as Customer
actor "AI Agent" as AIAgent

' Use case systems
rectangle "Hệ Thống ERP" {
  ' Authentication
  package "Quản Lý Tà<PERSON> K<PERSON>" {
    usecase "Đăng Nhập" as UC1
    usecase "Đăng Ký" as UC2
    usecase "Quản Lý Tà<PERSON> K<PERSON>ản N<PERSON> Viên" as UC3
  }

  ' Task Management
  package "Quản Lý Công Việc" {
    usecase "Tạo Công Việc" as UC4
    usecase "Phân Công Công Việc" as UC5
    usecase "Theo Dõi Tiến Độ Công Việc" as UC6
    usecase "Báo C<PERSON>o <PERSON> Thành Công Việc" as UC7
  }

  ' OKR Management
  package "Quản Lý OKRs" {
    usecase "Tạ<PERSON> OKR<PERSON>" as UC8
    usecase "Thiết <PERSON>" as UC9
    usecase "<PERSON>á<PERSON> Kết Quả Then Chốt" as UC10
    usecase "Theo Dõi Tiến Độ OKRs" as UC11
    usecase "Đánh Giá OKRs" as UC12
  }

  ' Calendar Management
  package "Quản Lý Lịch" {
    usecase "Xem Lịch Làm Việc Cá Nhân" as UC13
    usecase "Đăng Ký Lịch Làm Việc" as UC14
    usecase "Phân Công Lịch Làm Việc" as UC15
    usecase "Quản Lý Sự Kiện" as UC16
  }

  ' Leave Management
  package "Quản Lý Nghỉ Phép" {
    usecase "Nộp Đơn Xin Nghỉ Phép" as UC17
    usecase "Phê Duyệt Đơn Nghỉ Phép" as UC18
    usecase "Xem Lịch Sử Nghỉ Phép" as UC19
  }

  ' Attendance Management
  package "Quản Lý Chấm Công" {
    usecase "Chấm Công" as UC20
    usecase "Tạo Báo Cáo Chấm Công" as UC21
    usecase "Hiệu Chỉnh Dữ Liệu Chấm Công" as UC22
  }

  ' Marketing Management
  package "Quản Lý Marketing" {
    usecase "Quản Lý Thông Tin Khách Hàng" as UC23
    usecase "Phân Khúc Khách Hàng" as UC24
    usecase "Tạo Chiến Dịch Marketing" as UC25
    usecase "Phân Tích Hiệu Quả Chiến Dịch" as UC26
  }

  ' AI Agent System
  package "Hệ Thống AI Agent" {
    usecase "Giao Tiếp Khách Hàng Qua Chat" as UC27
    usecase "Tạo Thông Báo Tự Động" as UC28
    usecase "Tạo Công Việc Tự Động" as UC29
    usecase "Quản Lý Lịch Tự Động" as UC30
  }
}

' Relationships for Authentication
Employee --> UC1
Employee --> UC2
Admin --> UC3

' Relationships for Task Management
Employee --> UC4
Manager --> UC5
Employee --> UC6
Employee --> UC7
Manager --> UC6

' Relationships for OKR Management
Manager --> UC8
Employee --> UC9
Employee --> UC10
Employee --> UC11
Manager --> UC12
Manager --> UC11

' Relationships for Calendar Management
Employee --> UC13
Employee --> UC14
Manager --> UC15
Employee --> UC16
Manager --> UC16

' Relationships for Leave Management
Employee --> UC17
Manager --> UC18
Employee --> UC19
Manager --> UC19

' Relationships for Attendance Management
Employee --> UC20
Manager --> UC21
Admin --> UC22

' Relationships for Marketing Management
Manager --> UC23
Manager --> UC24
Manager --> UC25
Manager --> UC26

' Relationships for AI Agent System
AIAgent --> UC27
AIAgent --> UC28
AIAgent --> UC29
AIAgent --> UC30
Customer --> UC27

@enduml
```

## Mermaid Version

```mermaid
graph LR
    %% Actors with styling to resemble people
    Employee["👤<br/>Nhân Viên"]
    Manager["👨‍💼<br/>Quản Lý"]
    Admin["👨‍💻<br/>Quản Trị Viên"]
    Customer["🧑‍🤝‍🧑<br/>Khách Hàng"]
    AIAgent["🤖<br/>AI Agent"]

    %% Use Cases with ellipse shape
    UC1(("Đăng Nhập"))
    UC2(("Đăng Ký"))
    UC3(("Quản Lý Tài Khoản<br/>Nhân Viên"))
    UC4(("Tạo Công Việc"))
    UC5(("Phân Công Công Việc"))
    UC6(("Theo Dõi Tiến Độ<br/>Công Việc"))
    UC7(("Báo Cáo Hoàn Thành<br/>Công Việc"))
    UC8(("Tạo Chu Kỳ OKRs"))
    UC9(("Thiết Lập Mục Tiêu"))
    UC10(("Xác Định Kết Quả<br/>Then Chốt"))
    UC11(("Theo Dõi Tiến Độ<br/>OKRs"))
    UC12(("Đánh Giá OKRs"))
    UC13(("Xem Lịch Làm Việc<br/>Cá Nhân"))
    UC14(("Đăng Ký Lịch<br/>Làm Việc"))
    UC15(("Phân Công Lịch<br/>Làm Việc"))
    UC16(("Quản Lý Sự Kiện"))
    UC17(("Nộp Đơn Xin<br/>Nghỉ Phép"))
    UC18(("Phê Duyệt Đơn<br/>Nghỉ Phép"))
    UC19(("Xem Lịch Sử<br/>Nghỉ Phép"))
    UC20(("Chấm Công"))
    UC21(("Tạo Báo Cáo<br/>Chấm Công"))
    UC22(("Hiệu Chỉnh Dữ Liệu<br/>Chấm Công"))
    UC23(("Quản Lý Thông Tin<br/>Khách Hàng"))
    UC24(("Phân Khúc<br/>Khách Hàng"))
    UC25(("Tạo Chiến Dịch<br/>Marketing"))
    UC26(("Phân Tích Hiệu Quả<br/>Chiến Dịch"))
    UC27(("Giao Tiếp Khách Hàng<br/>Qua Chat"))
    UC28(("Tạo Thông Báo<br/>Tự Động"))
    UC29(("Tạo Công Việc<br/>Tự Động"))
    UC30(("Quản Lý Lịch<br/>Tự Động"))
    
    %% Connections with thicker lines
    Employee ====> UC1
    Employee ====> UC2
    Employee ====> UC4
    Employee ====> UC6
    Employee ====> UC7
    Employee ====> UC9
    Employee ====> UC10
    Employee ====> UC11
    Employee ====> UC13
    Employee ====> UC14
    Employee ====> UC16
    Employee ====> UC17
    Employee ====> UC19
    Employee ====> UC20
    
    Manager ====> UC5
    Manager ====> UC6
    Manager ====> UC8
    Manager ====> UC11
    Manager ====> UC12
    Manager ====> UC15
    Manager ====> UC16
    Manager ====> UC18
    Manager ====> UC19
    Manager ====> UC21
    Manager ====> UC23
    Manager ====> UC24
    Manager ====> UC25
    Manager ====> UC26
    
    Admin ====> UC3
    Admin ====> UC22
    
    AIAgent ====> UC27
    AIAgent ====> UC28
    AIAgent ====> UC29
    AIAgent ====> UC30
    
    Customer ====> UC27
    
    %% Style nodes
    classDef actor fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef useCase fill:#e1f5fe,stroke:#01579b,stroke-width:2px,rx:30px,ry:30px
    
    class Employee,Manager,Admin,Customer,AIAgent actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13,UC14,UC15,UC16,UC17,UC18,UC19,UC20,UC21,UC22,UC23,UC24,UC25,UC26,UC27,UC28,UC29,UC30 useCase
```

## Mermaid Version (Simplified Style)

```mermaid
graph LR
    %% Define Actors
    Employee(["👤 Nhân Viên"])
    Manager(["👨‍💼 Quản Lý"])
    Admin(["👨‍💻 Quản Trị Viên"])
    Customer(["🧑‍🤝‍🧑 Khách Hàng"])
    AIAgent(["🤖 AI Agent"])
    
    %% HR Management Use Cases
    HR1(("Quản lý tin tuyển dụng"))
    HR2(("Quản lý hồ sơ ứng viên"))
    HR3(("Quản lý lịch phỏng vấn và đánh giá"))
    HR4(("Quản lý các yêu cầu tuyển dụng nội bộ"))
    HR5(("Phân tích và báo cáo"))
    HR6(("Giao tiếp với ứng viên"))
    HR7(("Quản lý nhân viên mới"))
    
    %% Department Manager Use Cases
    DM1(("Đề xuất nhu cầu tuyển dụng"))
    DM2(("Xem trạng thái yêu cầu tuyển dụng"))
    DM3(("Nhận thông báo về trạng thái đề xuất"))
    
    %% Applicant Use Cases
    APP1(("Giao tiếp với nhà tuyển dụng"))
    
    %% Strong connections
    Employee ===> HR1
    Employee ===> HR2
    Employee ===> HR3
    Employee ===> HR4
    Employee ===> HR5
    Employee ===> HR6
    Employee ===> HR7
    
    Manager ===> DM1
    Manager ===> DM2
    Manager ===> DM3
    
    Customer ===> APP1
    
    %% Style
    classDef actor fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef useCase fill:#fff,stroke:#333,stroke-width:2px,rx:30px,ry:30px
    
    class Employee,Manager,Admin,Customer,AIAgent actor
    class HR1,HR2,HR3,HR4,HR5,HR6,HR7,DM1,DM2,DM3,APP1 useCase
```

## Mô Tả Chi Tiết Các Use Case

### Quản Lý Tài Khoản
1. **Đăng Nhập**: Xác thực người dùng để truy cập vào hệ thống
2. **Đăng Ký**: Tạo tài khoản mới trong hệ thống
3. **Quản Lý Tài Khoản Nhân Viên**: Thêm, sửa, xóa và phân quyền cho tài khoản nhân viên

### Quản Lý Công Việc
4. **Tạo Công Việc**: Khởi tạo nhiệm vụ mới với các thông tin chi tiết
5. **Phân Công Công Việc**: Giao nhiệm vụ cho các nhân viên phù hợp
6. **Theo Dõi Tiến Độ Công Việc**: Giám sát và cập nhật trạng thái công việc
7. **Báo Cáo Hoàn Thành Công Việc**: Xác nhận và cung cấp thông tin về việc hoàn thành nhiệm vụ

### Quản Lý OKRs
8. **Tạo Chu Kỳ OKRs**: Thiết lập khung thời gian cho chu kỳ OKRs
9. **Thiết Lập Mục Tiêu**: Xác định mục tiêu cá nhân hoặc tổ chức
10. **Xác Định Kết Quả Then Chốt**: Thiết lập các chỉ số đo lường sự thành công của mục tiêu
11. **Theo Dõi Tiến Độ OKRs**: Cập nhật và giám sát tiến độ đạt được
12. **Đánh Giá OKRs**: Phân tích kết quả đạt được sau khi kết thúc chu kỳ

### Quản Lý Lịch
13. **Xem Lịch Làm Việc Cá Nhân**: Truy cập lịch trình công việc của bản thân
14. **Đăng Ký Lịch Làm Việc**: Đăng ký thời gian làm việc theo ca hoặc ngày
15. **Phân Công Lịch Làm Việc**: Sắp xếp và phân bổ lịch làm việc cho nhân viên
16. **Quản Lý Sự Kiện**: Tạo, chỉnh sửa và xóa các sự kiện trong hệ thống

### Quản Lý Nghỉ Phép
17. **Nộp Đơn Xin Nghỉ Phép**: Gửi yêu cầu nghỉ phép với thông tin thời gian và lý do
18. **Phê Duyệt Đơn Nghỉ Phép**: Xem xét và quyết định chấp thuận/từ chối đơn nghỉ phép
19. **Xem Lịch Sử Nghỉ Phép**: Tra cứu thông tin về các lần nghỉ phép trước đây

### Quản Lý Chấm Công
20. **Chấm Công**: Ghi nhận thời gian làm việc của nhân viên
21. **Tạo Báo Cáo Chấm Công**: Tổng hợp dữ liệu chấm công thành báo cáo
22. **Hiệu Chỉnh Dữ Liệu Chấm Công**: Sửa đổi thông tin chấm công khi có sai sót

### Quản Lý Marketing
23. **Quản Lý Thông Tin Khách Hàng**: Lưu trữ và cập nhật thông tin khách hàng
24. **Phân Khúc Khách Hàng**: Phân loại khách hàng theo các tiêu chí khác nhau
25. **Tạo Chiến Dịch Marketing**: Thiết lập và triển khai chiến dịch quảng bá
26. **Phân Tích Hiệu Quả Chiến Dịch**: Đánh giá kết quả và ROI của chiến dịch marketing

### Hệ Thống AI Agent
27. **Giao Tiếp Khách Hàng Qua Chat**: Tự động hóa tương tác với khách hàng thông qua chatbot
28. **Tạo Thông Báo Tự Động**: Khởi tạo thông báo dựa trên các sự kiện hoặc điều kiện
29. **Tạo Công Việc Tự Động**: Khởi tạo nhiệm vụ dựa trên các trigger định sẵn
30. **Quản Lý Lịch Tự Động**: Tự động hóa việc sắp xếp và quản lý lịch làm việc
