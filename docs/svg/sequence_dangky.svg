<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-75 -15 2859 1387.5" style="max-width: 2859px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg">
  <style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style>
  <style>
    #export-svg{font-family:arial,sans-serif;font-size:28px;fill:#333;}
    @keyframes edge-animation-frame{from{stroke-dashoffset:0;}}
    @keyframes dash{to{stroke-dashoffset:0;}}
    #export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}
    #export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}
    #export-svg .error-icon{fill:#ffffff;}
    #export-svg .error-text{fill:#000000;stroke:#000000;}
    #export-svg .edge-thickness-normal{stroke-width:6px;}
    #export-svg .edge-thickness-thick{stroke-width:6px;}
    #export-svg .edge-pattern-solid{stroke-dasharray:0;}
    #export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}
    #export-svg .edge-pattern-dashed{stroke-dasharray:3;}
    #export-svg .edge-pattern-dotted{stroke-dasharray:2;}
    #export-svg .marker{fill:#000000;stroke:#000000;}
    #export-svg .marker.cross{stroke:#000000;}
    #export-svg svg{font-family:arial,sans-serif;font-size:28px;}
    #export-svg p{margin:0;}
    #export-svg .actor{stroke:#000000;fill:#ffffff;stroke-width:5;}
    #export-svg rect.actor.outer-path[data-look="neo"]{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg rect.note[data-look="neo"]{stroke:#000000;fill:#fff5ad;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg text.actor>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .actor-line{stroke:#000000;stroke-width:4px;}
    #export-svg .messageLine0{stroke-width:6px;stroke-dasharray:none;stroke:#000000;}
    #export-svg .messageLine1{stroke-width:6px;stroke-dasharray:3,3;stroke:#000000;}
    #export-svg #arrowhead path{fill:#000000;stroke:#000000;}
    #export-svg .sequenceNumber{fill:#ffffff;}
    #export-svg #sequencenumber{fill:#333;}
    #export-svg #crosshead path{fill:#333;stroke:#333;}
    #export-svg .messageText{fill:#333;stroke:none;font-weight:700;}
    #export-svg .labelBox{stroke:#000000;fill:#ffffff;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg .labelText,#export-svg .labelText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopText,#export-svg .loopText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopLine{stroke-width:6px;stroke-dasharray:2,2;stroke:#000000;fill:#000000;}
    #export-svg .note{stroke:#000000;fill:#fff5ad;}
    #export-svg .noteText,#export-svg .noteText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .activation0{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation1{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation2{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .actorPopupMenu{position:absolute;}
    #export-svg .actorPopupMenuPanel{position:absolute;fill:#ffffff;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}
    #export-svg .actor-man circle,#export-svg line{fill:#ffffff;stroke-width:5px;}
    #export-svg g rect.rect{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke:#000000;stroke-width:5;}
    #export-svg .node .neo-node{stroke:#000000;}
    #export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node path{stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].node .outer-path{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}
    #export-svg [data-look="neo"].node circle{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node circle .state-start{fill:#000000;}
    #export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].icon-shape .icon{fill:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}
  </style>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="6" rx="6" name="Email" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1213.5" x="2484"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1262.25" x="2596.5"><tspan dy="0" x="2596.5">Dịch vụ Email</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="6" rx="6" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1213.5" x="2184"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1262.25" x="2296.5"><tspan dy="0" x="2296.5">CSDL</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="6" rx="6" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1213.5" x="1378.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1262.25" x="1491"><tspan dy="0" x="1491">Hệ thống</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="6" rx="6" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1213.5" x="618"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1262.25" x="730.5"><tspan dy="0" x="730.5">Giao diện</tspan></text>
  </g>
  <g>
    <line data-id="Email" data-et="life-line" name="Email" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1213.5" x2="2596.5" y1="97.5" x1="2596.5" id="actor54"/>
    <g data-id="Email" data-et="participant" data-look="neo" id="root-54">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="6" rx="6" name="Email" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="2484"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="2596.5"><tspan dy="0" x="2596.5">Dịch vụ Email</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="DB" data-et="life-line" name="DB" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1213.5" x2="2296.5" y1="97.5" x1="2296.5" id="actor53"/>
    <g data-id="DB" data-et="participant" data-look="neo" id="root-53">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="6" rx="6" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="2184"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="2296.5"><tspan dy="0" x="2296.5">CSDL</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="Server" data-et="life-line" name="Server" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1213.5" x2="1491" y1="97.5" x1="1491" id="actor52"/>
    <g data-id="Server" data-et="participant" data-look="neo" id="root-52">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="6" rx="6" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="1378.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="1491"><tspan dy="0" x="1491">Hệ thống</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="UI" data-et="life-line" name="UI" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1213.5" x2="730.5" y1="97.5" x1="730.5" id="actor51"/>
    <g data-id="UI" data-et="participant" data-look="neo" id="root-51">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="6" rx="6" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="618"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="730.5"><tspan dy="0" x="730.5">Giao diện</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="Guest" data-et="life-line" name="Guest" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1213.5" x2="112.5" y1="120" x1="112.5" id="actor50"/>
  </g>
  <defs>
    <symbol height="24" width="24" id="computer">
      <path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"/>
    </symbol>
  </defs>
  <defs>
    <symbol clip-rule="evenodd" fill-rule="evenodd" id="database">
      <path d="M12.258.001l.256.004.255.005...[long path data unchanged]"/>
    </symbol>
  </defs>
  <defs>
    <symbol height="24" width="24" id="clock">
      <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"/>
    </symbol>
  </defs>
  <defs>
    <marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead">
      <path d="M -1 0 L 10 5 L 0 10 z"/>
    </marker>
  </defs>
  <defs>
    <marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead">
      <path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"/>
    </marker>
  </defs>
  <defs>
    <marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head">
      <path d="M 18,7 L9,13 L14,7 L9,1 Z"/>
    </marker>
  </defs>
  <defs>
    <marker markerUnits="userSpaceOnUse" orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber">
      <circle r="10" cy="15" cx="15"/>
    </marker>
  </defs>
  <g data-id="i15" data-et="control-structure">
    <line class="loopLine" y2="472.5" x2="2613" y1="472.5" x1="714" stroke-width="6px"/>
    <line class="loopLine" y2="1183.5" x2="2613" y1="472.5" x1="2613" stroke-width="6px"/>
    <line class="loopLine" y2="1183.5" x2="2613" y1="1183.5" x1="714" stroke-width="6px"/>
    <line class="loopLine" y2="1183.5" x2="714" y1="472.5" x1="714" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="763.5" x2="2613" y1="763.5" x1="714" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="906" x2="2613" y1="906" x1="714" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1048.5" x2="2613" y1="1048.5" x1="714" stroke-width="6px"/>
    <polygon class="labelBox" points="714,472.5 789,472.5 789,514.5 776.4,525 714,525" stroke-width="5px"/>
    <text class="labelText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="502.5" x="751.5">alt</text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="499.5" x="1701"><tspan x="1701">[Email chưa tồn tại và mật khẩu hợp lệ]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="790.5" x="1663.5"><tspan x="1663.5">[Email đã tồn tại]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="933" x="1663.5"><tspan x="1663.5">[Dữ liệu không hợp lệ]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="1075.5" x="1663.5"><tspan x="1663.5">[Lỗi hệ thống]</tspan></text>
  </g>
  <g style="stroke: rgb(179, 179, 179);" data-id="Guest" data-et="participant" name="Guest" class="actor-man actor-top">
    <line y2="56.25" x2="112.5" y1="41.25" x1="112.5" id="actor-man-torso50"/>
    <line y2="47.25" x2="126" y1="47.25" x1="99" id="actor-man-arms50"/>
    <line y2="56.25" x2="112.5" y1="67.5" x1="99"/>
    <line y2="67.5" x2="124.5" y1="56.25" x1="112.5"/>
    <circle height="48.75" width="112.5" r="11.25" cy="30" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="82.5" x="112.5"><tspan dy="0" x="112.5">Khách (Guest)</tspan></text>
  </g>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="120" x="417">Chọn "Đăng ký công ty"</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="Guest" data-id="i0" data-et="message" class="messageLine0" y2="169.5" x2="720" y1="169.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="192" x="426">Hiển thị form: Tên công ty, Email, Mật khẩu</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="Guest" data-from="UI" data-id="i1" data-et="message" class="messageLine1" y2="241.5" x2="123" y1="241.5" x1="729"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="264" x="417">Nhập thông tin và nhấn "Đăng ký"</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="Guest" data-id="i2" data-et="message" class="messageLine0" y2="313.5" x2="720" y1="313.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="336" x="1107">Gửi dữ liệu đăng ký</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Server" data-from="UI" data-id="i3" data-et="message" class="messageLine0" y2="385.5" x2="1480.5" y1="385.5" x1="732"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="408" x="1890">Kiểm tra email đã tồn tại?</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="DB" data-from="Server" data-id="i4" data-et="message" class="messageLine0" y2="457.5" x2="2286" y1="457.5" x1="1492.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="547.5" x="1890">Tạo tài khoản CompanyAdmin (trạng thái "Chưa xác thực")</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="DB" data-from="Server" data-id="i6" data-et="message" class="messageLine0" y2="597" x2="2286" y1="597" x1="1492.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="619.5" x="2040">Gửi email xác thực</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Email" data-from="Server" data-id="i7" data-et="message" class="messageLine0" y2="669" x2="2586" y1="669" x1="1492.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="691.5" x="1116">Hiển thị "Đăng ký thành công, vui lòng xác thực email"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i8" data-et="message" class="messageLine1" y2="741" x2="741" y1="741" x1="1489.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="831" x="1116">Hiển thị lỗi "Email đã được đăng ký"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i10" data-et="message" class="messageLine1" y2="883.5" x2="741" y1="883.5" x1="1489.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="973.5" x="1116">Hiển thị lỗi định dạng/trường trống</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i12" data-et="message" class="messageLine1" y2="1026" x2="741" y1="1026" x1="1489.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1116" x="1116">Hiển thị "Lỗi hệ thống, vui lòng thử lại sau"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i14" data-et="message" class="messageLine1" y2="1168.5" x2="741" y1="1168.5" x1="1489.5"/>
  <g style="stroke: rgb(179, 179, 179);" name="Guest" class="actor-man actor-bottom">
    <line y2="1269.75" x2="112.5" y1="1254.75" x1="112.5" id="actor-man-torso54"/>
    <line y2="1260.75" x2="126" y1="1260.75" x1="99" id="actor-man-arms54"/>
    <line y2="1269.75" x2="112.5" y1="1281" x1="99"/>
    <line y2="1281" x2="124.5" y1="1269.75" x1="112.5"/>
    <circle height="24.375" width="112.5" r="11.25" cy="1243.5" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1296" x="112.5"><tspan dy="0" x="112.5">Khách (Guest)</tspan></text>
  </g>
</svg>