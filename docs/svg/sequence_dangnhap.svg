<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-75 -15 2431.5 1680" style="max-width: 2431.5px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg">
  <style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style>
  <style>
    #export-svg{font-family:arial,sans-serif;font-size:28px;fill:#333;}
    @keyframes edge-animation-frame{from{stroke-dashoffset:0;}}
    @keyframes dash{to{stroke-dashoffset:0;}}
    #export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}
    #export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}
    #export-svg .error-icon{fill:#ffffff;}
    #export-svg .error-text{fill:#000000;stroke:#000000;}
    #export-svg .edge-thickness-normal{stroke-width:6px;}
    #export-svg .edge-thickness-thick{stroke-width:6px;}
    #export-svg .edge-pattern-solid{stroke-dasharray:0;}
    #export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}
    #export-svg .edge-pattern-dashed{stroke-dasharray:3;}
    #export-svg .edge-pattern-dotted{stroke-dasharray:2;}
    #export-svg .marker{fill:#000000;stroke:#000000;}
    #export-svg .marker.cross{stroke:#000000;}
    #export-svg svg{font-family:arial,sans-serif;font-size:28px;}
    #export-svg p{margin:0;}
    #export-svg .actor{stroke:#000000;fill:#ffffff;stroke-width:5;}
    #export-svg rect.actor.outer-path[data-look="neo"]{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg rect.note[data-look="neo"]{stroke:#000000;fill:#fff5ad;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg text.actor>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .actor-line{stroke:#000000;stroke-width:4px;}
    #export-svg .messageLine0{stroke-width:6px;stroke-dasharray:none;stroke:#000000;}
    #export-svg .messageLine1{stroke-width:6px;stroke-dasharray:3,3;stroke:#000000;}
    #export-svg #arrowhead path{fill:#000000;stroke:#000000;}
    #export-svg .sequenceNumber{fill:#ffffff;}
    #export-svg #sequencenumber{fill:#333;}
    #export-svg #crosshead path{fill:#333;stroke:#333;}
    #export-svg .messageText{fill:#333;stroke:none;font-weight:700;}
    #export-svg .labelBox{stroke:#000000;fill:#ffffff;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg .labelText,#export-svg .labelText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopText,#export-svg .loopText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopLine{stroke-width:6px;stroke-dasharray:2,2;stroke:#000000;fill:#000000;}
    #export-svg .note{stroke:#000000;fill:#fff5ad;}
    #export-svg .noteText,#export-svg .noteText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .activation0{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation1{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation2{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .actorPopupMenu{position:absolute;}
    #export-svg .actorPopupMenuPanel{position:absolute;fill:#ffffff;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}
    #export-svg .actor-man circle,#export-svg line{fill:#ffffff;stroke-width:5px;}
    #export-svg g rect.rect{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke:#000000;stroke-width:5;}
    #export-svg .node .neo-node{stroke:#000000;}
    #export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node path{stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].node .outer-path{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}
    #export-svg [data-look="neo"].node circle{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node circle .state-start{fill:#000000;}
    #export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].icon-shape .icon{fill:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}
  </style>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="Auth" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="2056.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="2169"><tspan dy="0" x="2169">Dịch vụ xác thực</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="1756.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="1869"><tspan dy="0" x="1869">CSDL</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="1294.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="1407"><tspan dy="0" x="1407">Hệ thống</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="537"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="649.5"><tspan dy="0" x="649.5">Giao diện</tspan></text>
  </g>
  <g>
    <line data-id="Auth" data-et="life-line" name="Auth" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="2169" y1="97.5" x1="2169" id="actor64"/>
    <g data-id="Auth" data-et="participant" data-look="neo" id="root-64">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="Auth" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="2056.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="2169"><tspan dy="0" x="2169">Dịch vụ xác thực</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="DB" data-et="life-line" name="DB" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="1869" y1="97.5" x1="1869" id="actor63"/>
    <g data-id="DB" data-et="participant" data-look="neo" id="root-63">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="1756.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="1869"><tspan dy="0" x="1869">CSDL</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="Server" data-et="life-line" name="Server" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="1407" y1="97.5" x1="1407" id="actor62"/>
    <g data-id="Server" data-et="participant" data-look="neo" id="root-62">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="1294.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="1407"><tspan dy="0" x="1407">Hệ thống</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="UI" data-et="life-line" name="UI" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="649.5" y1="97.5" x1="649.5" id="actor61"/>
    <g data-id="UI" data-et="participant" data-look="neo" id="root-61">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="537"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="649.5"><tspan dy="0" x="649.5">Giao diện</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="User" data-et="life-line" name="User" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="112.5" y1="120" x1="112.5" id="actor60"/>
  </g>
  <defs>
    <symbol height="36" width="36" id="computer">
      <path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.75)"/>
    </symbol>
  </defs>
  <defs>
    <symbol clip-rule="evenodd" fill-rule="evenodd" id="database">
      <path d="M12.258.001l.256.004.255.005...[long path data unchanged]"/>
    </symbol>
  </defs>
  <defs>
    <symbol height="36" width="36" id="clock">
      <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.75)"/>
    </symbol>
  </defs>
  <defs>
    <marker orient="auto-start-reverse" markerHeight="18" markerWidth="18" markerUnits="userSpaceOnUse" refY="7.5" refX="11.85" id="arrowhead">
      <path d="M -1.5 0 L 15 7.5 L 0 15 z"/>
    </marker>
  </defs>
  <defs>
    <marker refY="6.75" refX="6" orient="auto" markerHeight="12" markerWidth="22.5" id="crosshead">
      <path style="stroke-dasharray: 0, 0;" d="M 1.5,3 L 9,10.5 M 9,3 L 1.5,10.5" stroke-width="1.5pt" stroke="#000000" fill="none"/>
    </marker>
  </defs>
  <defs>
    <marker orient="auto" markerHeight="42" markerWidth="30" refY="10.5" refX="23.25" id="filled-head">
      <path d="M 27,10.5 L13.5,19.5 L21,10.5 L13.5,1.5 Z"/>
    </marker>
  </defs>
  <defs>
    <marker markerUnits="userSpaceOnUse" orient="auto" markerHeight="60" markerWidth="90" refY="22.5" refX="22.5" id="sequencenumber">
      <circle r="15" cy="22.5" cx="22.5"/>
    </marker>
  </defs>
  <g data-id="i15" data-et="control-structure">
    <line class="loopLine" y2="612" x2="2185.5" y1="612" x1="633" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="2185.5" y1="612" x1="2185.5" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="2185.5" y1="1174.5" x1="633" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="633" y1="612" x1="633" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="903" x2="2185.5" y1="903" x1="633" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1042.5" x2="2185.5" y1="1042.5" x1="633" stroke-width="6px"/>
    <polygon class="labelBox" points="633,612 708,612 708,654 695.4,664.5 633,664.5" stroke-width="5px"/>
    <text class="labelText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="642" x="670.5">alt</text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="639" x="1446.75"><tspan x="1446.75">[Mật khẩu đúng và đã xác thực email]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="930" x="1409.25"><tspan x="1409.25">[Tài khoản chưa xác thực email]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="1069.5" x="1409.25"><tspan x="1409.25">[Mật khẩu sai]</tspan></text>
  </g>
  <g data-id="i18" data-et="control-structure">
    <line class="loopLine" y2="472.5" x2="2200.5" y1="472.5" x1="618" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="2200.5" y1="472.5" x1="2200.5" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="2200.5" y1="1329" x1="618" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="618" y1="472.5" x1="618" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1197" x2="2200.5" y1="1197" x1="618" stroke-width="6px"/>
    <polygon class="labelBox" points="618,472.5 693,472.5 693,514.5 680.4,525 618,525" stroke-width="5px"/>
    <text class="labelText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="502.5" x="655.5">alt</text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="499.5" x="1446.75"><tspan x="1446.75">[Tài khoản tồn tại]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="1224" x="1409.25"><tspan x="1409.25">[Không tìm thấy tài khoản]</tspan></text>
  </g>
  <g data-id="i19" data-et="note">
    <rect data-look="neo" class="note" height="57" width="832.5" stroke="#000000" fill="#fff5ad" y="1344" x="612"/>
    <text dy="1em" class="noteText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1351.5" x="1029"><tspan x="1029">Nếu lỗi kết nối DB hoặc dịch vụ xác thực</tspan></text>
  </g>
  <g style="stroke: rgb(179, 179, 179);" data-id="User" data-et="participant" name="User" class="actor-man actor-top">
    <line y2="56.25" x2="112.5" y1="41.25" x1="112.5" id="actor-man-torso60"/>
    <line y2="47.25" x2="126" y1="47.25" x1="99" id="actor-man-arms60"/>
    <line y2="56.25" x2="112.5" y1="67.5" x1="99"/>
    <line y2="67.5" x2="124.5" y1="56.25" x1="112.5"/>
    <circle height="48.75" width="112.5" r="11.25" cy="30" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="82.5" x="112.5"><tspan dy="0" x="112.5">Người dùng</tspan></text>
  </g>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="120" x="376.5">Chọn "Đăng nhập"</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="User" data-id="i0" data-et="message" class="messageLine0" y2="169.5" x2="639" y1="169.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="192" x="385.5">Hiển thị form nhập Email + Mật khẩu</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="User" data-from="UI" data-id="i1" data-et="message" class="messageLine1" y2="241.5" x2="123" y1="241.5" x1="648"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="264" x="376.5">Nhập Email và Mật khẩu</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="User" data-id="i2" data-et="message" class="messageLine0" y2="313.5" x2="639" y1="313.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="336" x="1024.5">Gửi thông tin đăng nhập</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Server" data-from="UI" data-id="i3" data-et="message" class="messageLine0" y2="385.5" x2="1396.5" y1="385.5" x1="651"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="408" x="1633.5">Kiểm tra tài khoản theo email</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="DB" data-from="Server" data-id="i4" data-et="message" class="messageLine0" y2="457.5" x2="1858.5" y1="457.5" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="547.5" x="1783.5">Xác thực mật khẩu</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Auth" data-from="Server" data-id="i6" data-et="message" class="messageLine0" y2="597" x2="2158.5" y1="597" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="687" x="1783.5">Sinh token/session</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Auth" data-from="Server" data-id="i8" data-et="message" class="messageLine0" y2="736.5" x2="2158.5" y1="736.5" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="759" x="1792.5">Trả token</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="Server" data-from="Auth" data-id="i9" data-et="message" class="messageLine1" y2="808.5" x2="1417.5" y1="808.5" x1="2167.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="831" x="1033.5">Chuyển vào dashboard</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i10" data-et="message" class="messageLine1" y2="880.5" x2="660" y1="880.5" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="970.5" x="1033.5">Hiển thị "Vui lòng xác thực email trước khi đăng nhập"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i12" data-et="message" class="messageLine1" y2="1020" x2="660" y1="1020" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1110" x="1033.5">Hiển thị "Email hoặc mật khẩu không đúng"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i14" data-et="message" class="messageLine1" y2="1159.5" x2="660" y1="1159.5" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1264.5" x="1033.5">Hiển thị "Email hoặc mật khẩu không đúng"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i17" data-et="message" class="messageLine1" y2="1314" x2="660" y1="1314" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1423.5" x="1033.5">Hiển thị "Lỗi hệ thống, vui lòng thử lại sau"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i20" data-et="message" class="messageLine1" y2="1476" x2="660" y1="1476" x1="1405.5"/>
  <g style="stroke: rgb(179, 179, 179);" name="User" class="actor-man actor-bottom">
    <line y2="1562.25" x2="112.5" y1="1547.25" x1="112.5" id="actor-man-torso64"/>
    <line y2="1553.25" x2="126" y1="1553.25" x1="99" id="actor-man-arms64"/>
    <line y2="1562.25" x2="112.5" y1="1573.5" x1="99"/>
    <line y2="1573.5" x2="124.5" y1="1562.25" x1="112.5"/>
    <circle height="24.375" width="112.5" r="11.25" cy="1536" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1588.5" x="112.5"><tspan dy="0" x="112.5">Người dùng</tspan></text>
  </g>
</svg><svg aria-roledescription="sequence" role="graphics-document document" viewBox="-75 -15 2431.5 1680" style="max-width: 2431.5px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg">
  <style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style>
  <style>
    #export-svg{font-family:arial,sans-serif;font-size:28px;fill:#333;}
    @keyframes edge-animation-frame{from{stroke-dashoffset:0;}}
    @keyframes dash{to{stroke-dashoffset:0;}}
    #export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}
    #export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}
    #export-svg .error-icon{fill:#ffffff;}
    #export-svg .error-text{fill:#000000;stroke:#000000;}
    #export-svg .edge-thickness-normal{stroke-width:6px;}
    #export-svg .edge-thickness-thick{stroke-width:6px;}
    #export-svg .edge-pattern-solid{stroke-dasharray:0;}
    #export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}
    #export-svg .edge-pattern-dashed{stroke-dasharray:3;}
    #export-svg .edge-pattern-dotted{stroke-dasharray:2;}
    #export-svg .marker{fill:#000000;stroke:#000000;}
    #export-svg .marker.cross{stroke:#000000;}
    #export-svg svg{font-family:arial,sans-serif;font-size:28px;}
    #export-svg p{margin:0;}
    #export-svg .actor{stroke:#000000;fill:#ffffff;stroke-width:5;}
    #export-svg rect.actor.outer-path[data-look="neo"]{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg rect.note[data-look="neo"]{stroke:#000000;fill:#fff5ad;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg text.actor>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .actor-line{stroke:#000000;stroke-width:4px;}
    #export-svg .messageLine0{stroke-width:6px;stroke-dasharray:none;stroke:#000000;}
    #export-svg .messageLine1{stroke-width:6px;stroke-dasharray:3,3;stroke:#000000;}
    #export-svg #arrowhead path{fill:#000000;stroke:#000000;}
    #export-svg .sequenceNumber{fill:#ffffff;}
    #export-svg #sequencenumber{fill:#333;}
    #export-svg #crosshead path{fill:#333;stroke:#333;}
    #export-svg .messageText{fill:#333;stroke:none;font-weight:700;}
    #export-svg .labelBox{stroke:#000000;fill:#ffffff;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke-width:5;}
    #export-svg .labelText,#export-svg .labelText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopText,#export-svg .loopText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .loopLine{stroke-width:6px;stroke-dasharray:2,2;stroke:#000000;fill:#000000;}
    #export-svg .note{stroke:#000000;fill:#fff5ad;}
    #export-svg .noteText,#export-svg .noteText>tspan{fill:#333;stroke:none;font-weight:700;}
    #export-svg .activation0{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation1{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .activation2{fill:hsl(-120, 0%, 80%);stroke:#000000;}
    #export-svg .actorPopupMenu{position:absolute;}
    #export-svg .actorPopupMenuPanel{position:absolute;fill:#ffffff;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}
    #export-svg .actor-man circle,#export-svg line{fill:#ffffff;stroke-width:5px;}
    #export-svg g rect.rect{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));stroke:#000000;stroke-width:5;}
    #export-svg .node .neo-node{stroke:#000000;}
    #export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node path{stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].node .outer-path{filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}
    #export-svg [data-look="neo"].node circle{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].node circle .state-start{fill:#000000;}
    #export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:#000000;stroke-width:5;}
    #export-svg [data-look="neo"].icon-shape .icon{fill:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:#000000;filter:drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.25));}
    #export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}
  </style>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="Auth" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="2056.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="2169"><tspan dy="0" x="2169">Dịch vụ xác thực</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="1756.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="1869"><tspan dy="0" x="1869">CSDL</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="1294.5"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="1407"><tspan dy="0" x="1407">Hệ thống</tspan></text>
  </g>
  <g>
    <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-bottom" ry="9" rx="9" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="1506" x="537"/>
    <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1554.75" x="649.5"><tspan dy="0" x="649.5">Giao diện</tspan></text>
  </g>
  <g>
    <line data-id="Auth" data-et="life-line" name="Auth" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="2169" y1="97.5" x1="2169" id="actor64"/>
    <g data-id="Auth" data-et="participant" data-look="neo" id="root-64">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="Auth" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="2056.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="2169"><tspan dy="0" x="2169">Dịch vụ xác thực</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="DB" data-et="life-line" name="DB" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="1869" y1="97.5" x1="1869" id="actor63"/>
    <g data-id="DB" data-et="participant" data-look="neo" id="root-63">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="DB" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="1756.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="1869"><tspan dy="0" x="1869">CSDL</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="Server" data-et="life-line" name="Server" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="1407" y1="97.5" x1="1407" id="actor62"/>
    <g data-id="Server" data-et="participant" data-look="neo" id="root-62">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="Server" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="1294.5"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="1407"><tspan dy="0" x="1407">Hệ thống</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="UI" data-et="life-line" name="UI" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="649.5" y1="97.5" x1="649.5" id="actor61"/>
    <g data-id="UI" data-et="participant" data-look="neo" id="root-61">
      <rect filter="url(#drop-shadow)" data-look="neo" class="actor actor-top" ry="9" rx="9" name="UI" height="97.5" width="225" stroke="#000000" fill="#eaeaea" y="0" x="537"/>
      <text class="actor actor-box" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="48.75" x="649.5"><tspan dy="0" x="649.5">Giao diện</tspan></text>
    </g>
  </g>
  <g>
    <line data-id="User" data-et="life-line" name="User" stroke="#000000" stroke-width="4px" class="actor-line 200" y2="1506" x2="112.5" y1="120" x1="112.5" id="actor60"/>
  </g>
  <defs>
    <symbol height="36" width="36" id="computer">
      <path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.75)"/>
    </symbol>
  </defs>
  <defs>
    <symbol clip-rule="evenodd" fill-rule="evenodd" id="database">
      <path d="M12.258.001l.256.004.255.005...[long path data unchanged]"/>
    </symbol>
  </defs>
  <defs>
    <symbol height="36" width="36" id="clock">
      <path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.75)"/>
    </symbol>
  </defs>
  <defs>
    <marker orient="auto-start-reverse" markerHeight="18" markerWidth="18" markerUnits="userSpaceOnUse" refY="7.5" refX="11.85" id="arrowhead">
      <path d="M -1.5 0 L 15 7.5 L 0 15 z"/>
    </marker>
  </defs>
  <defs>
    <marker refY="6.75" refX="6" orient="auto" markerHeight="12" markerWidth="22.5" id="crosshead">
      <path style="stroke-dasharray: 0, 0;" d="M 1.5,3 L 9,10.5 M 9,3 L 1.5,10.5" stroke-width="1.5pt" stroke="#000000" fill="none"/>
    </marker>
  </defs>
  <defs>
    <marker orient="auto" markerHeight="42" markerWidth="30" refY="10.5" refX="23.25" id="filled-head">
      <path d="M 27,10.5 L13.5,19.5 L21,10.5 L13.5,1.5 Z"/>
    </marker>
  </defs>
  <defs>
    <marker markerUnits="userSpaceOnUse" orient="auto" markerHeight="60" markerWidth="90" refY="22.5" refX="22.5" id="sequencenumber">
      <circle r="15" cy="22.5" cx="22.5"/>
    </marker>
  </defs>
  <g data-id="i15" data-et="control-structure">
    <line class="loopLine" y2="612" x2="2185.5" y1="612" x1="633" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="2185.5" y1="612" x1="2185.5" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="2185.5" y1="1174.5" x1="633" stroke-width="6px"/>
    <line class="loopLine" y2="1174.5" x2="633" y1="612" x1="633" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="903" x2="2185.5" y1="903" x1="633" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1042.5" x2="2185.5" y1="1042.5" x1="633" stroke-width="6px"/>
    <polygon class="labelBox" points="633,612 708,612 708,654 695.4,664.5 633,664.5" stroke-width="5px"/>
    <text class="labelText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="642" x="670.5">alt</text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="639" x="1446.75"><tspan x="1446.75">[Mật khẩu đúng và đã xác thực email]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="930" x="1409.25"><tspan x="1409.25">[Tài khoản chưa xác thực email]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="1069.5" x="1409.25"><tspan x="1409.25">[Mật khẩu sai]</tspan></text>
  </g>
  <g data-id="i18" data-et="control-structure">
    <line class="loopLine" y2="472.5" x2="2200.5" y1="472.5" x1="618" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="2200.5" y1="472.5" x1="2200.5" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="2200.5" y1="1329" x1="618" stroke-width="6px"/>
    <line class="loopLine" y2="1329" x2="618" y1="472.5" x1="618" stroke-width="6px"/>
    <line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1197" x2="2200.5" y1="1197" x1="618" stroke-width="6px"/>
    <polygon class="labelBox" points="618,472.5 693,472.5 693,514.5 680.4,525 618,525" stroke-width="5px"/>
    <text class="labelText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="502.5" x="655.5">alt</text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="499.5" x="1446.75"><tspan x="1446.75">[Tài khoản tồn tại]</tspan></text>
    <text class="loopText" style="font-size: 28px; font-weight: 700;" text-anchor="middle" y="1224" x="1409.25"><tspan x="1409.25">[Không tìm thấy tài khoản]</tspan></text>
  </g>
  <g data-id="i19" data-et="note">
    <rect data-look="neo" class="note" height="57" width="832.5" stroke="#000000" fill="#fff5ad" y="1344" x="612"/>
    <text dy="1em" class="noteText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1351.5" x="1029"><tspan x="1029">Nếu lỗi kết nối DB hoặc dịch vụ xác thực</tspan></text>
  </g>
  <g style="stroke: rgb(179, 179, 179);" data-id="User" data-et="participant" name="User" class="actor-man actor-top">
    <line y2="56.25" x2="112.5" y1="41.25" x1="112.5" id="actor-man-torso60"/>
    <line y2="47.25" x2="126" y1="47.25" x1="99" id="actor-man-arms60"/>
    <line y2="56.25" x2="112.5" y1="67.5" x1="99"/>
    <line y2="67.5" x2="124.5" y1="56.25" x1="112.5"/>
    <circle height="48.75" width="112.5" r="11.25" cy="30" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="82.5" x="112.5"><tspan dy="0" x="112.5">Người dùng</tspan></text>
  </g>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="120" x="376.5">Chọn "Đăng nhập"</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="User" data-id="i0" data-et="message" class="messageLine0" y2="169.5" x2="639" y1="169.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="192" x="385.5">Hiển thị form nhập Email + Mật khẩu</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="User" data-from="UI" data-id="i1" data-et="message" class="messageLine1" y2="241.5" x2="123" y1="241.5" x1="648"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="264" x="376.5">Nhập Email và Mật khẩu</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="UI" data-from="User" data-id="i2" data-et="message" class="messageLine0" y2="313.5" x2="639" y1="313.5" x1="114"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="336" x="1024.5">Gửi thông tin đăng nhập</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Server" data-from="UI" data-id="i3" data-et="message" class="messageLine0" y2="385.5" x2="1396.5" y1="385.5" x1="651"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="408" x="1633.5">Kiểm tra tài khoản theo email</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="DB" data-from="Server" data-id="i4" data-et="message" class="messageLine0" y2="457.5" x2="1858.5" y1="457.5" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="547.5" x="1783.5">Xác thực mật khẩu</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Auth" data-from="Server" data-id="i6" data-et="message" class="messageLine0" y2="597" x2="2158.5" y1="597" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="687" x="1783.5">Sinh token/session</text>
  <line style="fill: none;" marker-end="url(#arrowhead)" stroke="#000000" stroke-width="6" data-to="Auth" data-from="Server" data-id="i8" data-et="message" class="messageLine0" y2="736.5" x2="2158.5" y1="736.5" x1="1408.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="759" x="1792.5">Trả token</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="Server" data-from="Auth" data-id="i9" data-et="message" class="messageLine1" y2="808.5" x2="1417.5" y1="808.5" x1="2167.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="831" x="1033.5">Chuyển vào dashboard</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i10" data-et="message" class="messageLine1" y2="880.5" x2="660" y1="880.5" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="970.5" x="1033.5">Hiển thị "Vui lòng xác thực email trước khi đăng nhập"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i12" data-et="message" class="messageLine1" y2="1020" x2="660" y1="1020" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1110" x="1033.5">Hiển thị "Email hoặc mật khẩu không đúng"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i14" data-et="message" class="messageLine1" y2="1159.5" x2="660" y1="1159.5" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1264.5" x="1033.5">Hiển thị "Email hoặc mật khẩu không đúng"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i17" data-et="message" class="messageLine1" y2="1314" x2="660" y1="1314" x1="1405.5"/>
  <text dy="1em" class="messageText" style="font-size: 28px; font-weight: 700;" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1423.5" x="1033.5">Hiển thị "Lỗi hệ thống, vui lòng thử lại sau"</text>
  <line marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;" stroke="#000000" stroke-width="6" data-to="UI" data-from="Server" data-id="i20" data-et="message" class="messageLine1" y2="1476" x2="660" y1="1476" x1="1405.5"/>
  <g style="stroke: rgb(179, 179, 179);" name="User" class="actor-man actor-bottom">
    <line y2="1562.25" x2="112.5" y1="1547.25" x1="112.5" id="actor-man-torso64"/>
    <line y2="1553.25" x2="126" y1="1553.25" x1="99" id="actor-man-arms64"/>
    <line y2="1562.25" x2="112.5" y1="1573.5" x1="99"/>
    <line y2="1573.5" x2="124.5" y1="1562.25" x1="112.5"/>
    <circle height="24.375" width="112.5" r="11.25" cy="1536" cx="112.5"/>
    <text class="actor actor-man" alignment-baseline="central" dominant-baseline="central" style="text-anchor: middle; font-size: 28px; font-weight: 700;" y="1588.5" x="112.5"><tspan dy="0" x="112.5">Người dùng</tspan></text>
  </g>
</svg>