# 📋 Kế Hoạch Triển Khai Module Quản Lý Tài Liệu Công Ty

## 🎯 Tổng Quan Dự Án

### Mục Tiêu
Xây dựng module quản lý tài liệu công ty với khả năng RAG sử dụng OpenAI Vector Store API, tích hợp với hệ thống S3 hiện có.

### Thời Gian Dự Kiến
**Tổng thời gian**: 4-5 tuầ<PERSON> (20-25 ngày làm việc)

### Công Nghệ Sử dụng
- **Backend**: NestJS, TypeORM, PostgreSQL
- **Storage**: AWS S3 (hiện có)
- **AI**: OpenAI GPT-4, OpenAI Vector Store API, OpenAI Files API
- **Database**: PostgreSQL với pgvector extension
- **Queue**: Bull/BullMQ cho background jobs

---

## 📅 Phase 1: Chuẩn Bị và Thiết Lập <PERSON> (3-4 ngày)

### Day 1: Thiết Lập Database và Entities

#### ✅ Tasks
- [ ] **Tạo migration files cho các bảng**
  - `documents` table với OpenAI integration fields
  - `document_folders` table với hierarchical structure
  - `document_permissions` table với multi-level permissions
  - `document_access_logs` table với RAG query logging
  - `document_upload_sessions` table với S3 integration
  - `openai_vector_stores` table cho tenant management

- [ ] **Tạo TypeORM Entities**
  - `Document.entity.ts` - Bao gồm openaiFileId, openaiVectorStoreId
  - `DocumentFolder.entity.ts` - Hierarchical folder structure
  - `DocumentPermission.entity.ts` - User/Role/Department permissions
  - `DocumentAccessLog.entity.ts` - Activity tracking
  - `DocumentUploadSession.entity.ts` - S3 upload sessions
  - `OpenAIVectorStore.entity.ts` - Vector store management

- [ ] **Thiết lập relationships và indexes**
  - Foreign key relationships
  - Performance indexes
  - Unique constraints

#### 📝 Deliverables
- Migration files hoàn chỉnh với tenant isolation
- Entity classes với đầy đủ decorators và relationships
- Database schema được tạo và test thành công

---

### Day 2: Tạo Cấu Trúc Module và Repositories

#### ✅ Tasks
- [ ] **Tạo module structure**
  ```
  src/modules/documents/
  ├── controllers/
  ├── services/
  ├── repositories/
  ├── entities/
  ├── dtos/
  ├── interfaces/
  ├── enums/
  ├── guards/
  ├── jobs/
  └── documents.module.ts
  ```

- [ ] **Tạo base repositories với tenant isolation**
  - `DocumentRepository` - CRUD với permission filtering
  - `DocumentFolderRepository` - Hierarchical queries
  - `DocumentPermissionRepository` - Permission management
  - `DocumentAccessLogRepository` - Activity logging
  - `DocumentUploadSessionRepository` - Upload session management
  - `OpenAIVectorStoreRepository` - Vector store tracking

- [ ] **Tạo enums và interfaces**
  - `DocumentType.enum.ts` - policy, procedure, guideline, manual
  - `PermissionLevel.enum.ts` - read, write, admin
  - `UploadStatus.enum.ts` - pending, uploaded, processing, completed, failed
  - `ProcessingStatus.enum.ts` - OpenAI processing states

#### 📝 Deliverables
- Module structure hoàn chỉnh theo chuẩn NestJS
- Base repositories với tenant isolation
- Enums và interfaces cơ bản

---

### Day 3: DTOs và Validation

#### ✅ Tasks
- [ ] **Tạo Request DTOs với validation**
  - `CreateDocumentDto` - Document creation với file info
  - `UpdateDocumentDto` - Document updates
  - `CreateFolderDto` - Folder creation với hierarchy
  - `UpdateFolderDto` - Folder updates
  - `UploadRequestDto` - S3 upload request
  - `RagQueryDto` - RAG search parameters
  - `SearchDocumentDto` - Traditional search
  - `PermissionDto` - Permission management

- [ ] **Tạo Response DTOs**
  - `DocumentResponseDto` - Document với metadata
  - `FolderResponseDto` - Folder với children count
  - `UploadSessionResponseDto` - Upload session với presigned URL
  - `RagResultResponseDto` - RAG results với sources
  - `SearchResultResponseDto` - Search results với pagination

- [ ] **Validation và Swagger documentation**
  - Class-validator decorators
  - Custom validators cho file types
  - Swagger API documentation annotations

#### 📝 Deliverables
- DTOs hoàn chỉnh với comprehensive validation
- Swagger documentation cho tất cả endpoints
- Custom validators cho business rules

---

### Day 4: OpenAI Services Integration

#### ✅ Tasks
- [ ] **Mở rộng OpenAI Service hiện có**
  - Vector Store methods: create, delete, list
  - Files API methods: upload, delete, retrieve
  - Assistant API methods: create, query, manage

- [ ] **Tạo OpenAI Vector Store Service**
  - `createVectorStoreForTenant()` - Tạo vector store per tenant
  - `getOrCreateVectorStoreForTenant()` - Get existing hoặc tạo mới
  - `addFileToVectorStore()` - Thêm file vào vector store
  - `removeFileFromVectorStore()` - Xóa file khỏi vector store
  - `updateVectorStoreStats()` - Cập nhật thống kê usage

- [ ] **Tạo OpenAI File Service**
  - `uploadFileFromS3()` - Upload file từ S3 lên OpenAI
  - `deleteFile()` - Xóa file khỏi OpenAI
  - `getFileInfo()` - Lấy thông tin file
  - `validateFileFormat()` - Validate file format cho OpenAI

- [ ] **Error handling và retry logic**
  - OpenAI API rate limiting
  - Network error handling
  - Exponential backoff retry

#### 📝 Deliverables
- OpenAI services mở rộng với full functionality
- Error handling và retry mechanisms
- Unit tests cho OpenAI integration

---

## 📅 Phase 2: Core Features Implementation (5-6 ngày)

### Day 5-6: Document Upload System với S3

#### ✅ Tasks
- [ ] **Document Upload Service**
  - `createUploadSession()` - Tạo presigned URL từ S3Service
  - `confirmUpload()` - Xác nhận upload và trigger processing
  - `validateFileUpload()` - Validate file type, size, permissions
  - `generateS3Key()` - Tạo unique S3 key với tenant isolation
  - `cleanupExpiredSessions()` - Cleanup expired upload sessions

- [ ] **Document Upload Controller**
  - `POST /api/documents/upload/request` - Request upload URL
  - `POST /api/documents/upload/confirm` - Confirm upload completion
  - `GET /api/documents/upload/status/:sessionId` - Check processing status
  - `DELETE /api/documents/upload/cancel/:sessionId` - Cancel upload

- [ ] **S3 Integration**
  - Sử dụng `S3Service.generatePresignedUrl()` hiện có
  - Verify file existence sau upload
  - Generate download URLs với expiration
  - Handle S3 errors và fallbacks

#### 📝 Deliverables
- Complete upload system với S3 integration
- Upload session management
- API endpoints với proper error handling

---

### Day 7-8: Document Processing và OpenAI Integration

#### ✅ Tasks
- [ ] **Document Processor Service**
  - `processDocument()` - Main processing pipeline
  - `extractTextContent()` - Extract text từ PDF, DOCX, TXT
  - `uploadToOpenAI()` - Upload file lên OpenAI Files API
  - `addToVectorStore()` - Thêm file vào OpenAI Vector Store
  - `handleProcessingError()` - Error handling và retry

- [ ] **Background Job System**
  - Setup Bull/BullMQ với Redis
  - `DocumentProcessorJob` - Process uploaded documents
  - `CleanupExpiredSessionsJob` - Cleanup expired sessions
  - `SyncVectorStoreJob` - Sync với OpenAI Vector Store stats
  - Job monitoring và error handling

- [ ] **Content Extraction Utilities**
  - PDF text extraction với pdf-parse
  - DOCX text extraction với mammoth
  - Plain text handling
  - File type detection và validation
  - Content chunking cho large files

#### 📝 Deliverables
- Document processing pipeline hoàn chỉnh
- Background job system với monitoring
- Content extraction cho multiple file types

---

### Day 9-10: Document CRUD Operations

#### ✅ Tasks
- [ ] **Document Service với Permission Integration**
  - `createDocument()` - Tạo document với default permissions
  - `updateDocument()` - Update với permission check
  - `deleteDocument()` - Soft delete với OpenAI cleanup
  - `getDocument()` - Get với permission filtering
  - `listDocuments()` - List với pagination và filtering
  - `duplicateDocument()` - Duplicate với new OpenAI file

- [ ] **Document Controller với Security**
  - `GET /api/documents` - List với filtering và pagination
  - `POST /api/documents` - Create document
  - `GET /api/documents/:id` - Get document details
  - `PUT /api/documents/:id` - Update document
  - `DELETE /api/documents/:id` - Delete document
  - `GET /api/documents/:id/download` - Generate download URL
  - `POST /api/documents/:id/duplicate` - Duplicate document

- [ ] **Permission Integration**
  - Permission checks trong mỗi operation
  - Filter results theo user permissions
  - Audit logging cho sensitive operations

#### 📝 Deliverables
- Complete CRUD operations với security
- Permission-aware endpoints
- Comprehensive API documentation

---

## 📅 Phase 3: Advanced Features (4-5 ngày)

### Day 11-12: Folder Management và Hierarchy

#### ✅ Tasks
- [ ] **Document Folder Service**
  - `createFolder()` - Tạo folder với hierarchy validation
  - `updateFolder()` - Update với path recalculation
  - `deleteFolder()` - Delete với content handling
  - `getFolderTree()` - Get hierarchical folder structure
  - `moveFolderContents()` - Move documents/folders
  - `calculateFolderPath()` - Auto-calculate folder paths

- [ ] **Folder Controller**
  - `GET /api/documents/folders` - List folders với tree structure
  - `POST /api/documents/folders` - Create folder
  - `PUT /api/documents/folders/:id` - Update folder
  - `DELETE /api/documents/folders/:id` - Delete folder
  - `GET /api/documents/folders/:id/tree` - Get folder tree
  - `POST /api/documents/folders/:id/move` - Move folder

- [ ] **Hierarchical Permissions**
  - Folder permission inheritance
  - Permission override mechanisms
  - Bulk permission updates

#### 📝 Deliverables
- Folder management system với hierarchy
- Permission inheritance system
- Tree structure APIs

---

### Day 13-14: Permission System Implementation

#### ✅ Tasks
- [ ] **Document Permission Service**
  - `setPermission()` - Set user/role/department permissions
  - `removePermission()` - Remove specific permissions
  - `checkPermission()` - Check user access rights
  - `getAccessibleDocuments()` - Get documents user can access
  - `getUserPermissions()` - Get all user permissions
  - `bulkUpdatePermissions()` - Bulk permission operations

- [ ] **Permission Controller**
  - `GET /api/documents/:id/permissions` - Get document permissions
  - `POST /api/documents/:id/permissions` - Set permission
  - `DELETE /api/documents/:id/permissions/:permissionId` - Remove permission
  - `GET /api/documents/permissions/my` - Get user's permissions

- [ ] **Permission Guards và Decorators**
  - `DocumentPermissionGuard` - Route-level permission checking
  - `@RequireDocumentPermission()` decorator
  - Integration với existing JWT guards

- [ ] **Multi-level Permission Support**
  - User-based permissions
  - Role-based permissions (integration với existing roles)
  - Department-based permissions
  - Permission precedence rules

#### 📝 Deliverables
- Complete permission system
- Guards và decorators
- Multi-level permission support

---

### Day 15: RAG Search System với OpenAI

#### ✅ Tasks
- [ ] **Document RAG Service**
  - `searchDocuments()` - Main RAG search với permission filtering
  - `createAssistantForTenant()` - Tạo OpenAI Assistant per tenant
  - `queryAssistant()` - Query Assistant với context
  - `parseAssistantResponse()` - Parse response và extract citations
  - `mapFileCitations()` - Map OpenAI file citations về documents
  - `filterResultsByPermissions()` - Post-filter results

- [ ] **Search Controller**
  - `POST /api/documents/search/rag` - RAG search với natural language
  - `GET /api/documents/search` - Traditional keyword search
  - `GET /api/documents/:id/similar` - Find similar documents
  - `GET /api/documents/search/suggestions` - Search suggestions

- [ ] **Permission-aware Search**
  - Filter OpenAI file IDs theo user permissions
  - Post-process search results
  - Audit logging cho search queries

#### 📝 Deliverables
- RAG search system với OpenAI integration
- Permission-aware search results
- Search API endpoints với logging

---

## 🔧 Environment Configuration

### Environment Variables Cần Thêm
```env
# OpenAI Configuration
OPENAI_API_KEY=sk-proj-...
OPENAI_ORGANIZATION_ID=org-...

# Document Processing
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_MIME_TYPES=application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,text/markdown

# Vector Store Configuration
VECTOR_STORE_EXPIRY_DAYS=365
MAX_FILES_PER_VECTOR_STORE=1000
OPENAI_ASSISTANT_MODEL=gpt-4-turbo-preview

# Queue Configuration (nếu chưa có Redis)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Processing
PDF_PROCESSING_TIMEOUT=30000
DOCX_PROCESSING_TIMEOUT=15000
```

### Dependencies Cần Thêm
```json
{
  "dependencies": {
    "@bull-board/api": "^5.x.x",
    "@bull-board/express": "^5.x.x", 
    "bull": "^4.x.x",
    "pdf-parse": "^1.x.x",
    "mammoth": "^1.x.x",
    "file-type": "^18.x.x",
    "mime-types": "^2.x.x",
    "uuid": "^9.x.x"
  },
  "devDependencies": {
    "@types/pdf-parse": "^1.x.x",
    "@types/mime-types": "^2.x.x"
  }
}
```

---

## 🎯 Success Criteria

### Functional Requirements
- [ ] Upload tài liệu thành công qua S3 với presigned URLs
- [ ] Xử lý và extract text từ PDF, DOCX, TXT files
- [ ] Tích hợp hoàn chỉnh với OpenAI Vector Store và Files API
- [ ] RAG search hoạt động chính xác với permission filtering
- [ ] Hệ thống phân quyền multi-level (user/role/department)
- [ ] Quản lý thư mục phân cấp với inheritance
- [ ] Background processing với job queue
- [ ] Analytics và comprehensive logging

### Performance Requirements
- [ ] Upload file < 5 giây (file 10MB)
- [ ] RAG search response < 3 giây
- [ ] API response time < 500ms (95th percentile)
- [ ] Hỗ trợ 100+ concurrent users
- [ ] 99.9% uptime target

### Security Requirements
- [ ] Tenant isolation hoàn toàn
- [ ] Permission system secure với proper validation
- [ ] File access control với signed URLs
- [ ] API rate limiting và input validation
- [ ] Audit logging cho sensitive operations

---

## 🚨 Risk Management

### Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|---------|-------------|------------|
| OpenAI API rate limits | High | Medium | Implement caching, retry logic, queue management |
| Large file processing timeout | Medium | High | Chunking, async processing, timeout handling |
| Vector Store quota exceeded | High | Low | Monitor usage, implement cleanup policies |
| S3 integration complexity | Medium | Low | Thorough testing, fallback mechanisms |
| Permission system complexity | High | Medium | Incremental development, extensive testing |

### Business Risks
| Risk | Impact | Probability | Mitigation |
|------|---------|-------------|------------|
| OpenAI cost escalation | Medium | Medium | Usage monitoring, cost alerts, optimization |
| User adoption challenges | Medium | Low | User training, intuitive UI design |
| Data privacy concerns | High | Low | Compliance review, data handling policies |

---

## 📊 Monitoring và Maintenance

### Key Metrics
- Document upload success rate (target: >99%)
- RAG search accuracy và user satisfaction
- API response times và error rates
- OpenAI API usage và associated costs
- Storage usage trends
- User activity patterns và adoption rates

### Maintenance Tasks
- **Daily**: Monitor job queue health, check error logs
- **Weekly**: Cleanup expired upload sessions, review usage metrics
- **Monthly**: OpenAI Vector Store optimization, cost analysis
- **Quarterly**: Performance review, security audit
- **Yearly**: Full system security audit, disaster recovery testing

---

## 🎉 Next Steps

1. **Review và Approval**: Stakeholder review của implementation plan
2. **Environment Setup**: Configure development environment với OpenAI keys
3. **Database Setup**: Run migrations và setup test data
4. **Begin Phase 1**: Start với database entities và basic structure
5. **Continuous Integration**: Setup CI/CD pipeline cho automated testing

**Estimated Start Date**: [To be determined]
**Target Completion**: [Start date + 25 working days]
