# Kế hoạch chỉnh sửa module Auth - Tách mật khẩu khỏi CompanyAccount

## Phân tích hiện trạng

### Cấu trúc hiện tại
1. **CompanyAccount Entity**:
   - Hiện đang lưu trữ thông tin công ty và mật khẩu đăng nhập trong cùng một entity
   - Trường `password` trong `company_accounts` được sử dụng để xác thực đăng nhập

2. **Quá trình đăng ký công ty**:
   - Người dùng đăng ký thông tin công ty kèm mật khẩu
   - Hệ thống tạo bản ghi trong bảng `company_accounts` với mật khẩu đã mã hóa
   - Sau khi xác thực email, tài khoản công ty được kích hoạt
   - <PERSON>hi đăng nhập, người dùng sử dụng email công ty và mật khẩu đã đăng ký

3. **Qu<PERSON>n lý quyền**:
   - <PERSON><PERSON> đăng nhập, hệ thống tạo JWT token với type là 'COMPANY_ADMIN'
   - Người dùng với type 'COMPANY_ADMIN' tự động được cấp tất cả các quyền

### Vấn đề
- Mật khẩu đang được lưu trữ trong bảng `company_accounts`, không phù hợp với mô hình phân quyền
- Không có bản ghi `User` tương ứng cho người quản trị công ty
- Khó khăn trong việc quản lý quyền và vai trò cho người quản trị công ty

## Kế hoạch chỉnh sửa

### 1. Thay đổi cấu trúc dữ liệu
1. **Xóa trường `password` trong CompanyAccount Entity**:
   - Cập nhật entity `CompanyAccount` để loại bỏ trường `password`
   - Tạo migration để xóa cột `password` trong bảng `company_accounts`

2. **Điều chỉnh DTO**:
   - Cập nhật `CompanyRegisterDto` để bao gồm thông tin người dùng quản trị (admin)
   - Thêm các trường như `adminUsername`, `adminEmail`, `adminFullName` vào DTO

### 2. Thay đổi quá trình đăng ký
1. **Tạo tài khoản công ty và tài khoản người dùng**:
   - Tạo bản ghi `CompanyAccount` không có mật khẩu
   - Tạo bản ghi `User` cho người quản trị với mật khẩu đã mã hóa
   - Thiết lập `tenantId` của `User` là ID của `CompanyAccount`

2. **Cập nhật quá trình xác thực email**:
   - Sau khi xác thực email, kích hoạt cả tài khoản công ty và tài khoản người dùng

### 3. Thay đổi quá trình đăng nhập
1. **Điều chỉnh quá trình đăng nhập**:
   - Tìm kiếm công ty theo email
   - Tìm kiếm người dùng quản trị theo tenantId và email
   - Xác thực mật khẩu của người dùng thay vì công ty

2. **Cập nhật JWT payload**:
   - Bao gồm thông tin cả công ty và người dùng trong payload
   - Giữ nguyên type 'COMPANY_ADMIN' để duy trì tính năng tự động cấp quyền

### 4. Cập nhật gán quyền
1. **Tạo vai trò và quyền mặc định**:
   - Tạo vai trò 'Company Admin' với đầy đủ quyền
   - Gán vai trò này cho người dùng quản trị khi đăng ký

## Chi tiết thực hiện

### Các file cần chỉnh sửa

1. **Entity**:
   - `src/modules/auth/entities/company-account.entity.ts`: Xóa trường password

2. **DTO**:
   - `src/modules/auth/dto/company-register.dto.ts`: Thêm thông tin người dùng quản trị
   - `src/modules/auth/dto/company-login.dto.ts`: Giữ nguyên

3. **Service**:
   - `src/modules/auth/services/company-auth.service.ts`: 
     - Cập nhật phương thức `register` để tạo cả tài khoản công ty và người dùng
     - Cập nhật phương thức `verifyEmail` để kích hoạt cả hai tài khoản
     - Cập nhật phương thức `login` để xác thực qua tài khoản người dùng

4. **Repository**:
   - `src/modules/auth/repositories/company-account.repository.ts`: Cập nhật các phương thức liên quan

5. **Migration**:
   - Tạo migration để xóa cột `password` trong bảng `company_accounts`

### Các bước thực hiện

1. Tạo migration để xóa cột password trong bảng company_accounts
2. Cập nhật CompanyAccount entity để loại bỏ trường password
3. Cập nhật CompanyRegisterDto để thêm thông tin người dùng quản trị
4. Cập nhật CompanyAuthService để thực hiện các thay đổi trong quá trình đăng ký và đăng nhập
5. Kiểm tra và đảm bảo tính tương thích ngược

## Lưu ý
- Cần đảm bảo tính tương thích ngược với dữ liệu hiện có
- Cần cập nhật tài liệu API và hướng dẫn sử dụng
- Cần kiểm tra kỹ các tính năng liên quan đến xác thực và phân quyền
