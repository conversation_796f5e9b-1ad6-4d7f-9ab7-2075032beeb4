# Hướng dẫn triển khai và sử dụng TenantId

## Giới thiệu

Hệ thống quản lý TenantId được thiết kế để đảm bảo tính bảo mật dữ liệu giữa các công ty/tổ chức trong hệ thống đa tenant. Mỗi tenant (công ty/tổ chức) sẽ chỉ có thể truy cập dữ liệu của chính mình, không thể truy cập dữ liệu của tenant khác.

## C<PERSON> chế hoạt động

Hệ thống sử dụng kết hợp các thành phần sau để quản lý TenantId:

1. **TenantSecurityMiddleware**: Trích xuất tenantId từ JWT token và gắn vào request
2. **TenantContextMiddleware**: Lưu tenantId vào AsyncLocalStorage để sử dụng trong toàn bộ request lifecycle
3. **TenantEntitySubscriber**: Tự động thêm điều kiện tenantId vào các câu truy vấn database
4. **WithTenant Decorator**: Cho phép thiết lập tenantId trong các context không có request

## Quy tắc sử dụng TenantId

### 1. Trong Controller

#### Cách đúng:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { ApiBearerAuth } from '@nestjs/swagger';

@Controller('users')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll() {
    // TenantId được tự động thêm vào các câu truy vấn
    return this.userService.findAll();
  }
}
```

#### Cách sai:

```typescript
import { Controller, Get } from '@nestjs/common';
import { TenantSecurity, CurrentTenant } from '@/common';

@Controller('users')
@TenantSecurity() // KHÔNG sử dụng decorator này nữa
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  findAll(@CurrentTenant() tenantId: number) { // KHÔNG sử dụng decorator này nữa
    return this.userService.findAll(tenantId); // KHÔNG truyền tenantId vào service
  }
}
```

### 2. Trong Repository

#### Cách đúng:

```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    // TenantId được tự động thêm vào câu truy vấn
    return this.repository.find();
  }

  async findById(id: number): Promise<User | null> {
    // TenantId được tự động thêm vào câu truy vấn
    return this.repository.findOne({ where: { id } });
  }
}
```

#### Cách sai:

```typescript
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findAll(tenantId: number): Promise<User[]> { // KHÔNG nhận tenantId làm tham số
    // KHÔNG thêm điều kiện tenantId thủ công
    return this.repository.find({ where: { tenantId } });
  }

  async findById(id: number, tenantId: number): Promise<User | null> { // KHÔNG nhận tenantId làm tham số
    // KHÔNG thêm điều kiện tenantId thủ công
    return this.repository.findOne({ where: { id, tenantId } });
  }
}
```

### 3. Trong Service không có request context

Khi cần thực hiện các thao tác với database trong một context không có request (ví dụ: trong các job, cronjob, hoặc các service được gọi từ bên ngoài request lifecycle), sử dụng decorator `WithTenant`:

```typescript
import { Injectable } from '@nestjs/common';
import { WithTenant } from '@/common/decorators/with-tenant.decorator';

@Injectable()
export class BackgroundService {
  constructor(private readonly userRepository: UserRepository) {}

  @WithTenant(1) // Thiết lập tenantId cố định
  async processDataForTenant1() {
    // TenantId = 1 được tự động thêm vào các câu truy vấn
    return this.userRepository.findAll();
  }

  async processDataForTenant(tenantId: number) {
    await this.processDataForSpecificTenant(tenantId);
  }

  @WithTenant((args) => args[0]) // Lấy tenantId từ tham số đầu tiên
  private async processDataForSpecificTenant(tenantId: number) {
    // TenantId từ tham số được tự động thêm vào các câu truy vấn
    return this.userRepository.findAll();
  }
}
```

### 4. Truy cập dữ liệu của tất cả các tenant (Admin)

Khi cần truy cập dữ liệu của tất cả các tenant (ví dụ: cho admin), sử dụng phương thức `withoutTenantFilter`:

```typescript
import { Injectable } from '@nestjs/common';
import { tenantContext } from '@/common/subscribers/tenant-entity.subscriber';

@Injectable()
export class AdminService {
  constructor(private readonly userRepository: UserRepository) {}

  async findAllUsersAcrossTenants() {
    // Tạm thời tắt tenant filtering
    return tenantContext.run({ tenantId: 0, disableTenantFilter: true }, () => {
      return this.userRepository.findAll();
    });
  }
}
```

## Quy tắc triển khai API mới

1. **Không sử dụng** decorator `@TenantSecurity()` và `@CurrentTenant()` trong controller mới
2. **Luôn sử dụng** `@UseGuards(JwtUserGuard)` và `@ApiBearerAuth('JWT-auth')` cho các API cần xác thực
3. **Không truyền** tenantId từ controller xuống service hoặc repository
4. **Không thêm** điều kiện tenantId thủ công vào câu truy vấn
5. **Không lưu** tenantId thủ công khi tạo entity mới (tenantId sẽ được tự động thêm)
6. **Sử dụng** decorator `@WithTenant` cho các service không có request context
7. **Sử dụng** `disableTenantFilter: true` khi cần truy cập dữ liệu của tất cả các tenant

## Lưu ý quan trọng

1. Middleware bảo mật tenant đã được áp dụng toàn cục trong `CommonModule`, không cần áp dụng thêm
2. TenantId được tự động trích xuất từ JWT token và lưu vào AsyncLocalStorage
3. TenantEntitySubscriber tự động thêm điều kiện tenantId vào tất cả các câu truy vấn
4. Không thể thay đổi tenantId của entity sau khi đã tạo
5. Chỉ SYSTEM_ADMIN mới có thể truy cập dữ liệu của tenant khác

## Ví dụ triển khai API đăng ký tài khoản nhân viên

```typescript
// Controller
@Post('register-employee')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
async registerEmployee(
  @Body() registerDto: RegisterEmployeeDto,
): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
  return this.userAuthService.registerEmployee(registerDto);
}

// Service
async registerEmployee(
  registerDto: RegisterEmployeeDto,
): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
  // Tạo tài khoản người dùng mới
  await this.userRepository.create({
    username: registerDto.username,
    email: registerDto.email,
    password: hashedPassword,
    fullName: registerDto.fullName,
    departmentId: registerDto.departmentId || null,
    status: UserStatus.ACTIVE,
    createdAt: Date.now(),
    // tenantId được tự động thêm bởi middleware bảo mật tenant
  });
  
  return ApiResponseDto.created({ message: 'Đăng ký thành công' });
}
```
