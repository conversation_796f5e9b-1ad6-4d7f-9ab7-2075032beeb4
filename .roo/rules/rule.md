---
description: 
globs: 
alwaysApply: true
---
# Tóm tắt Quy tắc phát triển API cho RedAI Backend

## Cấu trúc phản hồi API

### Phản hồi thành công
Tất cả API sử dụng `ApiResponseDto` từ `@src/common/response/api-response-dto.ts`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {}
}
```
- Sử dụng phương thức tĩnh như `ApiResponseDto.success`, `ApiResponseDto.created`, `ApiResponseDto.deleted` trong controller.

### Tham số truy vấn danh sách
API lấy danh sách sử dụng `QueryDto` từ `@src/common/dto/query.dto.ts`:
- Các tham số: `page`, `limit`, `search_files`, `sortBy`, `sortDirection` (enum `SortDirection: ASC, DESC`).
- Mở rộng `QueryDto` khi cần thêm tham số:
```typescript
export class UserQueryDto extends QueryDto {
  @ApiProperty({ enum: UserStatus, required: false })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
```

### Dữ liệu phân trang
API phân trang sử dụng `PaginatedResult` và `ApiResponseDto.paginated`:
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

## Xử lý lỗi

### Tổ chức mã lỗi
- Mỗi module có thư mục `errors` với file định nghĩa mã lỗi (ví dụ: `module-name-error.code.ts`).
- Phạm vi mã lỗi riêng (ví dụ: User: 10000-10099, R-Point: 12000-12099).
- Ví dụ:
```typescript
export const RPOINT_ERROR_CODES = {
  POINT_PACKAGE_NOT_FOUND: new ErrorCode(12000, 'Không tìm thấy gói point', HttpStatus.NOT_FOUND),
};
```

### Phản hồi lỗi
Sử dụng `AppException` từ `@src/common/exceptions/app.exception.ts`:
```typescript
throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${id}`);
```
Phản hồi lỗi:
```json
{
  "code": 12000,
  "message": "Không tìm thấy gói point với ID 123",
  "result": null
}
```

### Quy tắc xử lý lỗi
- Định nghĩa mã lỗi trong module, sử dụng constant `MODULE_NAME_ERROR_CODES`.
- Không dùng trực tiếp exception của NestJS (`NotFoundException`, ...).
- Xử lý lỗi trong `try/catch`, wrap lỗi chưa xác định vào `AppException`.
- Tên mã lỗi rõ ràng, chữ hoa, phân tách bằng gạch dưới (ví dụ: `USER_NOT_FOUND`).

## Authentication và Authorization
- **User API**: Bảo vệ bằng `JwtUserGuard`, sử dụng `@CurrentUser` để lấy thông tin user.
- **Admin/Employee API**: Bảo vệ bằng `JwtEmployeeGuard`, sử dụng `@CurrentEmployee`.
- Đánh dấu xác thực trong Swagger với `@ApiBearerAuth('JWT-auth')`.

## Swagger Documentation
- **Controller**: Sử dụng `@ApiTags`, `@ApiBearerAuth`, `@ApiExtraModels`.
- **Endpoint**: Mô tả bằng `@ApiOperation`, `@ApiParam`, `@ApiQuery`, `@ApiBody`, `@ApiResponse`.
- **DTO**: Mô tả đầy đủ với `@ApiProperty` (bao gồm `description`, `example`, `required`).
- **Phân trang**: Mô tả cấu trúc `items` và `meta` trong `@ApiResponse`.
- Quy tắc: Nhất quán, đầy đủ, rõ ràng, cung cấp ví dụ, cập nhật thường xuyên.

## Xử lý URL Media
### Hiển thị tài nguyên
- Sử dụng `CdnService` để tạo URL có chữ ký:
```typescript
avatarUrl = this.cdnService.generateUrlView(user.avatarKey, TimeIntervalEnum.ONE_HOUR);
```
- Không lưu URL vào database, chỉ lưu key. Chọn thời hạn phù hợp từ `TimeIntervalEnum`.

### Upload tài nguyên
- Sử dụng `S3Service` để tạo presigned URL:
```typescript
const presignedUrl = await this.s3Service.createPresignedWithID(key, TimeIntervalEnum.FIFTEEN_MINUTES, MediaType.IMAGE_JPEG, FileSizeEnum.ONE_MB);
```
- Frontend gọi API lấy URL, upload file bằng PUT, rồi cập nhật key vào database.

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration và đồng thuận.
- Kiểu dữ liệu entity khớp với database, sử dụng decorator TypeORM phù hợp.
- Tránh relationship mapping (`@OneToMany`, ...), ưu tiên tham chiếu trực tiếp (`userId`).
- Đánh dấu rõ trường nullable, xử lý null/undefined phù hợp.

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh `any`.
- Sử dụng optional chaining (`?.`) và nullish coalescing (`??`).
- Bật `strict: true` trong `tsconfig.json`, không dùng `@ts-ignore`.
- Chạy `npm run build` hoặc `npm run check-types` trước khi commit.

## Quy trình phát triển
1. **Lập kế hoạch**: Tạo file markdown trong `@docs/plan` (format: `YYYYMMDD-module-name-plan.md`).
2. **Kiểm tra code**: Chạy `npm run lint`, `npm run build`, `npm test` trước khi push.
3. **Commit**: Commit thường xuyên, tên rõ ràng, mô tả chi tiết (ví dụ: "User API: Thêm endpoint đăng ký").
4. **Code review**: PR phải tuân thủ quy tắc, không có lỗi TypeScript.

## Unit Test
- Bắt buộc viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%.
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ.
- Tổ chức test theo cấu trúc code, sử dụng mock/stub cho dependency.
- Chạy `npm run test` trước khi commit, đảm bảo CI chạy test tự động.

- Test API kiểm tra cấu trúc response, xử lý lỗi, và validation.