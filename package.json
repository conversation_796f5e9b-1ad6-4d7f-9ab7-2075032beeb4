{"name": "redai-backend-p01", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:employee": "jest --config ./src/modules/employee/jest.config.js", "test:strategy": "jest --config ./src/modules/strategy/jest.config.js", "test:affiliate": "jest --config ./src/modules/affiliate/jest.config.js", "test:okrs": "jest --config ./src/modules/okrs/jest.config.js", "test:db-connection": "jest --config ./src/modules/todolists/jest.config.js src/modules/todolists/tests/database-connection.spec.ts --detectO<PERSON>Handles", "test:todolists": "jest --config ./src/modules/todolists/jest.config.js", "test:tenant-id": "jest --config ./src/modules/todolists/jest.config.js src/modules/todolists/tests/tenant-id-query.spec.ts --detectOpenHandles", "test:tenant-id-sql": "jest --config ./src/modules/todolists/jest.config.js src/modules/todolists/tests/tenant-id-sql-query.spec.ts --detectOpenHandles", "test:todo-tenant": "jest --config ./src/modules/todolists/jest.config.js src/modules/todolists/tests/todo-creation-tenant.spec.ts --detectOpenHandles", "db:test-connection": "ts-node -r tsconfig-paths/register src/modules/todolists/tests/db-connection-test.ts", "db:simple-test": "ts-node -r tsconfig-paths/register src/modules/todolists/tests/simple-db-test.ts", "db:test-tenant": "ts-node -r tsconfig-paths/register src/modules/todolists/tests/tenant-id-test.ts", "db:simple-tenant": "ts-node -r tsconfig-paths/register src/modules/todolists/tests/simple-tenant-test.ts", "db:seed-companies": "ts-node -r tsconfig-paths/register src/modules/auth/tests/seed-company-accounts.ts", "db:seed-all": "ts-node -r tsconfig-paths/register src/modules/auth/tests/seed-data.ts", "db:seed-sample": "ts-node -r tsconfig-paths/register src/database/seeds/run-seed.ts", "seed": "ts-node src/database/seeds/run-seed.ts"}, "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@geersch/nestjs-retry": "^1.0.0", "@google-cloud/storage": "^7.16.0", "@google-cloud/translate": "^9.0.1", "@google-cloud/vision": "^5.1.0", "@google/genai": "^0.12.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.1", "@nestjs/swagger": "^11.1.3", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.1", "@openai/agents": "^0.0.2", "@types/nodemailer": "^6.4.17", "@xstate/fsm": "^2.1.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bull": "^4.16.5", "bullmq": "^5.53.1", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "exponential-backoff": "^3.1.2", "google-ads-api": "^19.0.4-rest-beta", "googleapis": "^148.0.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "nodemailer": "^6.10.1", "npm": "^11.3.0", "openai": "^4.94.0", "opossum": "^8.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "qrcode": "^1.5.4", "react-grid-layout": "^1.5.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.1", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0", "typeorm-transactional": "^0.5.0", "uuid": "^11.1.0", "xstate": "^5.19.2", "zod": "^3.25.51"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/opossum": "^8.1.8", "@types/passport-jwt": "^4.0.1", "@types/selenium-webdriver": "^4.1.28", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^@modules/(.*)$": "<rootDir>/modules/$1", "^@common/(.*)$": "<rootDir>/common/$1", "^@shared/(.*)$": "<rootDir>/shared/$1", "^@config/(.*)$": "<rootDir>/config/$1", "^@config$": "<rootDir>/config", "^@utils/(.*)$": "<rootDir>/shared/utils/$1", "^@database/(.*)$": "<rootDir>/shared/database/$1", "^@dto/(.*)$": "<rootDir>/common/dto/$1", "^@filters/(.*)$": "<rootDir>/common/filters/$1", "^@guards/(.*)$": "<rootDir>/common/guards/$1", "^@interceptors/(.*)$": "<rootDir>/common/interceptors/$1", "^@pipes/(.*)$": "<rootDir>/common/pipes/$1", "^@entities/(.*)$": "<rootDir>/common/entities/$1"}}}