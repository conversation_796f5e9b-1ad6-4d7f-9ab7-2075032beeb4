document.addEventListener('DOMContentLoaded', function() {
    // <PERSON><PERSON><PERSON> <PERSON>hần tử DOM
    const markdownContent = document.getElementById('markdown-content');
    const tocElement = document.getElementById('toc');
    const sidebar = document.querySelector('.sidebar');
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const showSidebarBtn = document.getElementById('show-sidebar');
    const fileSelect = document.getElementById('file-select');
    const uploadBtn = document.getElementById('upload-btn');
    const fileInput = document.getElementById('file-input');
    const editorModal = document.getElementById('editor-modal');
    const markdownEditor = document.getElementById('markdown-editor');
    const saveMarkdownBtn = document.getElementById('save-markdown');
    const cancelMarkdownBtn = document.getElementById('cancel-markdown');
    const closeModalBtn = document.querySelector('.close');

    // Cấu hình Marked.js
    marked.setOptions({
        renderer: new marked.Renderer(),
        highlight: function(code, lang) {
            const language = hljs.getLanguage(lang) ? lang : 'plaintext';
            return hljs.highlight(code, { language }).value;
        },
        langPrefix: 'hljs language-',
        pedantic: false,
        gfm: true,
        breaks: false,
        sanitize: false,
        smartypants: false,
        xhtml: false
    });

    // Mẫu Markdown mặc định
    const defaultMarkdown = `# Cấu trúc phát triển dự án RedAI Backend

## Tổng quan kiến trúc

Dự án RedAI Backend được xây dựng trên nền tảng NestJS, một framework Node.js hiện đại với kiến trúc module hóa và hỗ trợ TypeScript. Dự án áp dụng các nguyên tắc thiết kế hướng đối tượng, dependency injection và kiến trúc đa tầng.

## Cấu trúc thư mục

\`\`\`
backend-ai-erp/
├── src/
│   ├── common/                 # Các thành phần dùng chung
│   │   ├── decorators/        # Các decorator tùy chỉnh
│   │   ├── dto/               # Các DTO dùng chung
│   │   ├── exceptions/        # Xử lý ngoại lệ
│   │   ├── filters/           # Bộ lọc ngoại lệ
│   │   ├── guards/            # Guards bảo mật
│   │   ├── interceptors/      # Interceptors
│   │   ├── middlewares/       # Middlewares
│   │   ├── pipes/             # Pipes xác thực
│   │   ├── response/          # Cấu trúc phản hồi API
│   │   ├── subscribers/       # Entity subscribers
│   │   └── swagger/           # Cấu hình Swagger
│   ├── config/                # Cấu hình ứng dụng
│   ├── modules/               # Các module nghiệp vụ
│   │   ├── auth/              # Module xác thực
│   │   ├── okrs/              # Module OKRs
│   │   ├── todolists/         # Module quản lý công việc
│   │   └── ...                # Các module khác
│   ├── shared/                # Các dịch vụ dùng chung
│   │   ├── database/          # Cấu hình và kết nối database
│   │   ├── services/          # Các dịch vụ dùng chung (email, S3, ...)
│   │   └── utils/             # Các tiện ích
│   ├── app.module.ts          # Module gốc của ứng dụng
│   └── main.ts                # Điểm khởi đầu ứng dụng
├── docs/                      # Tài liệu dự án
├── test/                      # Unit tests và E2E tests
└── ...                        # Các file cấu hình khác
\`\`\`

## Cấu trúc module

Mỗi module nghiệp vụ được tổ chức theo cấu trúc sau:

\`\`\`
modules/module-name/
├── controllers/              # Xử lý request và response
├── dtos/                     # Data Transfer Objects
│   ├── requests/             # DTO cho request
│   └── responses/            # DTO cho response
├── entities/                 # Các entity (model)
├── errors/                   # Mã lỗi và xử lý lỗi
├── guards/                   # Guards bảo mật
├── interfaces/               # Các interface
├── repositories/             # Truy cập dữ liệu
├── services/                 # Logic nghiệp vụ
├── share/                    # Các thành phần dùng chung trong module
├── module-name.module.ts     # Định nghĩa module
└── docs/                     # Tài liệu module
\`\`\`

## Quy tắc phát triển API

### Cấu trúc phản hồi API

Tất cả API sử dụng \`ApiResponseDto\` từ \`@/common/response/api-response-dto.ts\`:

\`\`\`json
{
  "code": 200,
  "message": "Success",
  "result": {}
}
\`\`\`

### Tham số truy vấn danh sách

API lấy danh sách sử dụng \`QueryDto\` từ \`@/common/dto/query.dto.ts\`:
- Các tham số: \`page\`, \`limit\`, \`search\`, \`sortBy\`, \`sortDirection\`

### Dữ liệu phân trang

API phân trang sử dụng \`PaginatedResult\` và \`ApiResponseDto.paginated\`:

\`\`\`json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
\`\`\`

## Xử lý lỗi

### Tổ chức mã lỗi

- Mỗi module có thư mục \`errors\` với file định nghĩa mã lỗi
- Phạm vi mã lỗi riêng (ví dụ: User: 10000-10099, R-Point: 12000-12099)

### Phản hồi lỗi

Sử dụng \`AppException\` từ \`@/common/exceptions/app.exception.ts\`:

\`\`\`typescript
throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, \`Không tìm thấy gói point với ID \${id}\`);
\`\`\`

## Quản lý Tenant và TenantId

### Cơ chế hoạt động

1. **TenantEntitySubscriber**: Tự động thêm điều kiện tenantId vào các câu truy vấn
2. **TenantContextMiddleware**: Lưu tenantId vào AsyncLocalStorage
3. **WithTenant Decorator**: Thiết lập tenantId vào context trong các trường hợp đặc biệt

### Cách sử dụng

1. **Trong Controller**: Khi sử dụng \`@UseGuards(JwtUserGuard)\`, tenantId tự động thiết lập vào context
2. **Trong Repository**: Không cần thêm điều kiện tenantId vào các câu truy vấn
3. **Trong Service không có request**: Sử dụng decorator \`@WithTenant\`
4. **Truy cập dữ liệu của tất cả các tenant (Admin)**: Sử dụng decorator \`@AdminAccess\` hoặc hàm \`withoutTenantFilter\`

## Authentication và Authorization

- **Tất cả API có bảo mật**: Đặt \`@UseGuards(JwtUserGuard)\` và \`@ApiBearerAuth('JWT-auth')\` trên controller
- Sử dụng \`@CurrentUser() user: JwtPayload\` để lấy thông tin người dùng hiện tại

## Swagger Documentation

- **Controller**: Sử dụng \`@ApiTags(SWAGGER_API_TAG.XXX)\` với XXX từ \`SWAGGER_API_TAG\`
- **Endpoint**: Mô tả bằng \`@ApiOperation\`, \`@ApiParam\`, \`@ApiQuery\`, \`@ApiBody\`, \`@ApiResponse\`
- **DTO**: Mô tả đầy đủ với \`@ApiProperty\`

## Xử lý URL Media

### Hiển thị tài nguyên
- Sử dụng \`CdnService\` để tạo URL có chữ ký
- Không lưu URL vào database, chỉ lưu key

### Upload tài nguyên
- Sử dụng \`S3Service\` để tạo presigned URL
- Frontend gọi API lấy URL, upload file bằng PUT, rồi cập nhật key vào database

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration và đồng thuận
- Kiểu dữ liệu entity khớp với database, sử dụng decorator TypeORM phù hợp
- Tránh relationship mapping, ưu tiên tham chiếu trực tiếp
- Đánh dấu rõ trường nullable, xử lý null/undefined phù hợp

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh \`any\`
- Sử dụng optional chaining (\`?.\`) và nullish coalescing (\`??\`)
- Bật \`strict: true\` trong \`tsconfig.json\`, không dùng \`@ts-ignore\`
- Chạy \`npm run build\` hoặc \`npm run check-types\` trước khi commit

## Quy trình phát triển
1. **Lập kế hoạch**: Tạo file markdown trong \`@docs/plan\`
2. **Kiểm tra code**: Chạy \`npm run lint\`, \`npm run build\`, \`npm test\` trước khi push
3. **Commit**: Commit thường xuyên, tên rõ ràng, mô tả chi tiết
4. **Code review**: PR phải tuân thủ quy tắc, không có lỗi TypeScript

## Unit Test
- Bắt buộc viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ
- Tổ chức test theo cấu trúc code, sử dụng mock/stub cho dependency
- Chạy \`npm run test\` trước khi commit`;

    // Hiển thị Markdown mặc định
    renderMarkdown(defaultMarkdown);

    // Tạo mục lục từ nội dung Markdown
    function generateTOC(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const toc = document.createElement('ul');

        let currentLevel = 0;
        let currentList = toc;
        let listStack = [toc];

        headings.forEach((heading, index) => {
            // Lấy cấp độ tiêu đề (1-6)
            const level = parseInt(heading.tagName.substring(1));

            // Tạo ID cho tiêu đề nếu chưa có
            if (!heading.id) {
                heading.id = 'heading-' + index;
            }

            // Điều chỉnh cấu trúc danh sách dựa trên cấp độ tiêu đề
            if (level > currentLevel) {
                // Đi sâu hơn, tạo danh sách con mới
                const newList = document.createElement('ul');
                listStack[listStack.length - 1].lastChild.appendChild(newList);
                listStack.push(newList);
                currentList = newList;
            } else if (level < currentLevel) {
                // Đi lên, quay lại danh sách cha
                for (let i = 0; i < currentLevel - level; i++) {
                    listStack.pop();
                }
                currentList = listStack[listStack.length - 1];
            }

            // Tạo mục mới
            const listItem = document.createElement('li');
            const link = document.createElement('a');
            link.href = '#' + heading.id;
            link.textContent = heading.textContent;
            link.dataset.headingId = heading.id;

            listItem.appendChild(link);
            currentList.appendChild(listItem);

            currentLevel = level;
        });

        return toc;
    }

    // Render Markdown thành HTML và cập nhật mục lục
    function renderMarkdown(markdown) {
        // Chuyển đổi Markdown thành HTML
        const html = marked.parse(markdown);
        markdownContent.innerHTML = html;

        // Tạo mục lục
        const toc = generateTOC(html);
        tocElement.innerHTML = '';
        tocElement.appendChild(toc);

        // Thêm sự kiện click cho các liên kết trong mục lục
        const tocLinks = tocElement.querySelectorAll('a');
        tocLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Xóa class active từ tất cả các liên kết
                tocLinks.forEach(l => l.classList.remove('active'));

                // Thêm class active cho liên kết được click
                this.classList.add('active');

                // Cuộn đến tiêu đề tương ứng
                const headingId = this.getAttribute('href').substring(1);
                const heading = document.getElementById(headingId);

                if (heading) {
                    heading.scrollIntoView({ behavior: 'smooth' });
                }

                // Đóng sidebar trên thiết bị di động
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // Highlight code blocks
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightBlock(block);
        });
    }

    // Theo dõi vị trí cuộn để cập nhật mục lục
    markdownContent.addEventListener('scroll', function() {
        const headings = markdownContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const tocLinks = tocElement.querySelectorAll('a');

        // Tìm tiêu đề hiện tại đang hiển thị
        let currentHeadingId = null;

        headings.forEach(heading => {
            const rect = heading.getBoundingClientRect();
            if (rect.top <= 100) {
                currentHeadingId = heading.id;
            }
        });

        // Cập nhật trạng thái active trong mục lục
        if (currentHeadingId) {
            tocLinks.forEach(link => {
                link.classList.remove('active');
                if (link.dataset.headingId === currentHeadingId) {
                    link.classList.add('active');
                }
            });
        }
    });

    // Toggle sidebar
    toggleSidebarBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
    });

    // Show sidebar on mobile
    showSidebarBtn.addEventListener('click', function() {
        sidebar.classList.add('active');
    });

    // Xử lý tải lên file
    uploadBtn.addEventListener('click', function() {
        fileInput.click();
    });

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                renderMarkdown(content);

                // Thêm file vào dropdown
                const option = document.createElement('option');
                option.value = file.name;
                option.textContent = file.name;
                fileSelect.appendChild(option);
                fileSelect.value = file.name;

                // Lưu nội dung vào localStorage
                localStorage.setItem('markdown_' + file.name, content);
            };
            reader.readAsText(file);
        }
    });

    // Xử lý modal editor
    function openModal() {
        editorModal.style.display = 'block';
        markdownEditor.value = markdownContent.dataset.markdown || defaultMarkdown;
    }

    function closeModal() {
        editorModal.style.display = 'none';
    }

    saveMarkdownBtn.addEventListener('click', function() {
        const markdown = markdownEditor.value;
        renderMarkdown(markdown);
        markdownContent.dataset.markdown = markdown;
        closeModal();
    });

    cancelMarkdownBtn.addEventListener('click', closeModal);
    closeModalBtn.addEventListener('click', closeModal);

    // Đóng modal khi click bên ngoài
    window.addEventListener('click', function(e) {
        if (e.target === editorModal) {
            closeModal();
        }
    });

    // Xử lý chọn file từ dropdown
    fileSelect.addEventListener('change', function() {
        const selectedFile = this.value;
        if (selectedFile === 'default') {
            renderMarkdown(defaultMarkdown);
        } else if (selectedFile === 'editor') {
            openModal();
        } else {
            const content = localStorage.getItem('markdown_' + selectedFile);
            if (content) {
                renderMarkdown(content);
            }
        }
    });

    // Thêm tùy chọn editor vào dropdown
    const editorOption = document.createElement('option');
    editorOption.value = 'editor';
    editorOption.textContent = 'Nhập Markdown trực tiếp';
    fileSelect.appendChild(editorOption);

    // Khôi phục các file đã lưu từ localStorage
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('markdown_')) {
            const fileName = key.substring(9);
            const option = document.createElement('option');
            option.value = fileName;
            option.textContent = fileName;
            fileSelect.appendChild(option);
        }
    }
});
