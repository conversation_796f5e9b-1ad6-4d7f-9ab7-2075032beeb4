---
description: 
globs: 
alwaysApply: false
---
# ✅ HELPER RULES

## 🎯 Objective
Pure utility functions, not dependent on frameworks or layers.

## 📁 Location
`shared/utils/` or `[module-name]/helpers/`

## 💡 Coding Rules
- ✅ Must be pure functions, no side effects
- ✅ No DB access, no service calls
- ✅ No arbitrary exception throwing
- ✅ Easy to test, easy to reuse

## ✅ Checklist
- [ ] No framework or service imports
- [ ] Separated by domain (string, time, array...)

- [ ] Clear names, no abbreviations