---
description: 
globs: 
alwaysApply: true
---
# DETAILED ENTITY RULES

> Entities are the core of the database layer – critical when using ORM in TypeORM

## 🎯 **Objectives**

- Model database tables
- Single source of truth for table schema
- No business logic or utilities

## 📁 **Location and Naming Conventions**

- **File location**: In the `entities/` directory of each module

  ```
  src/modules/user/entities/user.entity.ts
  ```

- **File name**: `xxx.entity.ts` (mandatory per NestJS standards)
- **Class name**: PascalCase (e.g., `User`)
- **Variable names**: camelCase, mapping to snake_case in DB

## 💡 **Detailed Rules**

### 1. **Schema Definition – No Methods**

- Don't add business logic or utilities to entities
- If mapping or processing is needed, move it to service/helper

### 2. **ID Declaration (Primary Key)**

- Use `@PrimaryGeneratedColumn()` for auto-increment IDs
- Use `@PrimaryGeneratedColumn('uuid')` for UUIDs

```ts
/**
 * User ID
 */
@PrimaryGeneratedColumn()
id: number;

// Or with UUID
/**
 * User ID
 */
@PrimaryGeneratedColumn('uuid')
id: string;
```

### 3. **Complete Comments and Annotations**

- Always declare full decorators: `@Column`, `@CreateDateColumn`, `@UpdateDateColumn`, `@DeleteDateColumn`
- Use `{ name: 'db_field_name' }` to ensure sync with DB snake_case
- Must include clear comments for fields and tables in entities

```ts
/**
 * Detailed description of the field
 */
@Column({ name: 'field_name', type: 'text', nullable: true })
fieldName: string | null;
```

### 4. **Nullable Fields**

- If a field can be null, add null type after the main data type
- Always declare `nullable: true` in the decorator

```ts
/**
 * Detailed description of the field
 */
@Column({ type: 'text', nullable: true })
description: string | null;
```

### 5. **Timestamp Fields**

- Use `@CreateDateColumn`, `@UpdateDateColumn`, `@DeleteDateColumn` for timestamp fields
- Use `bigint` type with timestamp in milliseconds

```ts
/**
 * Creation timestamp (in milliseconds)
 */
@CreateDateColumn({
  name: 'created_at',
  type: 'bigint',
  default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
})
createdAt: number;

/**
 * Last update timestamp (in milliseconds)
 */
@UpdateDateColumn({
  name: 'updated_at',
  type: 'bigint',
  default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
})
updatedAt: number;

/**
 * Deletion timestamp (in milliseconds)
 */
@DeleteDateColumn({
  name: 'deleted_at',
  type: 'bigint',
  nullable: true,
})
deletedAt: number | null;
```

### 6. **JSONB Fields**

- Always define clear interfaces for JSONB fields instead of using `Record<string, any>`
- Interfaces should be placed in a separate file or in the same file as the entity

```ts
// Interface definition
export interface ModelConfig {
  temperature: number;
  maxTokens: number;
  topP?: number;
}

// Usage in entity
/**
 * AI model configuration
 */
@Column({ name: 'model_config', type: 'jsonb', default: '{}' })
modelConfig: ModelConfig;
```

### 7. **Custom Database Types**

- When the database has custom types (enums), use corresponding TypeScript enums in the entity
- Enums should be defined in separate files in the `enums/` or `constants/` directory

```ts
// Enum definition (in enums/user-status.enum.ts)
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// Usage in entity
/**
 * User status
 */
@Column({
  name: 'status',
  type: 'enum',
  enum: UserStatus,
  default: UserStatus.INACTIVE,
})
status: UserStatus;
```

### 8. **No Arbitrary Entity Modifications**

- Don't arbitrarily add fields to entities while implementing logic
- Don't arbitrarily modify entities while implementing logic
- Only modify entities when specifically requested

### 9. **Entity Relationships**

- When requested to create new entities, only write fields rather than creating relationship mappings like OneToOne, OneToMany, ManyToMany, ManyToOne
- Don't modify entities when implementing logic if you can't find a field
- Only declare relationships when specifically requested and must follow the designed structure

## ✅ **Entity Writing Checklist**

- [ ] Complete annotations for all columns
- [ ] Clear comments for fields and tables
- [ ] No business logic
- [ ] No imports from higher layers (DTO, service...)
- [ ] Synchronized with DB naming format (`snake_case`)
- [ ] Nullable fields have `Type | null` data type
- [ ] Timestamp fields use correct decorators and formats
- [ ] File names follow NestJS standard: `xxx.entity.ts`
- [ ] No arbitrary entity modifications when implementing logic
- [ ] JSONB fields have clear interfaces instead of `Record<string, any>`
- [ ] Fields using custom DB types have corresponding TypeScript enums

