<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Viewer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #24292e;
            margin: 0;
            padding: 0;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background-color: #f6f8fa;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
            padding: 20px;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        h1 {
            font-size: 2em;
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 0.3em;
        }
        
        h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 0.3em;
        }
        
        h3 {
            font-size: 1.25em;
        }
        
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }
        
        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
        }
        
        pre code {
            padding: 0;
            background-color: transparent;
        }
        
        blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
        }
        
        table th, table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }
        
        table th {
            background-color: #f6f8fa;
        }
        
        #toc ul {
            list-style-type: none;
            padding-left: 15px;
        }
        
        #toc > ul {
            padding-left: 0;
        }
        
        #toc a {
            color: #0366d6;
            text-decoration: none;
            display: block;
            padding: 5px 0;
        }
        
        #toc a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>Mục lục</h2>
            <div id="toc"></div>
        </div>
        <div class="content" id="content"></div>
    </div>

    <script>
        // Nội dung Markdown
        const markdown = `# Cấu trúc phát triển dự án RedAI Backend

## Tổng quan kiến trúc

Dự án RedAI Backend được xây dựng trên nền tảng NestJS, một framework Node.js hiện đại với kiến trúc module hóa và hỗ trợ TypeScript. Dự án áp dụng các nguyên tắc thiết kế hướng đối tượng, dependency injection và kiến trúc đa tầng.

## Cấu trúc thư mục

\`\`\`
backend-ai-erp/
├── src/
│   ├── common/                 # Các thành phần dùng chung
│   │   ├── decorators/        # Các decorator tùy chỉnh
│   │   ├── dto/               # Các DTO dùng chung
│   │   ├── exceptions/        # Xử lý ngoại lệ
│   │   ├── filters/           # Bộ lọc ngoại lệ
│   │   ├── guards/            # Guards bảo mật
│   │   ├── interceptors/      # Interceptors
│   │   ├── middlewares/       # Middlewares
│   │   ├── pipes/             # Pipes xác thực
│   │   ├── response/          # Cấu trúc phản hồi API
│   │   ├── subscribers/       # Entity subscribers
│   │   └── swagger/           # Cấu hình Swagger
│   ├── config/                # Cấu hình ứng dụng
│   ├── modules/               # Các module nghiệp vụ
│   │   ├── auth/              # Module xác thực
│   │   ├── okrs/              # Module OKRs
│   │   ├── todolists/         # Module quản lý công việc
│   │   └── ...                # Các module khác
│   ├── shared/                # Các dịch vụ dùng chung
│   │   ├── database/          # Cấu hình và kết nối database
│   │   ├── services/          # Các dịch vụ dùng chung (email, S3, ...)
│   │   └── utils/             # Các tiện ích
│   ├── app.module.ts          # Module gốc của ứng dụng
│   └── main.ts                # Điểm khởi đầu ứng dụng
├── docs/                      # Tài liệu dự án
├── test/                      # Unit tests và E2E tests
└── ...                        # Các file cấu hình khác
\`\`\`

## Cấu trúc module

Mỗi module nghiệp vụ được tổ chức theo cấu trúc sau:

\`\`\`
modules/module-name/
├── controllers/              # Xử lý request và response
├── dtos/                     # Data Transfer Objects
│   ├── requests/             # DTO cho request
│   └── responses/            # DTO cho response
├── entities/                 # Các entity (model)
├── errors/                   # Mã lỗi và xử lý lỗi
├── guards/                   # Guards bảo mật
├── interfaces/               # Các interface
├── repositories/             # Truy cập dữ liệu
├── services/                 # Logic nghiệp vụ
├── share/                    # Các thành phần dùng chung trong module
├── module-name.module.ts     # Định nghĩa module
└── docs/                     # Tài liệu module
\`\`\`

## Quy tắc phát triển API

### Cấu trúc phản hồi API

Tất cả API sử dụng \`ApiResponseDto\` từ \`@/common/response/api-response-dto.ts\`:

\`\`\`json
{
  "code": 200,
  "message": "Success",
  "result": {}
}
\`\`\`

### Tham số truy vấn danh sách

API lấy danh sách sử dụng \`QueryDto\` từ \`@/common/dto/query.dto.ts\`:
- Các tham số: \`page\`, \`limit\`, \`search\`, \`sortBy\`, \`sortDirection\`

### Dữ liệu phân trang

API phân trang sử dụng \`PaginatedResult\` và \`ApiResponseDto.paginated\`:

\`\`\`json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
\`\`\`

## Xử lý lỗi

### Tổ chức mã lỗi

- Mỗi module có thư mục \`errors\` với file định nghĩa mã lỗi
- Phạm vi mã lỗi riêng (ví dụ: User: 10000-10099, R-Point: 12000-12099)

### Phản hồi lỗi

Sử dụng \`AppException\` từ \`@/common/exceptions/app.exception.ts\`:

\`\`\`typescript
throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, \`Không tìm thấy gói point với ID \${id}\`);
\`\`\`

## Quản lý Tenant và TenantId

### Cơ chế hoạt động

1. **TenantEntitySubscriber**: Tự động thêm điều kiện tenantId vào các câu truy vấn
2. **TenantContextMiddleware**: Lưu tenantId vào AsyncLocalStorage
3. **WithTenant Decorator**: Thiết lập tenantId vào context trong các trường hợp đặc biệt

### Cách sử dụng

1. **Trong Controller**: Khi sử dụng \`@UseGuards(JwtUserGuard)\`, tenantId tự động thiết lập vào context
2. **Trong Repository**: Không cần thêm điều kiện tenantId vào các câu truy vấn
3. **Trong Service không có request**: Sử dụng decorator \`@WithTenant\`
4. **Truy cập dữ liệu của tất cả các tenant (Admin)**: Sử dụng decorator \`@AdminAccess\` hoặc hàm \`withoutTenantFilter\`

## Authentication và Authorization

- **Tất cả API có bảo mật**: Đặt \`@UseGuards(JwtUserGuard)\` và \`@ApiBearerAuth('JWT-auth')\` trên controller
- Sử dụng \`@CurrentUser() user: JwtPayload\` để lấy thông tin người dùng hiện tại

## Swagger Documentation

- **Controller**: Sử dụng \`@ApiTags(SWAGGER_API_TAG.XXX)\` với XXX từ \`SWAGGER_API_TAG\`
- **Endpoint**: Mô tả bằng \`@ApiOperation\`, \`@ApiParam\`, \`@ApiQuery\`, \`@ApiBody\`, \`@ApiResponse\`
- **DTO**: Mô tả đầy đủ với \`@ApiProperty\`

## Xử lý URL Media

### Hiển thị tài nguyên
- Sử dụng \`CdnService\` để tạo URL có chữ ký
- Không lưu URL vào database, chỉ lưu key

### Upload tài nguyên
- Sử dụng \`S3Service\` để tạo presigned URL
- Frontend gọi API lấy URL, upload file bằng PUT, rồi cập nhật key vào database

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration và đồng thuận
- Kiểu dữ liệu entity khớp với database, sử dụng decorator TypeORM phù hợp
- Tránh relationship mapping, ưu tiên tham chiếu trực tiếp
- Đánh dấu rõ trường nullable, xử lý null/undefined phù hợp

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh \`any\`
- Sử dụng optional chaining (\`?.\`) và nullish coalescing (\`??\`)
- Bật \`strict: true\` trong \`tsconfig.json\`, không dùng \`@ts-ignore\`
- Chạy \`npm run build\` hoặc \`npm run check-types\` trước khi commit

## Quy trình phát triển
1. **Lập kế hoạch**: Tạo file markdown trong \`@docs/plan\`
2. **Kiểm tra code**: Chạy \`npm run lint\`, \`npm run build\`, \`npm test\` trước khi push
3. **Commit**: Commit thường xuyên, tên rõ ràng, mô tả chi tiết
4. **Code review**: PR phải tuân thủ quy tắc, không có lỗi TypeScript

## Unit Test
- Bắt buộc viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ
- Tổ chức test theo cấu trúc code, sử dụng mock/stub cho dependency
- Chạy \`npm run test\` trước khi commit`;

        // Chuyển đổi Markdown thành HTML
        function convertMarkdownToHTML(markdown) {
            // Xử lý tiêu đề
            let html = markdown
                .replace(/^# (.*$)/gm, '<h1 id="$1">$1</h1>')
                .replace(/^## (.*$)/gm, '<h2 id="$1">$1</h2>')
                .replace(/^### (.*$)/gm, '<h3 id="$1">$1</h3>')
                .replace(/^#### (.*$)/gm, '<h4 id="$1">$1</h4>')
                .replace(/^##### (.*$)/gm, '<h5 id="$1">$1</h5>')
                .replace(/^###### (.*$)/gm, '<h6 id="$1">$1</h6>');

            // Xử lý đoạn văn
            html = html.replace(/^\s*(\n)?(.+)/gm, function(m) {
                return /\<(\/)?(h|ul|ol|li|blockquote|pre|table|tr|th|td)/.test(m) ? m : '<p>' + m + '</p>';
            });

            // Xử lý danh sách
            html = html
                .replace(/^\s*\n\* (.*)/gm, '<ul>\n<li>$1</li>\n</ul>')
                .replace(/^\s*\n- (.*)/gm, '<ul>\n<li>$1</li>\n</ul>')
                .replace(/^\s*\n(\d+)\. (.*)/gm, '<ol>\n<li>$2</li>\n</ol>')
                .replace(/<\/ul>\s*\n<ul>/g, '')
                .replace(/<\/ol>\s*\n<ol>/g, '');

            // Xử lý code block
            html = html.replace(/```([\s\S]*?)```/g, function(match, code) {
                return '<pre><code>' + code.trim() + '</code></pre>';
            });

            // Xử lý inline code
            html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Xử lý liên kết
            html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

            // Xử lý in đậm và in nghiêng
            html = html
                .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                .replace(/\_\_([^_]+)\_\_/g, '<strong>$1</strong>')
                .replace(/\_([^_]+)\_/g, '<em>$1</em>');

            return html;
        }

        // Tạo mục lục
        function generateTOC(html) {
            const headings = html.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/g) || [];
            let toc = '<ul>';
            let level = 1;
            let lastLevel = 1;

            headings.forEach(heading => {
                const match = heading.match(/<h([1-6])[^>]*id="([^"]+)">(.*?)<\/h[1-6]>/);
                if (match) {
                    const headingLevel = parseInt(match[1]);
                    const id = match[2];
                    const title = match[3];

                    if (headingLevel > lastLevel) {
                        toc += '<ul>';
                        level++;
                    } else if (headingLevel < lastLevel) {
                        while (level > headingLevel) {
                            toc += '</ul>';
                            level--;
                        }
                    }

                    toc += `<li><a href="#${id}">${title}</a></li>`;
                    lastLevel = headingLevel;
                }
            });

            while (level > 1) {
                toc += '</ul>';
                level--;
            }

            toc += '</ul>';
            return toc;
        }

        // Hiển thị nội dung và mục lục
        document.addEventListener('DOMContentLoaded', function() {
            const contentElement = document.getElementById('content');
            const tocElement = document.getElementById('toc');
            
            const html = convertMarkdownToHTML(markdown);
            contentElement.innerHTML = html;
            
            const toc = generateTOC(html);
            tocElement.innerHTML = toc;
            
            // Thêm sự kiện click cho các liên kết trong mục lục
            const tocLinks = tocElement.querySelectorAll('a');
            tocLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
    </script>
</body>
</html>
