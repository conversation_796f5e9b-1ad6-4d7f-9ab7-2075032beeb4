import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AppException, ErrorCode } from '@/common';

/**
 * Interface mở rộng cho Request để bao gồm thông tin người dùng từ JWT
 */
export interface RequestWithUser extends Request {
  user?: {
    id: number;
    sub: number;
    tenantId?: number | string; // Có thể là chuỗi từ JWT
    type?: 'SYSTEM_ADMIN' | 'COMPANY_ADMIN' | 'EMPLOYEE';
    [key: string]: any;
  };
  tenantId?: number; // Luôn là số sau khi xử lý
}

/**
 * Middleware bảo mật dữ liệu giữa các công ty dựa trên tenantId
 * Đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu thuộc về công ty của họ
 */
@Injectable()
export class TenantSecurityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantSecurityMiddleware.name);

  /**
   * <PERSON><PERSON> lý request và kiểm tra tenantId
   * @param request Request từ client
   * @param _response Response từ server
   * @param next Hàm next để chuyển tiếp request
   */
  use(request: RequestWithUser, _response: Response, next: NextFunction): void {
    const requestPath = request.path;
    const requestMethod = request.method;

    this.logger.debug(
      `[TenantSecurityMiddleware] Processing ${requestMethod} ${requestPath}`,
    );

    // Kiểm tra xem request có thông tin user không (đã được xác thực bởi JwtUserGuard)
    if (!request.user) {
      // Nếu không có thông tin user, cho phép request đi tiếp (có thể là route public)
      this.logger.debug(
        `[TenantSecurityMiddleware] No user found in request for ${requestMethod} ${requestPath} - allowing public access`,
      );
      next();
      return;
    }

    const userId = request.user.id;
    const userType = request.user.type;

    this.logger.debug(
      `[TenantSecurityMiddleware] User ${userId} (type: ${userType}) accessing ${requestMethod} ${requestPath}`,
    );
    this.logger.debug(
      `[TenantSecurityMiddleware] User tenantId from JWT: ${request.user.tenantId}`,
    );

    // Kiểm tra xem user có tenantId không
    if (!request.user.tenantId) {
      // Nếu là SYSTEM_ADMIN và không có tenantId, vẫn cho phép truy cập
      if (request.user.type === 'SYSTEM_ADMIN') {
        this.logger.log(
          `[TenantSecurityMiddleware] SYSTEM_ADMIN ${userId} truy cập không có tenantId cho ${requestMethod} ${requestPath}`,
        );
        next();
        return;
      }

      this.logger.warn(
        `[TenantSecurityMiddleware] Không tìm thấy tenantId trong JWT cho user ${userId} (type: ${userType})`,
      );
      throw new AppException(
        ErrorCode.UNAUTHORIZED_ACCESS,
        'Không tìm thấy thông tin công ty',
      );
    }

    // Chuyển đổi tenantId từ chuỗi sang số nếu cần
    let userTenantId = request.user.tenantId;
    if (typeof userTenantId === 'string') {
      userTenantId = parseInt(userTenantId, 10);
      this.logger.debug(
        `[TenantSecurityMiddleware] Chuyển đổi tenantId từ JWT từ chuỗi sang số: ${userTenantId} cho user ${userId}`,
      );
      // Cập nhật lại trong user object
      request.user.tenantId = userTenantId;
    }

    // Lấy tenantId từ request params hoặc query (nếu có)
    const requestTenantId = this.extractTenantIdFromRequest(request);

    this.logger.debug(
      `[TenantSecurityMiddleware] Request tenantId from params/query: ${requestTenantId}`,
    );

    // Nếu có tenantId trong request, kiểm tra xem có khớp với tenantId trong JWT không
    if (requestTenantId && requestTenantId !== userTenantId) {
      // Nếu là SYSTEM_ADMIN, cho phép truy cập dữ liệu của bất kỳ công ty nào
      if (request.user.type === 'SYSTEM_ADMIN') {
        this.logger.log(
          `[TenantSecurityMiddleware] SYSTEM_ADMIN ${userId} truy cập dữ liệu của công ty ${requestTenantId} (khác với JWT tenantId: ${userTenantId})`,
        );

        // Thêm tenantId vào request để các controller và service có thể sử dụng
        request.tenantId = requestTenantId;

        next();
        return;
      }

      this.logger.warn(
        `[TenantSecurityMiddleware] User ${userId} với tenantId ${userTenantId} đang cố truy cập dữ liệu của công ty ${requestTenantId}`,
      );
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Bạn không có quyền truy cập dữ liệu của công ty khác',
      );
    }

    // Thêm tenantId vào request để các controller và service có thể sử dụng
    request.tenantId = userTenantId;

    this.logger.log(
      `[TenantSecurityMiddleware] Set request.tenantId = ${userTenantId} for user ${userId} accessing ${requestMethod} ${requestPath}`,
    );

    // Cho phép request đi tiếp
    next();
  }

  /**
   * Trích xuất tenantId từ request params hoặc query
   * @param request Request từ client
   * @returns tenantId hoặc undefined nếu không tìm thấy
   */
  private extractTenantIdFromRequest(
    request: RequestWithUser,
  ): number | undefined {
    // Kiểm tra trong params
    if (request.params && request.params.tenantId) {
      return parseInt(request.params.tenantId, 10);
    }

    // Kiểm tra trong query
    if (request.query && request.query.tenantId) {
      return parseInt(request.query.tenantId as string, 10);
    }

    // Kiểm tra trong body
    if (request.body && request.body.tenantId) {
      return parseInt(request.body.tenantId, 10);
    }

    return undefined;
  }
}
