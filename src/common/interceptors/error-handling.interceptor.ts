import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AppException, ErrorCode } from '@/common';

/**
 * Interceptor bắt lỗi trong các Promise và Observable
 * Chuyển đổi các lỗi không xác định thành AppException
 */
@Injectable()
export class ErrorHandlingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ErrorHandlingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        // Nếu đã là AppException thì không cần wrap lại
        if (error instanceof AppException) {
          return throwError(() => error);
        }

        // Log lỗi
        const contextClass = context.getClass().name;
        const handlerName = context.getHandler().name;

        this.logger.error(
          `Uncaught error in ${contextClass}.${handlerName}`,
          error.stack,
        );

        // Wrap lỗi trong AppException
        return throwError(
          () =>
            new AppException(
              ErrorCode.INTERNAL_SERVER_ERROR,
              'An unexpected error occurred',
              process.env.NODE_ENV === 'development'
                ? {
                    originalError: error.message,
                    stack: error.stack,
                    context: `${contextClass}.${handlerName}`,
                  }
                : undefined,
            ),
        );
      }),
    );
  }
}
