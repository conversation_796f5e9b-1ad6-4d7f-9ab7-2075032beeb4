import { HttpException, HttpStatus } from '@nestjs/common';

export class AppException extends HttpException {
  private additionalData: any;
  private errorCode: ErrorCode;
  private detail: any;

  constructor(errorCode: ErrorCode, message?: string, detail?: any) {
    super(
      {
        code: errorCode.code,
        message: message || errorCode.message,
        detail: detail,
      },
      errorCode.status,
    );
  }

  withData(data: any): this {
    this.additionalData = data;
    return this;
  }

  getAdditionalData(): any {
    return this.additionalData;
  }

  getErrorCode(): ErrorCode {
    return this.errorCode;
  }
}

export class ErrorCode {
  code: number;
  message: string;
  status: HttpStatus;

  constructor(code: number, message: string, status: HttpStatus) {
    this.code = code;
    this.message = message;
    this.status = status;
  }

  static INTERNAL_SERVER_ERROR = new ErrorCode(
    9999,
    'Lõi không xác định.',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static DATABASE_ERROR = new ErrorCode(
    9999,
    'Lỗi Database.',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static RESOURCE_NOT_FOUND = new ErrorCode(
    10000,
    'Resource not found.',
    HttpStatus.NOT_FOUND,
  );

  static RATE_LIMIT_EXCEEDED = new ErrorCode(
    10002,
    'Too many requests. Please try again later.',
    HttpStatus.TOO_MANY_REQUESTS,
  );

  static TOKEN_NOT_FOUND = new ErrorCode(
    10003,
    'Authorization token not found.',
    HttpStatus.UNAUTHORIZED,
  );

  static EXTERNAL_SERVICE_ERROR = new ErrorCode(
    10004,
    'Internal server error. Please try again later.',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static VALIDATION_ERROR = new ErrorCode(
    10005,
    'Validation failed',
    HttpStatus.BAD_REQUEST,
  );

  static CLOUD_FLARE_ERROR_UPLOAD = new ErrorCode(
    10006,
    'Lỗi khi tải tệp lên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static FILE_TYPE_NOT_FOUND = new ErrorCode(
    10007,
    'Loại tệp không được hỗ trợ',
    HttpStatus.BAD_REQUEST,
  );

  static CDN_URL_GENERATION_ERROR = new ErrorCode(
    10008,
    'Lỗi khi tạo URL CDN',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static OPENAI_QUOTA_EXCEEDED = new ErrorCode(
    10009,
    'Đã vượt quá giới hạn sử dụng OpenAI API',
    HttpStatus.TOO_MANY_REQUESTS,
  );

  static OPENAI_TIMEOUT = new ErrorCode(
    10010,
    'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
    HttpStatus.GATEWAY_TIMEOUT,
  );

  static OPENAI_API_ERROR = new ErrorCode(
    10011,
    'Lỗi khi gọi OpenAI API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static RECAPTCHA_VERIFICATION_FAILED = new ErrorCode(
    10012,
    'Xác thực reCAPTCHA thất bại',
    HttpStatus.BAD_REQUEST,
  );

  static CLOUD_FLARE_ERROR_DELETE = new ErrorCode(
    10008,
    'Lỗi khi xóa tệp trên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static CLOUD_FLARE_ERROR_DOWNLOAD = new ErrorCode(
    10009,
    'Lỗi khi tạo URL download từ CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static CLOUD_FLARE_ERROR_COPY = new ErrorCode(
    10010,
    'Lỗi khi sao chép tệp trên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static USER_NOT_VERIFY = new ErrorCode(
    10011,
    'Người dùng chưa xác minh email hoặc số điện thoại',
    HttpStatus.BAD_REQUEST,
  );

  static UNCATEGORIZED_EXCEPTION = new ErrorCode(
    10012,
    'Lỗi không xác định',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static USER_NOT_FOUND = new ErrorCode(
    10013,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
  );

  static EMAIL_OR_PASSWORD_NOT_VALID = new ErrorCode(
    10014,
    'Email hoặc mật khẩu không chính xác',
    HttpStatus.BAD_REQUEST,
  );

  static USER_HAS_BLOCKED = new ErrorCode(
    10015,
    'Tài khoản của bạn đã bị khóa',
    HttpStatus.BAD_REQUEST,
  );

  static EMPLOYEE_HAS_BLOCKED = new ErrorCode(
    10016,
    'Tài khoản của bạn đã bị khóa',
    HttpStatus.BAD_REQUEST,
  );

  static EMAIL_ALREADY_EXISTS = new ErrorCode(
    10017,
    'Email đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  );

  static PHONE_NUMBER_ALREADY_EXISTS = new ErrorCode(
    10018,
    'Số điện thoại đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  );

  static TOKEN_INVALID_OR_EXPIRED = new ErrorCode(
    10019,
    'Token không hợp lệ hoặc đã hết hạn',
    HttpStatus.BAD_REQUEST,
  );

  static OTP_NOT_VALID = new ErrorCode(
    10020,
    'Mã OTP không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static AUDIENCE_NOT_FOUND = new ErrorCode(
    10021,
    'Không tìm thấy audience',
    HttpStatus.NOT_FOUND,
  );

  static EMPLOYEE_NOT_FOUND = new ErrorCode(
    10022,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  );

  static POINT_NOT_FOUND = new ErrorCode(
    10023,
    'Không tìm thấy gói point',
    HttpStatus.NOT_FOUND,
  );

  static INVALID_POINT_DATA = new ErrorCode(
    10024,
    'Dữ liệu gói point không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static VECTOR_STORE_NOT_FOUND = new ErrorCode(
    10025,
    'Không tìm thấy vector store',
    HttpStatus.NOT_FOUND,
  );

  static CAMPAIGN_VALIDATION_ERROR = new ErrorCode(
    10026,
    'Dữ liệu campaign không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static SEGMENT_NOT_FOUND = new ErrorCode(
    10027,
    'Không tìm thấy segment',
    HttpStatus.NOT_FOUND,
  );

  static TAG_NOT_FOUND = new ErrorCode(
    10028,
    'Không tìm thấy tag',
    HttpStatus.NOT_FOUND,
  );

  static RECAPTCHA_CONFIG_ERROR = new ErrorCode(
    10029,
    'Lỗi cấu hình reCAPTCHA',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static REDIS_ERROR = new ErrorCode(
    10030,
    'Lỗi khi thao tác với Redis',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static EMAIL_SENDING_ERROR = new ErrorCode(
    10031,
    'Lỗi khi gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static PDF_PROCESSING_ERROR = new ErrorCode(
    10032,
    'Lỗi khi xử lý file PDF',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static SMS_SENDING_ERROR = new ErrorCode(
    10033,
    'Lỗi khi gửi SMS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static UNAUTHORIZED_ACCESS = new ErrorCode(
    10034,
    'Không có quyền truy cập',
    HttpStatus.UNAUTHORIZED,
  );

  static CONFIGURATION_ERROR = new ErrorCode(
    10035,
    'Lỗi khi lấy cấu hình',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );
  static FORBIDDEN = new ErrorCode(
    10036,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  );
  static MEDIA_NOT_FOUND = new ErrorCode(
    10037,
    'Không tìm thấy media',
    HttpStatus.NOT_FOUND,
  );
  static FILE_SIZE_EXCEEDED = new ErrorCode(
    10038,
    'Kích thước tệp quá lớn',
    HttpStatus.BAD_REQUEST,
  );
  static NOT_FOUND = new ErrorCode(
    10039,
    'Không tìm thấy',
    HttpStatus.NOT_FOUND,
  );
}
