import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';

export interface RetryOptions {
  maxAttempts?: number;
  delay?: number;
  exponentialBackoff?: boolean;
  retryableErrors?: any[];
}

@Injectable()
export class RetryService {
  private readonly logger = new Logger(RetryService.name);

  async execute<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {},
    context: string,
  ): Promise<T> {
    const {
      maxAttempts = 3,
      delay = 1000,
      exponentialBackoff = true,
      retryableErrors = [
        'TimeoutError',
        'NetworkError',
        'ConnectionError',
        'ECONNRESET',
        'ETIMEDOUT',
      ],
    } = options;

    let attempt = 1;
    let lastError: any;

    while (attempt <= maxAttempts) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (error instanceof AppException) {
          throw error;
        }

        const shouldRetry = retryableErrors.some(
          (errorType) =>
            error.name === errorType ||
            error.code === errorType ||
            error.message.includes(errorType),
        );

        if (attempt === maxAttempts || !shouldRetry) {
          this.logger.error(
            `[${context}] All retry attempts failed. Last error: ${error.message}`,
          );
          throw error;
        }

        const nextDelay = exponentialBackoff
          ? delay * Math.pow(2, attempt - 1)
          : delay;

        this.logger.warn(
          `[${context}] Attempt ${attempt}/${maxAttempts} failed. ` +
            `Retrying in ${nextDelay}ms. Error: ${error.message}`,
        );

        await new Promise((resolve) => setTimeout(resolve, nextDelay));
        attempt++;
      }
    }

    throw lastError;
  }
}
