import { Global, Module } from '@nestjs/common';
import { CircuitBreakerService } from './circuit-breaker.service';
import { RetryService } from './retry.service';

/**
 * Module cung cấp các công cụ để xử lý lỗi và tăng tính ổn định của hệ thống
 */
@Global()
@Module({
  providers: [CircuitBreakerService, RetryService],
  exports: [CircuitBreakerService, RetryService],
})
export class ResilienceModule {}
