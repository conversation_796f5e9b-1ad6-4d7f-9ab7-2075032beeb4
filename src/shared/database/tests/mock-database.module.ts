import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigService, ConfigType, DatabaseConfig } from '../../../config';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.getConfig<DatabaseConfig>(
          ConfigType.Database,
        );

        return {
          type: 'postgres',
          host: dbConfig.host,
          port: dbConfig.port,
          username: dbConfig.username,
          password: dbConfig.password,
          database: dbConfig.database,
          autoLoadEntities: true,
          synchronize: false,
          ssl: {
            rejectUnauthorized: !dbConfig.ssl,
          },
        };
      },
    }),
  ],
})
export class MockDatabaseModule {}
