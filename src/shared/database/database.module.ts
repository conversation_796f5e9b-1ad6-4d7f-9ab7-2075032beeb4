import { Logger, Modu<PERSON>, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigService, ConfigType, DatabaseConfig } from '@config';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { TypeOrmTransactionalModule } from '@modules/database/typeorm-transactional.module';

/**
 * Module quản lý kết nối database
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.getConfig<DatabaseConfig>(
          ConfigType.Database,
        );
        return {
          type: 'postgres',
          host: dbConfig.host,
          port: dbConfig.port,
          username: dbConfig.username,
          password: dbConfig.password,
          database: dbConfig.database,
          ssl: {
            rejectUnauthorized: !dbConfig.ssl,
          },
          autoLoadEntities: true,
          synchronize: false,
          namingStrategy: new SnakeNamingStrategy(),
          logger: 'advanced-console',
          logging: true,
        };
      },
    }),
    TypeOrmTransactionalModule,
  ],
  providers: [],
  exports: [TypeOrmTransactionalModule],
})
export class DatabaseModule {}
