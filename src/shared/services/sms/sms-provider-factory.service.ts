import { Injectable } from '@nestjs/common';
import { ISmsProvider } from './sms-provider.interface';
import { SpeedSmsProvider } from './speed-sms-provider.service';
import { TwilioProvider } from './twilio-provider.service';
import { VonageProvider } from './vonage-provider.service';
import { FptSmsProvider } from './fpt-sms-provider.service';

/**
 * Énumération des types de fournisseurs SMS supportés
 */
export enum SmsProviderType {
  SPEED_SMS = 'SPEED_SMS',
  TWILIO = 'TWILIO',
  VONAGE = 'VONAGE',
  FPT_SMS = 'FPT_SMS',
}

/**
 * Service de fabrique pour créer des instances de fournisseurs SMS
 */
@Injectable()
export class SmsProviderFactory {
  constructor(
    private readonly speedSmsProvider: SpeedSmsProvider,
    private readonly twilioProvider: TwilioProvider,
    private readonly vonageProvider: VonageProvider,
    private readonly fptSmsProvider: FptSmsProvider,
  ) {}

  /**
   * Crée une instance de fournisseur SMS en fonction du type spécifié
   * @param providerType Type de fournisseur SMS
   * @returns Instance du fournisseur SMS
   * @throws Error si le type de fournisseur n'est pas supporté
   */
  createProvider(providerType: SmsProviderType): ISmsProvider {
    switch (providerType) {
      case SmsProviderType.SPEED_SMS:
        return this.speedSmsProvider;
      case SmsProviderType.TWILIO:
        return this.twilioProvider;
      case SmsProviderType.VONAGE:
        return this.vonageProvider;
      case SmsProviderType.FPT_SMS:
        return this.fptSmsProvider;
      default:
        throw new Error(
          `Type de fournisseur SMS non supporté: ${providerType}`,
        );
    }
  }

  /**
   * Crée une instance de fournisseur SMS en fonction du nom du fournisseur
   * @param providerName Nom du fournisseur SMS
   * @returns Instance du fournisseur SMS
   * @throws Error si le nom du fournisseur n'est pas supporté
   */
  createProviderByName(providerName: string): ISmsProvider {
    const normalizedName = providerName
      .toUpperCase()
      .replace(/[^A-Z0-9_]/g, '_');

    try {
      const providerType = SmsProviderType[normalizedName];
      return this.createProvider(providerType);
    } catch (error) {
      throw new Error(`Nom de fournisseur SMS non supporté: ${providerName}`);
    }
  }
}
