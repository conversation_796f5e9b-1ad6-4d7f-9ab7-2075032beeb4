import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@/config/config.service';
import * as nodemailer from 'nodemailer';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@/common/exceptions/app.exception';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Service để gửi email
 */
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly transporter: nodemailer.Transporter;
  private readonly defaultFrom: string;

  constructor(private readonly configService: ConfigService) {
    // Lấy cấu hình email từ biến môi trường
    const emailConfig = {
      smtpHost:
        this.configService.appConfig.services.email?.smtpHost ||
        process.env.SMTP_HOST,
      smtpPort:
        this.configService.appConfig.services.email?.smtpPort ||
        Number(process.env.SMTP_PORT),
      smtpUser:
        this.configService.appConfig.services.email?.smtpUser ||
        process.env.SMTP_USER,
      smtpPass:
        this.configService.appConfig.services.email?.smtpPass ||
        process.env.SMTP_PASS,
    };

    if (!emailConfig.smtpHost || !emailConfig.smtpPort) {
      this.logger.warn(
        'Email configuration is missing. Email service will not work properly.',
      );
    }

    this.defaultFrom = emailConfig.smtpUser || '<EMAIL>';

    try {
      this.transporter = nodemailer.createTransport({
        host: emailConfig.smtpHost,
        port: emailConfig.smtpPort,
        secure: emailConfig.smtpPort === 465, // true for 465, false for other ports
        auth: {
          user: emailConfig.smtpUser,
          pass: emailConfig.smtpPass,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to initialize email transporter: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Kiểm tra xem dịch vụ email đã được cấu hình đúng chưa
   * @returns true nếu đã cấu hình đúng, false nếu chưa
   */
  isConfigured(): boolean {
    return !!this.transporter;
  }

  /**
   * Gửi email
   * @param options Tùy chọn email
   * @returns Kết quả gửi email
   * @throws AppException nếu dịch vụ email chưa được cấu hình hoặc gặp lỗi khi gửi
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.isConfigured()) {
      this.logger.error('Email service is not properly configured');
      throw new AppException(
        ErrorCode.CONFIGURATION_ERROR,
        'Dịch vụ email chưa được cấu hình đúng',
      );
    }

    try {
      const mailOptions = {
        from: options.from || this.defaultFrom,
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'Không thể gửi email',
      );
    }
  }

  /**
   * Gửi email xác thực
   * @param to Email người nhận
   * @param verificationToken Token xác thực
   * @param companyName Tên công ty
   * @returns Kết quả gửi email
   */
  async sendVerificationEmail(
    to: string,
    verificationToken: string,
    companyName: string,
  ): Promise<boolean> {
    const subject = 'Xác thực đăng ký tài khoản công ty';

    // Lấy APP_URL từ cấu hình
    const appUrl = this.configService.appConfig?.apiPrefix
      ? `http://localhost:3000/${this.configService.appConfig.apiPrefix}`
      : 'http://localhost:3000';

    const verificationUrl = `${appUrl}/verify-email?token=${verificationToken}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Xác thực đăng ký tài khoản</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
          }
          .header {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
          }
          .content {
            padding: 20px;
          }
          .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
          }
          .url-display {
            word-break: break-all;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Xác thực đăng ký tài khoản</h2>
          </div>
          <div class="content">
            <p>Kính gửi <strong>${companyName}</strong>,</p>
            <p>Cảm ơn bạn đã đăng ký tài khoản trên hệ thống của chúng tôi. Để hoàn tất quá trình đăng ký, vui lòng xác thực địa chỉ email của bạn bằng cách nhấp vào liên kết dưới đây:</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="${verificationUrl}" class="button">Xác thực email</a>
            </div>
            <p>Hoặc bạn có thể sao chép và dán liên kết sau vào trình duyệt của bạn:</p>
            <div class="url-display">
              ${verificationUrl}
            </div>
            <p>Liên kết này sẽ hết hạn sau 24 giờ.</p>
            <p>Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.</p>
            <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} RedAI. Tất cả các quyền được bảo lưu.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to,
      subject,
      html,
    });
  }

  /**
   * Gửi email thông báo tạo subdomain thành công
   * @param to Email người nhận
   * @param companyName Tên công ty
   * @param subdomain Subdomain đã tạo
   * @param loginUrl URL đăng nhập
   * @returns Kết quả gửi email
   */
  async sendSubdomainCreatedEmail(
    to: string,
    companyName: string,
    subdomain: string,
    loginUrl: string,
  ): Promise<boolean> {
    const subject = 'Thông báo tạo subdomain thành công';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thông báo tạo subdomain thành công</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
          }
          .header {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
          }
          .content {
            padding: 20px;
          }
          .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
          }
          .url-display {
            word-break: break-all;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 15px 0;
          }
          .highlight {
            font-weight: bold;
            color: #4CAF50;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Thông báo tạo subdomain thành công</h2>
          </div>
          <div class="content">
            <p>Kính gửi <strong>${companyName}</strong>,</p>
            <p>Chúng tôi vui mừng thông báo rằng subdomain của công ty bạn đã được tạo thành công.</p>
            <p>Thông tin subdomain của bạn:</p>
            <div class="url-display">
              <span class="highlight">https://${subdomain}.redai.com</span>
            </div>
            <p>Bạn có thể truy cập vào hệ thống bằng cách nhấp vào liên kết dưới đây:</p>
            <div style="text-align: center; margin: 25px 0;">
              <a href="${loginUrl}" class="button">Đăng nhập vào hệ thống</a>
            </div>
            <p>Hoặc bạn có thể sao chép và dán liên kết sau vào trình duyệt của bạn:</p>
            <div class="url-display">
              ${loginUrl}
            </div>
            <p>Nếu bạn có bất kỳ câu hỏi hoặc cần hỗ trợ, vui lòng liên hệ với chúng tôi.</p>
            <p>Trân trọng,<br>Đội ngũ hỗ trợ</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} RedAI. Tất cả các quyền được bảo lưu.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to,
      subject,
      html,
    });
  }
}
