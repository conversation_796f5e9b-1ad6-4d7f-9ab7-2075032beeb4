import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  TelegramBotCommand,
  TelegramBotInfo,
  TelegramFile,
  TelegramMessage,
  TelegramResponse,
  TelegramSendDocumentOptions,
  TelegramSendMessageOptions,
  TelegramSendPhotoOptions,
  TelegramSetWebhookOptions,
  TelegramUpdate,
  TelegramWebhookInfo,
} from './telegram.interface';
import * as FormData from 'form-data';
import * as fs from 'fs';

/**
 * Service cung cấp các phương thức để tương tác với Telegram Bot API
 */
@Injectable()
export class TelegramService {
  private readonly logger = new Logger(TelegramService.name);
  private readonly apiUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.apiUrl = 'https://api.telegram.org/bot';
  }

  /**
   * Tạo URL API với token bot
   * @param botToken Token của bot
   * @param method Phương thức API
   * @returns URL API đầy đủ
   */
  private getApiUrl(botToken: string, method: string): string {
    return `${this.apiUrl}${botToken}/${method}`;
  }

  /**
   * Xử lý lỗi từ Telegram API
   * @param error Lỗi từ Axios
   * @param operation Tên thao tác đang thực hiện
   * @returns Không bao giờ trả về, luôn throw exception
   */
  private handleApiError(error: any, operation: string): never {
    if (error instanceof AppException) {
      throw error;
    }

    const axiosError = error as AxiosError;
    let errorMessage = `Lỗi khi ${operation}`;
    const errorCode = ErrorCode.EXTERNAL_SERVICE_ERROR;

    if (axiosError.response) {
      const responseData = axiosError.response.data as any;
      if (responseData && responseData.description) {
        errorMessage = `Lỗi khi ${operation}: ${responseData.description}`;
      }
    }

    this.logger.error(errorMessage, axiosError.stack);
    throw new AppException(errorCode, errorMessage);
  }

  /**
   * Lấy thông tin về bot
   * @param botToken Token của bot
   * @returns Thông tin về bot
   */
  async getMe(botToken: string): Promise<TelegramBotInfo> {
    try {
      const url = this.getApiUrl(botToken, 'getMe');
      const response = await lastValueFrom(
        this.httpService.get<TelegramResponse<TelegramBotInfo>>(url),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin bot: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'lấy thông tin bot');
    }
  }

  /**
   * Gửi tin nhắn văn bản
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param text Nội dung tin nhắn
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendMessage(
    botToken: string,
    chatId: number | string,
    text: string,
    options?: TelegramSendMessageOptions,
  ): Promise<TelegramMessage> {
    try {
      const url = this.getApiUrl(botToken, 'sendMessage');
      const data = {
        chat_id: chatId,
        text,
        ...options,
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramMessage>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'gửi tin nhắn');
    }
  }

  /**
   * Gửi ảnh
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param photo URL hoặc file_id của ảnh
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendPhoto(
    botToken: string,
    chatId: number | string,
    photo: string,
    options?: TelegramSendPhotoOptions,
  ): Promise<TelegramMessage> {
    try {
      const url = this.getApiUrl(botToken, 'sendPhoto');
      const data = {
        chat_id: chatId,
        photo,
        ...options,
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramMessage>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi ảnh: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'gửi ảnh');
    }
  }

  /**
   * Gửi ảnh từ file local
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param filePath Đường dẫn đến file ảnh
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendPhotoFromFile(
    botToken: string,
    chatId: number | string,
    filePath: string,
    options?: TelegramSendPhotoOptions,
  ): Promise<TelegramMessage> {
    try {
      const url = this.getApiUrl(botToken, 'sendPhoto');
      const formData = new FormData();
      formData.append('chat_id', chatId.toString());
      formData.append('photo', fs.createReadStream(filePath));

      if (options) {
        Object.entries(options).forEach(([key, value]) => {
          if (value !== undefined) {
            if (typeof value === 'object') {
              formData.append(key, JSON.stringify(value));
            } else {
              formData.append(key, value.toString());
            }
          }
        });
      }

      const config = {
        headers: {
          ...formData.getHeaders(),
        },
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramMessage>>(
          url,
          formData,
          config,
        ),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi ảnh từ file: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'gửi ảnh từ file');
    }
  }

  /**
   * Gửi document
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param document URL hoặc file_id của document
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendDocument(
    botToken: string,
    chatId: number | string,
    document: string,
    options?: TelegramSendDocumentOptions,
  ): Promise<TelegramMessage> {
    try {
      const url = this.getApiUrl(botToken, 'sendDocument');
      const data = {
        chat_id: chatId,
        document,
        ...options,
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramMessage>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi document: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'gửi document');
    }
  }

  /**
   * Gửi document từ file local
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param filePath Đường dẫn đến file
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendDocumentFromFile(
    botToken: string,
    chatId: number | string,
    filePath: string,
    options?: TelegramSendDocumentOptions,
  ): Promise<TelegramMessage> {
    try {
      const url = this.getApiUrl(botToken, 'sendDocument');
      const formData = new FormData();
      formData.append('chat_id', chatId.toString());
      formData.append('document', fs.createReadStream(filePath));

      if (options) {
        Object.entries(options).forEach(([key, value]) => {
          if (value !== undefined) {
            if (typeof value === 'object') {
              formData.append(key, JSON.stringify(value));
            } else {
              formData.append(key, value.toString());
            }
          }
        });
      }

      const config = {
        headers: {
          ...formData.getHeaders(),
        },
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramMessage>>(
          url,
          formData,
          config,
        ),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi document từ file: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'gửi document từ file');
    }
  }

  /**
   * Thiết lập webhook
   * @param botToken Token của bot
   * @param url URL webhook
   * @param options Tùy chọn bổ sung
   * @returns Kết quả thiết lập webhook
   */
  async setWebhook(
    botToken: string,
    url: string,
    options?: TelegramSetWebhookOptions,
  ): Promise<boolean> {
    try {
      const apiUrl = this.getApiUrl(botToken, 'setWebhook');
      const data = {
        url,
        ...options,
      };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<boolean>>(apiUrl, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi thiết lập webhook: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'thiết lập webhook');
    }
  }

  /**
   * Xóa webhook
   * @param botToken Token của bot
   * @param dropPendingUpdates Có xóa các update đang chờ xử lý không
   * @returns Kết quả xóa webhook
   */
  async deleteWebhook(
    botToken: string,
    dropPendingUpdates?: boolean,
  ): Promise<boolean> {
    try {
      const url = this.getApiUrl(botToken, 'deleteWebhook');
      const data = dropPendingUpdates ? { drop_pending_updates: true } : {};

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<boolean>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi xóa webhook: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'xóa webhook');
    }
  }

  /**
   * Lấy thông tin webhook
   * @param botToken Token của bot
   * @returns Thông tin về webhook
   */
  async getWebhookInfo(botToken: string): Promise<TelegramWebhookInfo> {
    try {
      const url = this.getApiUrl(botToken, 'getWebhookInfo');
      const response = await lastValueFrom(
        this.httpService.get<TelegramResponse<TelegramWebhookInfo>>(url),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin webhook: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'lấy thông tin webhook');
    }
  }

  /**
   * Lấy updates
   * @param botToken Token của bot
   * @param offset ID của update tiếp theo
   * @param limit Số lượng update tối đa
   * @param timeout Thời gian chờ (long polling)
   * @param allowedUpdates Các loại update được phép
   * @returns Danh sách các update
   */
  async getUpdates(
    botToken: string,
    offset?: number,
    limit?: number,
    timeout?: number,
    allowedUpdates?: string[],
  ): Promise<TelegramUpdate[]> {
    try {
      const url = this.getApiUrl(botToken, 'getUpdates');
      const data: any = {};

      if (offset !== undefined) data.offset = offset;
      if (limit !== undefined) data.limit = limit;
      if (timeout !== undefined) data.timeout = timeout;
      if (allowedUpdates !== undefined) data.allowed_updates = allowedUpdates;

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramUpdate[]>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy updates: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'lấy updates');
    }
  }

  /**
   * Thiết lập các lệnh cho bot
   * @param botToken Token của bot
   * @param commands Danh sách các lệnh
   * @returns Kết quả thiết lập lệnh
   */
  async setMyCommands(
    botToken: string,
    commands: TelegramBotCommand[],
  ): Promise<boolean> {
    try {
      const url = this.getApiUrl(botToken, 'setMyCommands');
      const data = { commands };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<boolean>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi thiết lập lệnh: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'thiết lập lệnh');
    }
  }

  /**
   * Lấy danh sách các lệnh của bot
   * @param botToken Token của bot
   * @returns Danh sách các lệnh
   */
  async getMyCommands(botToken: string): Promise<TelegramBotCommand[]> {
    try {
      const url = this.getApiUrl(botToken, 'getMyCommands');
      const response = await lastValueFrom(
        this.httpService.get<TelegramResponse<TelegramBotCommand[]>>(url),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách lệnh: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'lấy danh sách lệnh');
    }
  }

  /**
   * Xóa tất cả các lệnh của bot
   * @param botToken Token của bot
   * @returns Kết quả xóa lệnh
   */
  async deleteMyCommands(botToken: string): Promise<boolean> {
    try {
      const url = this.getApiUrl(botToken, 'deleteMyCommands');
      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<boolean>>(url, {}),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi xóa lệnh: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'xóa lệnh');
    }
  }

  /**
   * Lấy thông tin về file
   * @param botToken Token của bot
   * @param fileId ID của file
   * @returns Thông tin về file
   */
  async getFile(botToken: string, fileId: string): Promise<TelegramFile> {
    try {
      const url = this.getApiUrl(botToken, 'getFile');
      const data = { file_id: fileId };

      const response = await lastValueFrom(
        this.httpService.post<TelegramResponse<TelegramFile>>(url, data),
      );

      if (!response.data.ok) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin file: ${response.data.description}`,
        );
      }

      return response.data.result;
    } catch (error) {
      return this.handleApiError(error, 'lấy thông tin file');
    }
  }

  /**
   * Lấy URL để tải file
   * @param botToken Token của bot
   * @param filePath Đường dẫn đến file
   * @returns URL để tải file
   */
  getFileUrl(botToken: string, filePath: string): string {
    return `https://api.telegram.org/file/bot${botToken}/${filePath}`;
  }

  /**
   * Tải file từ Telegram
   * @param botToken Token của bot
   * @param fileId ID của file
   * @returns Buffer chứa dữ liệu file
   */
  async downloadFile(botToken: string, fileId: string): Promise<Buffer> {
    try {
      // Lấy thông tin file
      const fileInfo = await this.getFile(botToken, fileId);

      if (!fileInfo.file_path) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy đường dẫn file',
        );
      }

      // Lấy URL để tải file
      const fileUrl = this.getFileUrl(botToken, fileInfo.file_path);

      // Tải file
      const response = await lastValueFrom(
        this.httpService.get(fileUrl, { responseType: 'arraybuffer' }),
      );

      return Buffer.from(response.data);
    } catch (error) {
      return this.handleApiError(error, 'tải file');
    }
  }
}
