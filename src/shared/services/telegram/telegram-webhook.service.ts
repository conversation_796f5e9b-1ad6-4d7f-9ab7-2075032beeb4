import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TelegramUpdate } from './telegram.interface';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Service xử lý các webhook từ Telegram
 */
@Injectable()
export class TelegramWebhookService {
  private readonly logger = new Logger(TelegramWebhookService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Xác thực webhook request
   * @param secretToken Token bí mật từ request header
   * @param botId ID của bot
   * @returns True nếu xác thực thành công
   */
  async validateWebhookRequest(
    secretToken: string,
    botId: number,
  ): Promise<boolean> {
    try {
      // Trong môi trường thực tế, bạn sẽ cần lấy secret token từ database dựa trên botId
      const expectedToken = this.configService.get<string>(
        'TELEGRAM_WEBHOOK_SECRET_TOKEN',
        '',
      );

      if (!expectedToken) {
        this.logger.warn('TELEGRAM_WEBHOOK_SECRET_TOKEN không được cấu hình');
        return true; // Cho phép nếu không có cấu hình
      }

      return secretToken === expectedToken;
    } catch (error) {
      this.logger.error(
        `Error validating webhook request: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Xử lý update từ webhook
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  async processUpdate(
    update: TelegramUpdate,
    botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.debug(
        `Processing update for bot ${botId}: ${JSON.stringify(update)}`,
      );

      // Xử lý các loại update khác nhau
      if (update.message) {
        return await this.processMessage(update, botId);
      } else if (update.callback_query) {
        return await this.processCallbackQuery(update, botId);
      } else if (update.edited_message) {
        return await this.processEditedMessage(update, botId);
      } else if (update.channel_post) {
        return await this.processChannelPost(update, botId);
      } else if (update.edited_channel_post) {
        return await this.processEditedChannelPost(update, botId);
      } else {
        return { success: true, message: 'Không có xử lý cho loại update này' };
      }
    } catch (error) {
      this.logger.error(
        `Error processing update: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        return { success: false, message: error.message };
      }
      return { success: false, message: 'Lỗi khi xử lý update' };
    }
  }

  /**
   * Xử lý tin nhắn
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processMessage(
    update: TelegramUpdate,
    botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const message = update.message;

      if (!message) {
        return { success: false, message: 'Tin nhắn không hợp lệ' };
      }

      // Lưu thông tin chat và message vào database
      // Trong môi trường thực tế, bạn sẽ cần lưu thông tin này vào database

      // Xử lý các loại tin nhắn khác nhau
      if (message.text) {
        // Xử lý tin nhắn văn bản
        if (message.text.startsWith('/')) {
          // Xử lý lệnh
          return await this.processCommand(message.text, update, botId);
        } else {
          // Xử lý tin nhắn thông thường
          return await this.processTextMessage(message.text, update, botId);
        }
      } else if (message.photo && message.photo.length > 0) {
        // Xử lý ảnh
        return await this.processPhotoMessage(update, botId);
      } else if (message.document) {
        // Xử lý document
        return await this.processDocumentMessage(update, botId);
      } else {
        // Xử lý các loại tin nhắn khác
        return {
          success: true,
          message: 'Không có xử lý cho loại tin nhắn này',
        };
      }
    } catch (error) {
      this.logger.error(
        `Error processing message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        return { success: false, message: error.message };
      }
      return { success: false, message: 'Lỗi khi xử lý tin nhắn' };
    }
  }

  /**
   * Xử lý lệnh
   * @param command Lệnh từ tin nhắn
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processCommand(
    command: string,
    update: TelegramUpdate,
    botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // Xử lý các lệnh khác nhau
      const commandName = command.split(' ')[0].substring(1).toLowerCase();

      switch (commandName) {
        case 'start':
          return await this.processStartCommand(update, botId);
        case 'help':
          return await this.processHelpCommand(update, botId);
        case 'settings':
          return await this.processSettingsCommand(update, botId);
        default:
          // Xử lý lệnh tùy chỉnh
          return await this.processCustomCommand(commandName, update, botId);
      }
    } catch (error) {
      this.logger.error(
        `Error processing command: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        return { success: false, message: error.message };
      }
      return { success: false, message: 'Lỗi khi xử lý lệnh' };
    }
  }

  /**
   * Xử lý lệnh /start
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processStartCommand(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần gửi tin nhắn chào mừng và hướng dẫn sử dụng
    return { success: true, message: 'Đã xử lý lệnh /start' };
  }

  /**
   * Xử lý lệnh /help
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processHelpCommand(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần gửi tin nhắn hướng dẫn sử dụng
    return { success: true, message: 'Đã xử lý lệnh /help' };
  }

  /**
   * Xử lý lệnh /settings
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processSettingsCommand(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần gửi tin nhắn cài đặt
    return { success: true, message: 'Đã xử lý lệnh /settings' };
  }

  /**
   * Xử lý lệnh tùy chỉnh
   * @param commandName Tên lệnh
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processCustomCommand(
    commandName: string,
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần lấy thông tin lệnh từ database và xử lý tương ứng
    return { success: true, message: `Đã xử lý lệnh /${commandName}` };
  }

  /**
   * Xử lý tin nhắn văn bản
   * @param text Nội dung tin nhắn
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processTextMessage(
    _text: string,
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần gửi tin nhắn đến agent để xử lý và phản hồi
    return { success: true, message: 'Đã xử lý tin nhắn văn bản' };
  }

  /**
   * Xử lý tin nhắn ảnh
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processPhotoMessage(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần lưu ảnh và gửi đến agent để xử lý
    return { success: true, message: 'Đã xử lý tin nhắn ảnh' };
  }

  /**
   * Xử lý tin nhắn document
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processDocumentMessage(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần lưu document và gửi đến agent để xử lý
    return { success: true, message: 'Đã xử lý tin nhắn document' };
  }

  /**
   * Xử lý callback query
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processCallbackQuery(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần xử lý callback query từ inline keyboard
    return { success: true, message: 'Đã xử lý callback query' };
  }

  /**
   * Xử lý tin nhắn đã chỉnh sửa
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processEditedMessage(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần cập nhật tin nhắn trong database
    return { success: true, message: 'Đã xử lý tin nhắn đã chỉnh sửa' };
  }

  /**
   * Xử lý tin nhắn từ channel
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processChannelPost(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần xử lý tin nhắn từ channel
    return { success: true, message: 'Đã xử lý tin nhắn từ channel' };
  }

  /**
   * Xử lý tin nhắn đã chỉnh sửa từ channel
   * @param update Update từ Telegram
   * @param botId ID của bot trong hệ thống
   * @returns Kết quả xử lý
   */
  private async processEditedChannelPost(
    _update: TelegramUpdate,
    _botId: number,
  ): Promise<{ success: boolean; message?: string }> {
    // Trong môi trường thực tế, bạn sẽ cần cập nhật tin nhắn từ channel trong database
    return {
      success: true,
      message: 'Đã xử lý tin nhắn đã chỉnh sửa từ channel',
    };
  }
}
