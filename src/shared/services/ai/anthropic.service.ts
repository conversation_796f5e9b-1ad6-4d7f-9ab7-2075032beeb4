import { Injectable, Logger } from '@nestjs/common';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import Anthropic from '@anthropic-ai/sdk';

/**
 * Interface for Anthropic Model
 */
interface AnthropicModel {
  id: string;
  name?: string;
  description?: string;
  max_tokens?: number;
  created?: number;
}

/**
 * Interface for Anthropic Models List Response
 */
interface AnthropicModelsResponse {
  models: AnthropicModel[];
}

/**
 * Service for interacting with Anthropic API
 */
@Injectable()
export class AnthropicService {
  private readonly anthropic: Anthropic;
  private readonly logger = new Logger(AnthropicService.name);

  constructor(private readonly configService: ConfigService) {
    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    const apiKey = servicesConfig.anthropic?.apiKey;

    if (!apiKey) {
      this.logger.warn(
        'ANTHROPIC_API_KEY is not defined in environment variables',
      );
    }

    this.anthropic = new Anthropic({
      apiKey: apiKey || '',
    });
  }

  /**
   * Lấy danh sách model từ Anthropic
   * @param options Tùy chọn bổ sung (apiKey)
   * @returns Danh sách model từ Anthropic
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(options?: { apiKey?: string }): Promise<AnthropicModel[]> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Tạo client Anthropic với API key tùy chọn nếu được cung cấp
      const client = options?.apiKey
        ? new Anthropic({ apiKey: options.apiKey })
        : this.anthropic;

      // Gọi API để lấy danh sách model
      const response = await client.models.list({
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      return response.data;
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from Anthropic: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối Anthropic API
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Anthropic API',
        );
      }

      if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Anthropic API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.name === 'NetworkError' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Anthropic API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }

  /**
   * Tạo một instance Anthropic mới với API key tùy chỉnh
   * @param apiKey API key tùy chỉnh
   * @returns Instance Anthropic mới
   */
  createClient(apiKey: string): Anthropic {
    if (!apiKey) {
      this.logger.warn('Empty API key provided to createClient');
    }

    return new Anthropic({
      apiKey: apiKey || '',
    });
  }
}
