import { Injectable, Logger } from '@nestjs/common';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { S3Service } from '../s3.service';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import OpenAI from 'openai';
import { VectorStoreFile } from 'openai/resources/vector-stores';
import { Model } from 'openai/resources/models';
import { run } from '@openai/agents';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { HumanMessage } from '@langchain/core/messages';

/**
 * Interface for OpenAI Chat Completion Response
 */
interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string | null;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Interface for Vector Store Configuration
 */
interface VectorStoreConfig {
  name: string;
  description?: string;
}

interface VectorStoreResponse {
  vectorStoreId: string;
  fileId?: string | string[];
}

/**
 * Interface for OpenAI Model
 */
interface OpenAIModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  permission: {
    id: string;
    object: string;
    created: number;
    allow_create_engine: boolean;
    allow_sampling: boolean;
    allow_logprobs: boolean;
    allow_search_indices: boolean;
    allow_view: boolean;
    allow_fine_tuning: boolean;
    organization: string;
    group: null | string;
    is_blocking: boolean;
  }[];
  root: string;
  parent: null | string;
}

/**
 * Interface for OpenAI Models List Response
 */
interface OpenAIModelsResponse {
  object: string;
  data: OpenAIModel[];
}

/**
 * Interface for Embedding Response
 */
interface EmbeddingResponse {
  data: {
    embedding: number[];
    index: number;
    object: string;
  }[];
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

@Injectable()
export class OpenAiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenAiService.name);
  private lastResponseId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    this.openai = new OpenAI({
      apiKey: servicesConfig.openai.apiKey,
      organization: servicesConfig.openai.organizationId,
    });
  }

  /**
   * Tạo một vector store mới trong OpenAI
   * @param config Cấu hình cho vector store
   * @returns Thông tin về vector store đã tạo
   * @throws AppException nếu có lỗi khi tạo vector store
   */
  async createVectorStore(
    config: VectorStoreConfig,
  ): Promise<VectorStoreResponse> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để tạo vector store
      const response = await this.openai.vectorStores.create(
        {
          name: config.name,
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      this.logger.log(`Vector store created successfully: ${response.id}`);
      return {
        vectorStoreId: response.id,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error creating vector store: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo vector store: ' + error.message,
      );
    }
  }

  /**
   * Tạo một vector store mới trong OpenAI và gán file vào vector store
   * @param config Cấu hình cho vector store
   * @param fileId ID của file trên OpenAI đã tồn tại
   * @returns Thông tin về vector store đã tạo và file đã gán
   * @throws AppException nếu có lỗi khi tạo vector store hoặc gán file
   */
  async createVectorStoreWithFile(
    config: VectorStoreConfig,
    fileId: string,
  ): Promise<VectorStoreResponse> {
    try {
      // Kiểm tra fileId
      if (!fileId || typeof fileId !== 'string') {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'File ID không hợp lệ',
          { fileId },
        );
      }

      // Bước 1: Tạo vector store
      const vectorStore = await this.createVectorStore(config);

      try {
        // Bước 2: Gán file vào vector store
        await this.attachFileToVectorStore(vectorStore.vectorStoreId, fileId);

        return {
          vectorStoreId: vectorStore.vectorStoreId,
        };
      } catch (attachError: any) {
        // Nếu gán file thất bại, xóa vector store đã tạo để tránh rác dữ liệu
        try {
          await this.deleteVectorStore(vectorStore.vectorStoreId);
          this.logger.warn(
            `Deleted vector store ${vectorStore.vectorStoreId} due to file attachment failure`,
          );
        } catch (deleteError) {
          this.logger.error(
            `Failed to delete vector store ${vectorStore.vectorStoreId} after file attachment failure: ${deleteError.message}`,
            deleteError.stack,
          );
        }

        // Ném lại lỗi gán file
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Lỗi khi gán file vào vector store: ${attachError.message}`,
          process.env.NODE_ENV === 'production'
            ? undefined
            : { error: attachError.message, stack: attachError.stack },
        );
      }
    } catch (error: any) {
      // Xử lý lỗi từ createVectorStore hoặc các lỗi khác
      if (error instanceof AppException) {
        throw error; // Nếu đã là AppException, ném lại
      }

      this.logger.error(
        `Error creating vector store with file: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        `Lỗi khi tạo vector store với file: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Tạo một vector store mới trong OpenAI và gán nhiều file vào vector store
   * @param config Cấu hình cho vector store
   * @param fileIds Danh sách các ID của file trên OpenAI đã tồn tại
   * @returns Thông tin về vector store đã tạo và số lượng file đã gán thành công
   * @throws AppException nếu có lỗi khi tạo vector store hoặc gán file
   */
  async createVectorStoreWithMultipleFiles(
    config: VectorStoreConfig,
    fileIds: string[],
  ): Promise<{
    vectorStoreId: string;
    successCount: number;
    errorCount: number;
    errors?: { fileId: string; message: string }[];
  }> {
    try {
      // Kiểm tra fileIds
      if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Danh sách File ID không hợp lệ',
          { fileIds },
        );
      }

      // Bước 1: Tạo vector store
      const vectorStore = await this.createVectorStore(config);
      this.logger.log(
        `Vector store created: ${vectorStore.vectorStoreId}, attaching ${fileIds.length} files`,
      );

      // Bước 2: Gán các file vào vector store
      let successCount = 0;
      const errors: { fileId: string; message: string }[] = [];

      // Xử lý từng file
      for (const fileId of fileIds) {
        try {
          // Kiểm tra fileId
          if (!fileId || typeof fileId !== 'string') {
            errors.push({
              fileId: String(fileId),
              message: 'File ID không hợp lệ',
            });
            continue;
          }

          // Gán file vào vector store
          await this.attachFileToVectorStore(vectorStore.vectorStoreId, fileId);

          successCount++;
          this.logger.log(
            `File attached to vector store: ${vectorStore.vectorStoreId}, file: ${fileId}`,
          );
        } catch (attachError: any) {
          errors.push({
            fileId,
            message: attachError.message || 'Lỗi không xác định khi gán file',
          });
          this.logger.error(
            `Failed to attach file ${fileId} to vector store ${vectorStore.vectorStoreId}: ${attachError.message}`,
            attachError.stack,
          );
        }
      }

      // Kiểm tra kết quả
      if (successCount === 0 && errors.length > 0) {
        // Nếu không có file nào được gán thành công, xóa vector store
        try {
          await this.deleteVectorStore(vectorStore.vectorStoreId);
          this.logger.warn(
            `Deleted vector store ${vectorStore.vectorStoreId} because no files were attached successfully`,
          );
        } catch (deleteError) {
          this.logger.error(
            `Failed to delete vector store ${vectorStore.vectorStoreId}: ${deleteError.message}`,
            deleteError.stack,
          );
        }

        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không thể gán bất kỳ file nào vào vector store`,
          { errors },
        );
      }

      // Ghi log kết quả
      this.logger.log(
        `Vector store created with ${successCount}/${fileIds.length} files: ${vectorStore.vectorStoreId}, ` +
          `${errors.length} errors`,
      );

      return {
        vectorStoreId: vectorStore.vectorStoreId,
        successCount,
        errorCount: errors.length,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error: any) {
      // Xử lý lỗi từ createVectorStore hoặc các lỗi khác
      if (error instanceof AppException) {
        throw error; // Nếu đã là AppException, ném lại
      }

      this.logger.error(
        `Error creating vector store with multiple files: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        `Lỗi khi tạo vector store với nhiều file: ${error.message}`,
        process.env.NODE_ENV === 'production'
          ? undefined
          : { error: error.message, stack: error.stack },
      );
    }
  }

  /**
   * Upload file lên vector store
   * @param vectorStoreId ID của vector store
   * @param fileKey S3 key của file cần upload
   * @returns Thông tin về file đã upload
   * @throws AppException nếu có lỗi khi upload file
   */
  async uploadFileToVectorStore(
    vectorStoreId: string,
    fileKey: string,
  ): Promise<VectorStoreResponse> {
    try {
      // Tạo thư mục tạm để lưu file
      const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'openai-'));
      const tempFilePath = path.join(tempDir, path.basename(fileKey));

      // Tải file trực tiếp từ S3 dưới dạng byte array
      const fileBytes = await this.s3Service.downloadFileAsBytes(fileKey);

      // Ghi file vào thư mục tạm
      fs.writeFileSync(tempFilePath, Buffer.from(fileBytes));

      // Xác định loại file
      const purpose: 'assistants' | 'fine-tune' | 'batch' = 'assistants';

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Upload file lên OpenAI
      const fileUpload = await this.openai.files.create(
        {
          file: fs.createReadStream(tempFilePath),
          purpose: purpose,
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      // Thiết lập timeout cho request tạo file vector
      const controller2 = new AbortController();
      const timeoutId2 = setTimeout(() => controller2.abort(), 60000); // 60 seconds timeout

      // Tạo file vector trong vector store
      await this.openai.vectorStores.files.create(
        vectorStoreId,
        {
          file_id: fileUpload.id,
        },
        {
          signal: controller2.signal,
        },
      );

      clearTimeout(timeoutId2);

      // Xóa file tạm
      try {
        fs.unlinkSync(tempFilePath);
        fs.rmdirSync(tempDir);
      } catch (cleanupError) {
        this.logger.warn(
          `Failed to clean up temp files: ${cleanupError.message}`,
        );
      }

      this.logger.log(
        `File uploaded to vector store successfully: ${fileUpload.id}`,
      );

      return {
        vectorStoreId: vectorStoreId,
        fileId: fileUpload.id,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error uploading file to vector store: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi upload file lên vector store: ' + error.message,
      );
    }
  }

  /**
   * Upload nhiều file lên vector store
   * @param vectorStoreId ID của vector store
   * @param fileKeys Danh sách các S3 key của file cần upload
   * @returns Thông tin về các file đã upload
   */
  async uploadFilesToVectorStore(
    vectorStoreId: string,
    fileKeys: string[],
  ): Promise<{
    successful: VectorStoreResponse[];
    failed: { fileKey: string; error: string }[];
  }> {
    const successful: VectorStoreResponse[] = [];
    const failed: { fileKey: string; error: string }[] = [];

    // Xử lý từng file một
    for (const fileKey of fileKeys) {
      try {
        const result = await this.uploadFileToVectorStore(
          vectorStoreId,
          fileKey,
        );
        successful.push(result);
      } catch (error) {
        this.logger.error(
          `Failed to upload file ${fileKey} to vector store: ${error.message}`,
        );
        failed.push({
          fileKey,
          error: error.message || 'Unknown error',
        });
      }
    }

    return { successful, failed };
  }

  /**
   * Tạo embedding cho văn bản sử dụng OpenAI API
   * @param input Văn bản hoặc mảng văn bản cần tạo embedding
   * @param model Model embedding sử dụng (mặc định là 'text-embedding-ada-002')
   * @returns Kết quả embedding
   * @throws AppException nếu có lỗi khi tạo embedding
   */
  async createEmbedding(
    input: string | string[],
    model: string = 'text-embedding-ada-002',
  ): Promise<EmbeddingResponse> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để tạo embedding
      const response = await this.openai.embeddings.create(
        {
          input,
          model,
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      this.logger.log(
        `Created embeddings for ${Array.isArray(input) ? input.length : 1} texts`,
      );
      return response as EmbeddingResponse;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error creating embeddings: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo embedding: ' + error.message,
      );
    }
  }

  /**
   * Gọi OpenAI Chat Completion API
   * @param messages Danh sách các tin nhắn (system, user, assistant)
   * @param model Model OpenAI sử dụng (mặc định là 'gpt-3.5-turbo')
   * @returns Phản hồi từ OpenAI API
   * @throws AppException nếu có lỗi khi gọi API
   */
  async chatCompletion(
    messages: any[],
    model: string = 'gpt-3.5-turbo',
  ): Promise<ChatCompletionResponse> {
    try {
      // Thiết lập timeout cho request (giảm xuống 10 giây)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 seconds timeout

      // Gọi API để tạo chat completion (tối ưu cho tốc độ)
      const response = await this.openai.chat.completions.create(
        {
          model,
          messages,
          temperature: 0.3, // Giảm temperature để response nhanh hơn
          max_tokens: 300, // Giảm max_tokens để response ngắn gọn hơn
          stream: false, // Không dùng streaming để đơn giản
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      this.logger.log(`Chat completion created for model: ${model}`);
      return response as unknown as ChatCompletionResponse;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error creating chat completion: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo chat completion: ' + error.message,
      );
    }
  }

  /**
   * Tạo chat completion đơn giản với message
   * @param message Tin nhắn của người dùng
   * @param systemPrompt Prompt hệ thống (tùy chọn)
   * @param model Model chat sử dụng (mặc định là 'gpt-3.5-turbo')
   * @returns Câu trả lời từ OpenAI
   */
  async simpleChatCompletion(
    message: string,
    systemPrompt: string = 'Bạn là trợ lý AI hữu ích. Hãy trả lời câu hỏi một cách ngắn gọn và chính xác.',
    model: string = 'gpt-3.5-turbo',
  ): Promise<string> {
    try {
      // Tạo messages cho chat completion
      const messages = [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: message,
        },
      ];

      // Gọi chat completion
      const response = await this.chatCompletion(messages, model);

      // Trả về nội dung của câu trả lời
      return (
        response.choices[0]?.message?.content || 'Không thể tạo câu trả lời.'
      );
    } catch (error: any) {
      this.logger.error(
        `Error in simple chat completion: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo chat completion: ' + error.message,
      );
    }
  }

  /**
   * Tạo chat completion với streaming
   * @param message Tin nhắn của người dùng
   * @param systemPrompt Prompt hệ thống (tùy chọn)
   * @param model Model chat sử dụng (mặc định là 'gpt-3.5-turbo')
   * @param threadId
   * @param tenantId
   * @param userId
   * @param tenantId
   * @param userId
   * @param onChunk Callback function được gọi khi nhận được chunk mới
   * @param agent
   * @returns Promise<string> - Toàn bộ response khi hoàn thành
   */
  async streamChatCompletion(
    message: string,
    systemPrompt: string = 'Bạn là trợ lý AI hữu ích. Hãy trả lời câu hỏi một cách ngắn gọn và chính xác.',
    model: string = 'gpt-3.5-turbo',
    agent: ReturnType<typeof createReactAgent>,
    threadId: string = 'thread_abc123',
    tenantId: number = 1,
    userId: number = 1,
    onChunk?: (chunk: string) => void,
  ): Promise<string> {
    try {

      this.logger.debug(`threadId = ${threadId}`);

      const agentStream = agent.streamEvents(
        {
          messages: [new HumanMessage(message)],
        },
        {
          configurable: {
            thread_id: threadId,
            tenantId: tenantId,
            userId: userId,
          },
          version: 'v2',
        },
      );
      let fullResponses = '';

      for await (const chunk of agentStream) {
        if (chunk.event === 'on_chat_model_stream') {
          const content = chunk.data.chunk.content;
          if (content) {
            fullResponses += content;
            // Gọi callback nếu có
            if (onChunk) {
              onChunk(content);
            }
          }
        }
      }

      this.logger.log(
        `Streaming chat completion completed for model: ${model}`,
      );
      return fullResponses || 'Không thể tạo câu trả lời.';
    } catch (error: any) {
      this.logger.error(
        `Error in streaming chat completion: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo streaming chat completion: ' + error.message,
      );
    }
  }

  /**
   * Xóa file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileId ID của file trong vector store
   * @returns Thông tin về việc xóa thành công
   * @throws AppException nếu có lỗi khi xóa file
   */
  async deleteVectorStoreFile(
    vectorStoreId: string,
    fileId: string,
  ): Promise<{ deleted: boolean; vectorStoreId: string; fileId: string }> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để xóa file khỏi vector store
      await this.openai.vectorStores.files.del(vectorStoreId, fileId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(
        `File deleted from vector store successfully: ${fileId} from ${vectorStoreId}`,
      );

      return {
        deleted: true,
        vectorStoreId,
        fileId,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error deleting file from vector store: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy vector store hoặc file
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy vector store (${vectorStoreId}) hoặc file (${fileId})`,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi xóa file khỏi vector store: ' + error.message,
      );
    }
  }

  /**
   * Xóa file trực tiếp từ OpenAI
   * @param fileId ID của file cần xóa
   * @returns Thông tin về việc xóa thành công
   * @throws AppException nếu có lỗi khi xóa file
   */
  async deleteOpenAIFile(
    fileId: string,
  ): Promise<{ deleted: boolean; fileId: string }> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để xóa file trực tiếp từ OpenAI
      await this.openai.files.del(fileId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`OpenAI file deleted successfully: ${fileId}`);

      return {
        deleted: true,
        fileId,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error deleting file: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy file
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy file với ID: ' + fileId,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi xóa file OpenAI: ' + error.message,
      );
    }
  }

  /**
   * Gán file đã tồn tại trên OpenAI vào vector store
   * @param vectorStoreId ID của vector store
   * @param fileId ID của file trên OpenAI
   * @returns Thông tin về file đã gán vào vector store
   * @throws AppException nếu có lỗi khi gán file
   */
  async attachFileToVectorStore(
    vectorStoreId: string,
    fileId: string,
  ): Promise<VectorStoreFile> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Gán file vào vector store
      const fileVector = await this.openai.vectorStores.files.create(
        vectorStoreId,
        {
          file_id: fileId,
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      this.logger.log(
        `File attached to vector store successfully: ${fileId} to ${vectorStoreId}`,
      );

      return fileVector;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error attaching file to vector store: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy vector store hoặc file
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy vector store (${vectorStoreId}) hoặc file (${fileId})`,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi gán file vào vector store: ' + error.message,
      );
    }
  }

  /**
   * Lấy danh sách model từ OpenAI
   * @param filter Tùy chọn lọc model (gpt, embedding, davinci, curie, babbage, ada)
   * @returns Danh sách model từ OpenAI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async listModels(filter?: {
    gpt?: boolean;
    embedding?: boolean;
    davinci?: boolean;
    curie?: boolean;
    babbage?: boolean;
    ada?: boolean;
  }): Promise<any[]> {
    try {
      // Thực hiện gọi API để lấy danh sách model
      const response = await this.openai.models.list();

      // Lọc theo filter nếu có
      let filteredModels = response.data;

      if (filter) {
        filteredModels = filteredModels.filter((model) => {
          const id = model.id.toLowerCase();
          return (
            (filter.gpt && id.startsWith('gpt')) ||
            (filter.embedding && id.startsWith('text-embedding')) ||
            (filter.davinci && id.includes('davinci')) ||
            (filter.curie && id.includes('curie')) ||
            (filter.babbage && id.includes('babbage')) ||
            (filter.ada && id.includes('ada'))
          );
        });
      }

      this.logger.log(`Retrieved ${filteredModels.length} models from OpenAI`);
      return filteredModels;
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from OpenAI: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối OpenAI API
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.name === 'NetworkError' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết về một model từ OpenAI
   * @param modelId ID của model cần lấy thông tin
   * @returns Thông tin chi tiết về model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string): Promise<Model> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await this.openai.models.retrieve(modelId, {
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      this.logger.log(`Retrieved model information for: ${modelId}`);
      return response;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error retrieving model: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy model
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy model với ID: ' + modelId,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy thông tin model: ' + error.message,
      );
    }
  }

  /**
   * Xóa vector store
   * @param vectorStoreId ID của vector store cần xóa
   * @returns Thông tin về việc xóa thành công
   * @throws AppException nếu có lỗi khi xóa vector store
   */
  async deleteVectorStore(
    vectorStoreId: string,
  ): Promise<{ deleted: boolean; id: string }> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để xóa vector store
      await this.openai.vectorStores.del(vectorStoreId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`Vector store deleted successfully: ${vectorStoreId}`);

      return {
        deleted: true,
        id: vectorStoreId,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error deleting vector store: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy vector store
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy vector store với ID: ' + vectorStoreId,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi xóa vector store: ' + error.message,
      );
    }
  }
}
