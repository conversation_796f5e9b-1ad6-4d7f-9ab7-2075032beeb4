import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { S3Service } from './s3.service';
import { OpenAiService } from './ai/openai.service';
import { AnthropicService } from './ai/anthropic.service';
import { GoogleAIService } from './ai/google_ai.service';
import { DeepSeekService } from './ai/deepseek.service';
import { MetaAIService } from './ai/metaai.service';
import { CdnService } from './cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { PdfService } from '@shared/services/pdf.service';
import { EncryptionService } from '@shared/services/encryption.service';
import { SmsModule } from './sms/sms.module';
import { TelegramModule } from './telegram/telegram.module';
import { ZaloModule } from './zalo/zalo.module';
import { GoogleApiModule } from './google/google-api.module';
import { AuthenticatorModule } from './authenticator/authenticator.module';
import { MarketingAiModule } from './marketing-ai/marketing-ai.module';
import { EmailModule } from './email/email.module';
import { SocketModule } from '@shared/socket/socket.module';

@Global()
@Module({
  imports: [
    HttpModule,
    SmsModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    EmailModule,
    SocketModule,
  ],
  providers: [
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    MetaAIService,
    CdnService,
    RedisService,
    RecaptchaService,
    PdfService,
    EncryptionService,
  ],
  exports: [
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    MetaAIService,
    CdnService,
    RedisService,
    RecaptchaService,
    PdfService,
    EncryptionService,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    EmailModule,
    SocketModule,
  ],
})
export class ServicesModule {}
