import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloOaService } from './zalo-oa.service';
import { ZaloMessage } from './zalo.interface';

@Injectable()
export class ZaloAgentService {
  private readonly logger = new Logger(ZaloAgentService.name);

  constructor(
    private readonly zaloOaService: ZaloOaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn từ agent đến <PERSON>
   * @param botToken Token của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Nội dung tin nhắn
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendAgentMessageToZalo(
    accessToken: string,
    userId: string,
    message: string,
  ): Promise<{ message_id: string }> {
    try {
      return await this.zaloOaService.sendTextMessage(
        accessToken,
        userId,
        message,
      );
    } catch (error) {
      this.logger.error(
        `Error sending agent message to Zalo: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn từ agent đến Zalo',
      );
    }
  }

  /**
   * Gửi tin nhắn phức tạp từ agent đến Zalo
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Tin nhắn cần gửi
   * @returns ID của tin nhắn
   */
  async sendAgentComplexMessageToZalo(
    accessToken: string,
    userId: string,
    message: ZaloMessage,
  ): Promise<{ message_id: string }> {
    try {
      return await this.zaloOaService.sendMessage(accessToken, userId, message);
    } catch (error) {
      this.logger.error(
        `Error sending complex message to Zalo: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn phức tạp từ agent đến Zalo',
      );
    }
  }

  /**
   * Xử lý tin nhắn từ Zalo và chuyển tiếp đến agent
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Nội dung tin nhắn
   * @param agentId ID của agent
   * @returns Kết quả xử lý
   */
  async processZaloMessageToAgent(
    accessToken: string,
    userId: string,
    message: string,
    agentId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(
        `Processing message from Zalo user ${userId} to agent ${agentId}`,
      );

      // TODO: Implement logic to forward message to agent
      // This would typically involve:
      // 1. Retrieving the agent from your database
      // 2. Calling your agent service to process the message
      // 3. Getting the response from the agent
      // 4. Sending the response back to the Zalo user

      // For now, we'll just log the message
      this.logger.log(
        `Message from Zalo user ${userId} to agent ${agentId}: ${message}`,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error processing Zalo message to agent: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xử lý tin nhắn từ Zalo đến agent',
      );
    }
  }
}
