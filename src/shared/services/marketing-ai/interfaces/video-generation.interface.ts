import {
  MarketingAiOptions,
  MarketingAiResponse,
  BaseMarketingAiService,
} from './base.interface';

/**
 * Video resolution options
 */
export enum VideoResolution {
  SD = 'sd',
  HD = 'hd',
  FULL_HD = 'full_hd',
  ULTRA_HD = '4k',
}

/**
 * Video format options
 */
export enum VideoFormat {
  MP4 = 'mp4',
  WEBM = 'webm',
  MOV = 'mov',
}

/**
 * Video style options
 */
export enum VideoStyle {
  REALISTIC = 'realistic',
  ANIMATED = 'animated',
  CINEMATIC = 'cinematic',
  DOCUMENTARY = 'documentary',
  COMMERCIAL = 'commercial',
  SOCIAL_MEDIA = 'social_media',
}

/**
 * Video generation options
 */
export interface VideoGenerationOptions extends MarketingAiOptions {
  /**
   * Resolution of the generated video
   * @default VideoResolution.HD
   */
  resolution?: VideoResolution;

  /**
   * Format of the generated video
   * @default VideoFormat.MP4
   */
  format?: VideoFormat;

  /**
   * Style of the generated video
   * @default VideoStyle.REALISTIC
   */
  style?: VideoStyle;

  /**
   * Duration of the generated video in seconds
   * @default 15
   */
  duration?: number;

  /**
   * Whether to include audio in the generated video
   * @default true
   */
  includeAudio?: boolean;

  /**
   * Reference image URL to guide the video generation
   */
  referenceImageUrl?: string;

  /**
   * Reference video URL to guide the video generation
   */
  referenceVideoUrl?: string;
}

/**
 * Video generation result
 */
export interface VideoGenerationResult {
  /**
   * URL of the generated video
   */
  videoUrl: string;

  /**
   * URL of the video thumbnail
   */
  thumbnailUrl?: string;

  /**
   * Duration of the generated video in seconds
   */
  duration: number;

  /**
   * Prompt used for the generation
   */
  prompt: string;

  /**
   * Status of the video generation
   */
  status: 'completed' | 'processing' | 'failed';

  /**
   * ID of the generation job (for checking status later)
   */
  jobId?: string;
}

/**
 * Interface for video generation services
 */
export interface VideoGenerationService extends BaseMarketingAiService {
  /**
   * Generate a video from a text prompt
   * @param prompt Text prompt to generate video from
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  generateVideo(
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>>;

  /**
   * Check the status of a video generation job
   * @param jobId ID of the video generation job
   * @returns A promise that resolves to a response containing the status of the job
   */
  checkVideoGenerationStatus(
    jobId: string,
  ): Promise<MarketingAiResponse<VideoGenerationResult>>;

  /**
   * Generate a video from an image and text prompt
   * @param imageUrl URL of the image to use as a reference
   * @param prompt Text prompt to guide the video generation
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  generateVideoFromImage?(
    imageUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>>;

  /**
   * Edit an existing video using a text prompt
   * @param videoUrl URL of the video to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for video editing
   * @returns A promise that resolves to a response containing the edited video
   */
  editVideo?(
    videoUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>>;
}
