import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  VideoGenerationOptions,
  VideoGenerationResult,
  VideoGenerationService,
  VideoResolution,
  VideoFormat,
  VideoStyle,
  MarketingAiResponse,
} from '../interfaces';

/**
 * Synthesia service for video generation
 */
@Injectable()
export class SynthesiaService
  extends BaseMarketingAiServiceImpl
  implements VideoGenerationService
{
  readonly serviceName = 'Synthesia';
  protected readonly baseUrl = 'https://api.synthesia.io/v2';
  protected readonly apiKey: string | undefined;

  constructor(private readonly configService: ConfigService) {
    super(SynthesiaService.name);
    this.apiKey = this.configService.get<string>('SYNTHESIA_API_KEY');

    if (!this.apiKey) {
      this.logger.warn(
        'SYNTHESIA_API_KEY is not defined in environment variables',
      );
    }
  }

  /**
   * Test the connection to the Synthesia API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/avatars`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);
      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('Synthesia API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };
  }

  /**
   * Generate a video from a text prompt using Synthesia
   * @param prompt Text prompt to generate video from
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideo(
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      // Synthesia requires a script and an avatar ID
      // For simplicity, we'll use a default avatar
      const defaultAvatarId = 'anna-costume1-EN-US';

      // Create a video
      const url = `${this.baseUrl}/videos`;

      const data = {
        test: true, // Set to false for production
        title: 'Generated Video',
        description: prompt,
        visibility: 'private',
        avatarId: defaultAvatarId,
        avatarSettings: {
          voice: 'en-US-JennyNeural',
          horizontalAlign: 'center',
          scale: 1,
          style: 'rectangular',
        },
        script: prompt,
        background: {
          id: 'off-white',
        },
        soundtrack: {
          id: 'urban',
        },
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 30000,
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const videoId = response.data.id;

      // Return the video ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration: 0,
        prompt,
        status: 'processing',
        jobId: videoId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Synthesia video generation',
      );
    }
  }

  /**
   * Check the status of a video generation job
   * @param jobId ID of the video generation job
   * @returns A promise that resolves to a response containing the status of the job
   */
  async checkVideoGenerationStatus(
    jobId: string,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      const url = `${this.baseUrl}/videos/${jobId}`;
      const config = this.createRequestConfig();

      const response = await this.axiosInstance.get(url, config);

      const status = response.data.status;
      let videoStatus: 'completed' | 'processing' | 'failed' = 'processing';

      if (status === 'complete') {
        videoStatus = 'completed';
      } else if (status === 'failed') {
        videoStatus = 'failed';
      }

      const result: VideoGenerationResult = {
        videoUrl: response.data.download || '',
        thumbnailUrl: response.data.thumbnailUrl || '',
        duration: response.data.duration || 0,
        prompt: response.data.description || '',
        status: videoStatus,
        jobId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Synthesia video status check',
      );
    }
  }

  /**
   * Generate a video from an image and text prompt
   * @param imageUrl URL of the image to use as a reference
   * @param prompt Text prompt to guide the video generation
   * @param options Options for video generation
   * @returns A promise that resolves to a response containing the generated video
   */
  async generateVideoFromImage(
    imageUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      // Synthesia doesn't support direct image-to-video generation
      // We can use the image as a background for the video

      // Create a video with a custom background
      const url = `${this.baseUrl}/videos`;

      const data = {
        test: true, // Set to false for production
        title: 'Generated Video with Image',
        description: prompt,
        visibility: 'private',
        avatarId: 'anna-costume1-EN-US',
        avatarSettings: {
          voice: 'en-US-JennyNeural',
          horizontalAlign: 'center',
          scale: 1,
          style: 'rectangular',
        },
        script: prompt,
        background: {
          url: imageUrl,
        },
        soundtrack: {
          id: 'urban',
        },
      };

      const config = this.createRequestConfig({
        timeout: options?.timeout || 30000,
      });

      // Start the generation
      const response = await this.axiosInstance.post(url, data, config);

      const videoId = response.data.id;

      // Return the video ID for status checking
      const result: VideoGenerationResult = {
        videoUrl: '',
        duration: 0,
        prompt,
        status: 'processing',
        jobId: videoId,
      };

      return this.createSuccessResponse(result, response.data);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Synthesia image-to-video generation',
      );
    }
  }

  /**
   * Edit an existing video using a text prompt
   * @param videoUrl URL of the video to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for video editing
   * @returns A promise that resolves to a response containing the edited video
   */
  async editVideo(
    videoUrl: string,
    prompt: string,
    options?: VideoGenerationOptions,
  ): Promise<MarketingAiResponse<VideoGenerationResult>> {
    try {
      // Synthesia doesn't support direct video editing
      this.logger.warn(
        'Synthesia API does not support video editing. Generating a new video based on the prompt.',
      );

      // Generate a new video with the prompt
      return this.generateVideo(prompt, options);
    } catch (error) {
      return this.handleApiError<VideoGenerationResult>(
        error,
        'Synthesia video editing',
      );
    }
  }
}
