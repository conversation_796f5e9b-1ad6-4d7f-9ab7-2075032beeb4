import { Injectable } from '@nestjs/common';
import { ConfigService, ConfigType, ServicesConfig } from '@config';
import { AxiosRequestConfig } from 'axios';
import { BaseMarketingAiServiceImpl } from '../base-marketing-ai.service';
import {
  ImageGenerationOptions,
  ImageGenerationResult,
  ImageGenerationService,
  ImageSize,
  // ImageFormat, // Unused import
  MarketingAiResponse,
} from '../interfaces';
import OpenAI from 'openai';

/**
 * OpenAI DALL-E service for image generation
 */
@Injectable()
export class OpenAiDalleService
  extends BaseMarketingAiServiceImpl
  implements ImageGenerationService
{
  readonly serviceName = 'OpenAI DALL-E';
  protected readonly baseUrl = 'https://api.openai.com/v1';
  protected readonly apiKey: string | undefined;
  private readonly openai: OpenAI;

  constructor(private readonly configService: ConfigService) {
    super(OpenAiDalleService.name);

    const servicesConfig = this.configService.getConfig<ServicesConfig>(
      ConfigType.Services,
    );
    this.apiKey = servicesConfig.openai.apiKey;

    this.openai = new OpenAI({
      apiKey: this.apiKey,
      organization: servicesConfig.openai.organizationId,
    });
  }

  /**
   * Test the connection to the OpenAI API
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  async testConnection(): Promise<boolean> {
    try {
      // Simple test to check if the API key is valid
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      await this.openai.models.list({
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return true;
    } catch (error) {
      this.logger.error(
        `Connection test failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected createRequestConfig(
    config?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    if (!this.apiKey) {
      throw new Error('OpenAI API key is not defined');
    }

    return {
      ...config,
      headers: {
        ...config?.headers,
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };
  }

  /**
   * Map size option to DALL-E size parameter
   * @param size Size option
   * @param width Custom width (if size is CUSTOM)
   * @param height Custom height (if size is CUSTOM)
   * @returns DALL-E size parameter
   */
  private mapSizeToOpenAiSize(
    size: ImageSize = ImageSize.MEDIUM,
    width?: number,
    height?: number,
  ): string {
    if (size === ImageSize.CUSTOM && width && height) {
      // DALL-E 3 supports custom sizes in specific increments
      // Round to nearest supported size
      const supportedSizes = [1024, 1152, 1280, 1536, 1792, 2048];
      const nearestWidth = supportedSizes.reduce((prev, curr) =>
        Math.abs(curr - width) < Math.abs(prev - width) ? curr : prev,
      );
      const nearestHeight = supportedSizes.reduce((prev, curr) =>
        Math.abs(curr - height) < Math.abs(prev - height) ? curr : prev,
      );
      return `${nearestWidth}x${nearestHeight}`;
    }

    switch (size) {
      case ImageSize.SMALL:
        return '1024x1024';
      case ImageSize.MEDIUM:
        return '1024x1024';
      case ImageSize.LARGE:
        return '1792x1024';
      default:
        return '1024x1024';
    }
  }

  /**
   * Generate images from a text prompt using DALL-E
   * @param prompt Text prompt to generate images from
   * @param options Options for image generation
   * @returns A promise that resolves to a response containing the generated images
   */
  async generateImage(
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      const size = this.mapSizeToOpenAiSize(
        options?.size,
        options?.width,
        options?.height,
      );

      const count = options?.count || 1;

      // Set up timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        options?.timeout || 60000,
      );

      // Call OpenAI API
      const response = await this.openai.images.generate(
        {
          model: 'dall-e-3',
          prompt,
          n: count,
          size: size as any,
          quality: 'standard',
          response_format: 'url',
          style: options?.style === 'artistic' ? 'vivid' : 'natural',
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      // Extract image URLs
      const imageUrls = (response?.data || [])
        .map((item) => item.url || '')
        .filter((url): url is string => url !== undefined && url !== '');

      // Create result
      const result: ImageGenerationResult = {
        imageUrls,
        prompt,
      };

      return this.createSuccessResponse(result, response);
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(
        error,
        'DALL-E image generation',
      );
    }
  }

  /**
   * Edit an existing image using a text prompt
   * @param imageUrl URL of the image to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for image editing
   * @returns A promise that resolves to a response containing the edited image
   */
  async editImage(
    imageUrl: string,
    prompt: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // DALL-E 3 doesn't support direct image editing via API
      // We would need to download the image, convert it, and use the edit endpoint
      // This is a simplified implementation

      this.logger.warn(
        'DALL-E 3 API does not directly support image editing. Generating a new image based on the prompt.',
      );

      // Generate a new image with the prompt
      return this.generateImage(`Edit this image: ${prompt}`, options);
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(
        error,
        'DALL-E image editing',
      );
    }
  }

  /**
   * Generate image variations from an existing image
   * @param imageUrl URL of the image to create variations from
   * @param options Options for image variation generation
   * @returns A promise that resolves to a response containing the image variations
   */
  async generateImageVariations(
    imageUrl: string,
    options?: ImageGenerationOptions,
  ): Promise<MarketingAiResponse<ImageGenerationResult>> {
    try {
      // DALL-E 3 doesn't support variations via API
      // This is a simplified implementation

      this.logger.warn(
        'DALL-E 3 API does not directly support image variations. Generating a new image based on the original.',
      );

      // Generate a new image with a generic prompt
      return this.generateImage(`Create a variation of this image`, options);
    } catch (error) {
      return this.handleApiError<ImageGenerationResult>(
        error,
        'DALL-E image variations',
      );
    }
  }
}
