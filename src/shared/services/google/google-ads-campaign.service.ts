import { Injectable, Logger } from '@nestjs/common';
import { GoogleAdsService } from './google-ads.service';
import {
  CampaignSearchResult,
  PerformanceReport,
} from './interfaces/google-ads.interface';

@Injectable()
export class GoogleAdsCampaignService {
  private readonly logger = new Logger(GoogleAdsCampaignService.name);

  constructor(private readonly googleAdsService: GoogleAdsService) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách chiến dịch
   * @param customerId ID của customer
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách chiến dịch
   */
  async listCampaigns(
    customerId: string,
    refreshToken?: string,
  ): Promise<CampaignSearchResult[]> {
    try {
      return await this.googleAdsService.listCampaigns(
        customerId,
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to list campaigns: ${error.message}`);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết của chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Thông tin chi tiết của chiến dịch
   */
  async getCampaign(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<CampaignSearchResult> {
    try {
      return await this.googleAdsService.getCampaign(
        customerId,
        campaignId,
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to get campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo chiến dịch tìm kiếm mới
   * @param customerId ID của customer
   * @param campaignData Dữ liệu chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của chiến dịch mới
   */
  async createSearchCampaign(
    customerId: string,
    campaignData: {
      name: string;
      budgetAmount: number; // Micro amount (1000000 = 1 USD)
      startDate?: string; // YYYYMMDD format
      endDate?: string; // YYYYMMDD format
    },
    refreshToken?: string,
  ): Promise<string> {
    try {
      return await this.googleAdsService.createCampaign(
        customerId,
        {
          ...campaignData,
          type: 'SEARCH',
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to create search campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo chiến dịch hiển thị mới
   * @param customerId ID của customer
   * @param campaignData Dữ liệu chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của chiến dịch mới
   */
  async createDisplayCampaign(
    customerId: string,
    campaignData: {
      name: string;
      budgetAmount: number; // Micro amount (1000000 = 1 USD)
      startDate?: string; // YYYYMMDD format
      endDate?: string; // YYYYMMDD format
    },
    refreshToken?: string,
  ): Promise<string> {
    try {
      return await this.googleAdsService.createCampaign(
        customerId,
        {
          ...campaignData,
          type: 'DISPLAY',
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to create display campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Kích hoạt chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns true nếu kích hoạt thành công
   */
  async enableCampaign(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<boolean> {
    try {
      return await this.googleAdsService.updateCampaign(
        customerId,
        campaignId,
        {
          status: 'ENABLED',
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to enable campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạm dừng chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns true nếu tạm dừng thành công
   */
  async pauseCampaign(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<boolean> {
    try {
      return await this.googleAdsService.updateCampaign(
        customerId,
        campaignId,
        {
          status: 'PAUSED',
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to pause campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param refreshToken Refresh token (tùy chọn)
   * @returns true nếu xóa thành công
   */
  async removeCampaign(
    customerId: string,
    campaignId: string,
    refreshToken?: string,
  ): Promise<boolean> {
    try {
      return await this.googleAdsService.updateCampaign(
        customerId,
        campaignId,
        {
          status: 'REMOVED',
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to remove campaign: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật ngân sách chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param budgetAmount Số tiền ngân sách mới (micro amount)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns true nếu cập nhật thành công
   */
  async updateCampaignBudget(
    customerId: string,
    campaignId: string,
    budgetAmount: number,
    refreshToken?: string,
  ): Promise<boolean> {
    try {
      return await this.googleAdsService.updateCampaign(
        customerId,
        campaignId,
        {
          budgetAmount,
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to update campaign budget: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy báo cáo hiệu suất của chiến dịch
   * @param customerId ID của customer
   * @param campaignId ID của chiến dịch
   * @param dateRange Khoảng thời gian
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Báo cáo hiệu suất
   */
  async getCampaignPerformance(
    customerId: string,
    campaignId: string,
    dateRange: { startDate: string; endDate: string },
    refreshToken?: string,
  ): Promise<PerformanceReport[]> {
    try {
      return await this.googleAdsService.getCampaignPerformance(
        customerId,
        campaignId,
        dateRange,
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to get campaign performance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy báo cáo hiệu suất của tất cả chiến dịch
   * @param customerId ID của customer
   * @param dateRange Khoảng thời gian
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Báo cáo hiệu suất theo chiến dịch
   */
  async getAllCampaignsPerformance(
    customerId: string,
    dateRange: { startDate: string; endDate: string },
    refreshToken?: string,
  ): Promise<
    {
      campaignId: string;
      campaignName: string;
      performance: PerformanceReport[];
    }[]
  > {
    try {
      // Lấy danh sách chiến dịch
      const campaigns = await this.listCampaigns(customerId, refreshToken);

      // Lấy báo cáo hiệu suất cho từng chiến dịch
      const results = await Promise.all(
        campaigns.map(async (campaign) => {
          const performance = await this.getCampaignPerformance(
            customerId,
            campaign.id,
            dateRange,
            refreshToken,
          );

          return {
            campaignId: campaign.id,
            campaignName: campaign.name,
            performance,
          };
        }),
      );

      return results;
    } catch (error) {
      this.logger.error(
        `Failed to get all campaigns performance: ${error.message}`,
      );
      throw error;
    }
  }
}
