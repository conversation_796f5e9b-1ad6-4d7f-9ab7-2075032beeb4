import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho hình ảnh
 */
export enum ImageTypeEnum {
  PNG = 'image/png',
  JPEG = 'image/jpeg',
  WEBP = 'image/webp',
  GIF = 'image/gif',
}

/**
 * Object tiện ích để làm việc với ImageTypeEnum
 */
export const ImageType = {
  /**
   * Lấy giá trị chuỗi của một loại hình ảnh
   * @param type Loại hình ảnh
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: ImageTypeEnum): string {
    return type;
  },

  /**
   * Lấy enum ImageTypeEnum từ tên loại hình ảnh hoặc giá trị MIME type
   * @param type Tên loại hình ảnh (key của enum) hoặc giá trị MIME type (ví dụ: 'image/png')
   * @returns Giá trị enum ImageTypeEnum tương ứng
   * @throws AppException nếu loại hình ảnh không tồn tại
   */
  getType(type: string): ImageTypeEnum {
    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'image/png')
    const entries = Object.entries(ImageTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return ImageTypeEnum[entry[0] as keyof typeof ImageTypeEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại hình ảnh '${type}' không được hỗ trợ`,
    );
  },
};
