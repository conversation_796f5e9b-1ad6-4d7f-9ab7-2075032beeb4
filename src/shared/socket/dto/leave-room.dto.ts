import { IsString, IsOptional, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc rời phòng
 */
export class LeaveRoomDto {
  @ApiProperty({ description: 'ID của phòng' })
  @IsString()
  roomId: string;

  @ApiProperty({ description: 'Lý do rời phòng', required: false })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * DTO cho việc rời nhiều phòng
 */
export class LeaveRoomsDto {
  @ApiProperty({ description: 'Danh sách phòng cần rời', type: [LeaveRoomDto] })
  @IsArray()
  rooms: LeaveRoomDto[];
}
