import { IsString, IsOptional, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc tham gia phòng
 */
export class JoinRoomDto {
  @ApiProperty({ description: 'ID của phòng' })
  @IsString()
  roomId: string;

  @ApiProperty({ description: 'Mật khẩu của phòng (nếu có)', required: false })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Metadata của phòng',
    required: false,
    type: Object,
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * DTO cho việc tham gia nhiều phòng
 */
export class JoinRoomsDto {
  @ApiProperty({
    description: 'Danh sách phòng cần tham gia',
    type: [JoinRoomDto],
  })
  @IsArray()
  rooms: JoinRoomDto[];
}
