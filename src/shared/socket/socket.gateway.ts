import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
  WsException,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Logger, UseGuards, Injectable } from '@nestjs/common';
import { SocketService } from './socket.service';
import { SocketClient } from './interfaces/socket-client.interface';
import { SocketEvents } from './events/socket-events.enum';
import { SocketAuthGuard } from './guards/socket-auth.guard';
import { SocketUser } from './decorators/socket-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { JoinRoomDto, LeaveRoomDto, SocketMessageDto } from './dto';

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: 'socket',
})
@Injectable()
export class SocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(SocketGateway.name);

  @WebSocketServer()
  server: Server;

  constructor(private readonly socketService: SocketService) {}

  /**
   * Khởi tạo gateway
   * @param server Server Socket.IO
   */
  afterInit(server: Server) {
    this.socketService.setServer(server);
    this.logger.log('Socket.IO gateway initialized');
  }

  /**
   * Xử lý kết nối mới
   * @param client Client Socket.IO
   */
  handleConnection(client: SocketClient) {
    try {
      // Thêm client vào danh sách kết nối
      this.socketService.addClient(client);
      this.logger.log(`Client connected: ${client.id}`);
    } catch (error) {
      this.logger.error(`Error handling connection: ${error.message}`);
    }
  }

  /**
   * Xử lý ngắt kết nối
   * @param client Client Socket.IO
   */
  handleDisconnect(client: SocketClient) {
    try {
      // Xóa client khỏi danh sách kết nối
      this.socketService.removeClient(client.id);
      this.logger.log(`Client disconnected: ${client.id}`);
    } catch (error) {
      this.logger.error(`Error handling disconnect: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện xác thực
   * @param client Client Socket.IO
   * @param payload Dữ liệu xác thực
   */
  @SubscribeMessage('authenticate')
  async handleAuthenticate(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() payload: { token: string },
  ) {
    try {
      // Xác thực sẽ được xử lý bởi SocketAuthGuard
      // Chỉ cần trả về thông báo thành công
      return {
        event: 'authenticated',
        data: {
          status: 'success',
          message: 'Authentication successful',
        },
      };
    } catch (error) {
      this.logger.error(`Authentication error: ${error.message}`);
      throw new WsException('Authentication failed');
    }
  }

  /**
   * Xử lý sự kiện tham gia phòng
   * @param client Client Socket.IO
   * @param data Dữ liệu tham gia phòng
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(SocketEvents.JOIN_ROOM)
  async handleJoinRoom(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: JoinRoomDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      // Tham gia phòng
      await client.join(data.roomId);

      // Lưu thông tin người dùng trong phòng
      this.socketService.addUserToRoom(data.roomId, user.id.toString());

      // Thông báo cho người dùng
      client.emit(SocketEvents.ROOM_JOINED, {
        roomId: data.roomId,
        message: `Joined room ${data.roomId}`,
      });

      // Thông báo cho các người dùng khác trong phòng
      client.to(data.roomId).emit(SocketEvents.USER_CONNECTED, {
        userId: user.id,
        roomId: data.roomId,
        timestamp: Date.now(),
      });

      this.logger.log(`User ${user.id} joined room ${data.roomId}`);

      return {
        event: SocketEvents.ROOM_JOINED,
        data: {
          roomId: data.roomId,
          usersCount: this.socketService.getUsersInRoom(data.roomId).length,
        },
      };
    } catch (error) {
      this.logger.error(`Error joining room: ${error.message}`);
      throw new WsException(`Failed to join room: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện rời phòng
   * @param client Client Socket.IO
   * @param data Dữ liệu rời phòng
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(SocketEvents.LEAVE_ROOM)
  async handleLeaveRoom(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: LeaveRoomDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      // Rời phòng
      await client.leave(data.roomId);

      // Xóa thông tin người dùng khỏi phòng
      this.socketService.removeUserFromRoom(data.roomId, user.id.toString());

      // Thông báo cho người dùng
      client.emit(SocketEvents.ROOM_LEFT, {
        roomId: data.roomId,
        message: `Left room ${data.roomId}`,
      });

      // Thông báo cho các người dùng khác trong phòng
      client.to(data.roomId).emit(SocketEvents.USER_DISCONNECTED, {
        userId: user.id,
        roomId: data.roomId,
        reason: data.reason,
        timestamp: Date.now(),
      });

      this.logger.log(`User ${user.id} left room ${data.roomId}`);

      return {
        event: SocketEvents.ROOM_LEFT,
        data: {
          roomId: data.roomId,
        },
      };
    } catch (error) {
      this.logger.error(`Error leaving room: ${error.message}`);
      throw new WsException(`Failed to leave room: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện gửi tin nhắn
   * @param client Client Socket.IO
   * @param data Dữ liệu tin nhắn
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(SocketEvents.MESSAGE)
  async handleMessage(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: SocketMessageDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const timestamp = Date.now();

      // Chuẩn bị dữ liệu tin nhắn
      const messageData = {
        id: messageId,
        content: data.content,
        type: data.type,
        metadata: data.metadata,
        sender: {
          id: user.id,
          username: user.username,
        },
        timestamp,
      };

      // Gửi tin nhắn đến phòng hoặc người dùng cụ thể
      if (data.roomId) {
        // Kiểm tra xem người dùng có trong phòng không
        const usersInRoom = this.socketService.getUsersInRoom(data.roomId);
        if (!usersInRoom.includes(user.id.toString())) {
          throw new WsException('You are not a member of this room');
        }

        // Gửi tin nhắn đến tất cả người dùng trong phòng
        this.socketService.sendToRoom(
          data.roomId,
          SocketEvents.MESSAGE_RECEIVED,
          {
            ...messageData,
            roomId: data.roomId,
          },
          user.id.toString(), // Loại trừ người gửi
        );

        this.logger.log(
          `Message sent to room ${data.roomId} by user ${user.id}`,
        );
      } else if (data.recipientId) {
        // Gửi tin nhắn đến người dùng cụ thể
        this.socketService.sendToUser(
          data.recipientId,
          SocketEvents.MESSAGE_RECEIVED,
          {
            ...messageData,
            recipientId: data.recipientId,
          },
        );

        this.logger.log(
          `Message sent to user ${data.recipientId} by user ${user.id}`,
        );
      } else {
        throw new WsException('Either roomId or recipientId must be provided');
      }

      // Trả về xác nhận cho người gửi
      return {
        event: SocketEvents.MESSAGE_RECEIVED,
        data: {
          id: messageId,
          status: 'sent',
          timestamp,
        },
      };
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`);
      throw new WsException(`Failed to send message: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện đang gõ
   * @param client Client Socket.IO
   * @param data Dữ liệu đang gõ
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(SocketEvents.TYPING)
  handleTyping(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: { roomId?: string; recipientId?: string },
    @SocketUser() user: JwtPayload,
  ) {
    try {
      const typingData = {
        userId: user.id,
        username: user.username,
        timestamp: Date.now(),
      };

      if (data.roomId) {
        // Gửi thông báo đang gõ đến phòng
        client.to(data.roomId).emit(SocketEvents.TYPING, {
          ...typingData,
          roomId: data.roomId,
        });
      } else if (data.recipientId) {
        // Gửi thông báo đang gõ đến người dùng cụ thể
        this.socketService.sendToUser(data.recipientId, SocketEvents.TYPING, {
          ...typingData,
          recipientId: data.recipientId,
        });
      }

      return { status: 'success' };
    } catch (error) {
      this.logger.error(`Error handling typing event: ${error.message}`);
      throw new WsException(`Failed to handle typing event: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện dừng gõ
   * @param client Client Socket.IO
   * @param data Dữ liệu dừng gõ
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(SocketEvents.STOP_TYPING)
  handleStopTyping(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: { roomId?: string; recipientId?: string },
    @SocketUser() user: JwtPayload,
  ) {
    try {
      const typingData = {
        userId: user.id,
        username: user.username,
        timestamp: Date.now(),
      };

      if (data.roomId) {
        // Gửi thông báo dừng gõ đến phòng
        client.to(data.roomId).emit(SocketEvents.STOP_TYPING, {
          ...typingData,
          roomId: data.roomId,
        });
      } else if (data.recipientId) {
        // Gửi thông báo dừng gõ đến người dùng cụ thể
        this.socketService.sendToUser(
          data.recipientId,
          SocketEvents.STOP_TYPING,
          {
            ...typingData,
            recipientId: data.recipientId,
          },
        );
      }

      return { status: 'success' };
    } catch (error) {
      this.logger.error(`Error handling stop typing event: ${error.message}`);
      throw new WsException(
        `Failed to handle stop typing event: ${error.message}`,
      );
    }
  }
}
