import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { SocketClient } from '../interfaces/socket-client.interface';
import { WsException } from '@nestjs/websockets';
import { JwtUtilService, TokenType } from '@/modules/auth/guards/jwt.util';

@Injectable()
export class SocketAuthGuard implements CanActivate {
  private readonly logger = new Logger(SocketAuthGuard.name);

  constructor(
    private readonly jwtUtilService: JwtUtilService,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const client = context.switchToWs().getClient<SocketClient>();

    // Nếu đã xác thực trước đó
    if (client.user) {
      return true;
    }

    // Lấy token từ handshake
    const token = this.extractToken(client);
    if (!token) {
      this.logger.warn('Missing authentication token');
      throw new WsException('Unauthorized');
    }

    try {
      // Xác thực token sử dụng JwtUtilService
      const payload = this.jwtUtilService.verifyTokenWithType(
        token,
        TokenType.ACCESS,
      );

      // Lưu thông tin người dùng vào client
      client.user = payload;

      return true;
    } catch (error) {
      this.logger.error(`Authentication error: ${error.message}`);
      throw new WsException('Unauthorized');
    }
  }

  private extractToken(client: SocketClient): string | null {
    // Debug logging để kiểm tra dữ liệu từ client
    this.logger.log('=== WebSocket Handshake Debug ===');
    this.logger.log('Auth:', JSON.stringify(client.handshake?.auth));
    this.logger.log('Headers:', JSON.stringify(client.handshake?.headers));
    this.logger.log('Query:', JSON.stringify(client.handshake?.query));

    // Thử lấy token từ handshake auth
    const auth = client.handshake?.auth?.token;
    if (auth) {
      this.logger.log('✅ Token found in auth:', auth.substring(0, 20) + '...');
      return auth;
    }

    // Thử lấy token từ handshake headers
    const headers = client.handshake?.headers?.authorization;
    if (headers && headers.startsWith('Bearer ')) {
      const token = headers.substring(7);
      this.logger.log('✅ Token found in headers:', token.substring(0, 20) + '...');
      return token;
    }

    // Thử lấy token từ handshake query
    const query = client.handshake?.query?.token;
    if (query && typeof query === 'string') {
      this.logger.log('✅ Token found in query:', query.substring(0, 20) + '...');
      return query;
    }

    this.logger.warn('❌ No token found in any location');
    return null;
  }
}
