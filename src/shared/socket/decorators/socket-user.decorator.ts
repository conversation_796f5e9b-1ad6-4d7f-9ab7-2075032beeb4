import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SocketClient } from '../interfaces/socket-client.interface';

/**
 * Decorator để lấy thông tin người dùng từ socket
 *
 * @example
 * ```typescript
 * @SubscribeMessage('message')
 * handleMessage(@MessageBody() data: any, @SocketUser() user: JwtPayload) {
 *   console.log(`Message from user ${user.id}: ${data.content}`);
 * }
 * ```
 */
export const SocketUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const client = ctx.switchToWs().getClient<SocketClient>();
    return client.user;
  },
);
