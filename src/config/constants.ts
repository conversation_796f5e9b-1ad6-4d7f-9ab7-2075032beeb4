/**
 * Enum định nghĩa các môi trường ứng dụng
 */
export enum Environment {
  Development = 'development',
  Production = 'production',
  Test = 'test',
  Staging = 'staging',
}

/**
 * Enum định nghĩa các loại cấu hình
 */
export enum ConfigType {
  App = 'app',
  Database = 'database',
  Storage = 'storage',
  Auth = 'auth',
  Services = 'services',
  S3 = 's3',
}

/**
 * Các hằng số liên quan đến cấu hình
 */
export const CONFIG_CONSTANTS = {
  // Tên file cấu hình cho các môi trường
  ENV_FILES: {
    [Environment.Development]: '.env.development',
    [Environment.Production]: '.env.production',
    [Environment.Test]: '.env',
    [Environment.Staging]: '.env.staging',
    default: '.env',
  },

  // Các khóa cấu hình
  KEYS: {
    NODE_ENV: 'NODE_ENV',
    PORT: 'PORT',
    API_PREFIX: 'API_PREFIX',
  },

  // Gi<PERSON> trị mặc định
  DEFAULTS: {
    NODE_ENV: Environment.Development,
    PORT: 3000,
    API_PREFIX: 'api/v1',
  },
};
