import { Entity, PrimaryGeneratedColumn, Column, Check } from 'typeorm';
import { ObjectiveType } from '../enum/objective-type.enum';

/**
 * Entity representing objectives in the OKR system
 */
@Entity('objectives')
@Check(`"start_date" <= "end_date"`)
export class Objective {
  /**
   * Unique identifier for the objective
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Objective title
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Detailed description of the objective
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * ID of the user responsible for the objective
   */
  @Column({ name: 'owner_id', type: 'integer', nullable: false })
  ownerId: number;

  /**
   * ID of the department related to the objective (if any)
   */
  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  /**
   * ID of the parent objective (if this is a child objective)
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * ID of the OKR cycle this objective belongs to
   */
  @Column({ name: 'cycle_id', type: 'integer', nullable: true })
  cycleId: number | null;

  /**
   * Loại mục tiêu: Công ty, Phòng ban, Cá nhân
   */
  @Column({ name: 'type', type: 'enum', enum: ObjectiveType, nullable: false })
  type: ObjectiveType;

  /**
   * Objective completion progress (percentage)
   */
  @Column({ type: 'integer', default: 0, nullable: true })
  progress: number | null;

  /**
   * Objective status (active, completed, at-risk, behind)
   */
  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string | null;

  /**
   * Objective start date
   */
  @Column({ name: 'start_date', type: 'date', nullable: false })
  startDate: Date;

  /**
   * Objective end date
   */
  @Column({ name: 'end_date', type: 'date', nullable: false })
  endDate: Date;

  /**
   * ID of the user who created the objective
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
