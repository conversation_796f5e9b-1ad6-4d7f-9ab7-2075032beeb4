import { Injectable, Logger } from '@nestjs/common';
import { KeyResultRepository } from '../repositories/key-result.repository';
import { ObjectiveRepository } from '../repositories/objective.repository';
import { KeyResultSupportRepository } from '../repositories/key-result-support.repository';
import {
  CreateKeyResultDto,
  UpdateKeyResultDto,
  KeyResultQueryDto,
  KeyResultResponseDto,
} from '../dto/key-result';
import { KeyResult } from '../entities/key-result.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common';
import { OKRS_ERROR_CODES } from '../errors/okrs-error.code';

/**
 * Service for key results
 */
@Injectable()
export class KeyResultService {
  private readonly logger = new Logger(KeyResultService.name);

  constructor(
    private readonly keyResultRepository: KeyResultRepository,
    private readonly objectiveRepository: ObjectiveRepository,
    private readonly keyResultSupportRepository: KeyResultSupportRepository,
  ) {}

  /**
   * Create a new key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto Create key result DTO
   * @returns Created key result response
   */
  async create(
    tenantId: number,
    dto: CreateKeyResultDto,
  ): Promise<KeyResultResponseDto> {
    // Validate objective exists
    const objective = await this.objectiveRepository.findById(
      tenantId,
      dto.objectiveId,
    );
    if (!objective) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_INVALID_OBJECTIVE,
        `Không tìm thấy mục tiêu với ID ${dto.objectiveId}`,
      );
    }

    // Validate target value is different from start value
    if (dto.targetValue === dto.startValue) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_INVALID_VALUES,
        'Giá trị mục tiêu phải khác giá trị ban đầu',
      );
    }

    // Validate supportingKeyResultIds nếu có
    if (dto.supportingKeyResultIds && dto.supportingKeyResultIds.length > 0) {
      // Kiểm tra từng key result một
      for (const keyResultId of dto.supportingKeyResultIds) {
        const keyResult = await this.keyResultRepository.findById(
          tenantId,
          keyResultId,
        );
        if (!keyResult) {
          throw new AppException(
            OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
            `Không tìm thấy kết quả chính với ID: ${keyResultId}`,
          );
        }
      }
    }

    // Calculate initial progress
    let progress = 0;
    if (dto.startValue !== undefined && dto.targetValue !== undefined) {
      const startValue = dto.startValue || 0;
      const currentValue = dto.startValue || 0;

      // Calculate progress based on the direction (increasing or decreasing)
      if (dto.targetValue > startValue) {
        // Increasing direction
        progress = Math.round(
          ((currentValue - startValue) / (dto.targetValue - startValue)) * 100,
        );
      } else {
        // Decreasing direction
        progress = Math.round(
          ((startValue - currentValue) / (startValue - dto.targetValue)) * 100,
        );
      }

      // Ensure progress is between 0 and 100
      progress = Math.max(0, Math.min(100, progress));
    }

    // Create key result
    const keyResult = await this.keyResultRepository.create(tenantId, {
      ...dto,
      progress,
      currentValue: dto.startValue || 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Tạo các liên kết hỗ trợ nếu có
    if (dto.supportingKeyResultIds && dto.supportingKeyResultIds.length > 0) {
      try {
        // Thêm liên kết KeyResultSupport
        await this.keyResultSupportRepository.createMany(
          keyResult.id,
          dto.supportingKeyResultIds,
        );
        this.logger.log(
          `Đã tạo liên kết hỗ trợ cho key result ${keyResult.id} với các key result: ${dto.supportingKeyResultIds.join(', ')}`,
        );
      } catch (error) {
        this.logger.error(
          `Lỗi khi tạo liên kết hỗ trợ: ${error.message}`,
          error.stack,
        );
        throw new AppException(
          OKRS_ERROR_CODES.KEY_RESULT_SUPPORT_INVALID,
          'Lỗi khi tạo liên kết hỗ trợ cho kết quả chính',
        );
      }
    }

    return await this.mapToResponseDto(tenantId, keyResult);
  }

  /**
   * Get all key results with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of key result responses
   */
  async findAll(
    tenantId: number,
    query: KeyResultQueryDto,
  ): Promise<PaginatedResult<KeyResultResponseDto>> {
    const result = await this.keyResultRepository.findAll(tenantId, query);

    const items: KeyResultResponseDto[] = [];
    for (const keyResult of result.items) {
      items.push(await this.mapToResponseDto(tenantId, keyResult));
    }

    return {
      items,
      meta: result.meta,
    };
  }

  /**
   * Get a key result by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Key result ID
   * @returns Key result response
   */
  async findById(tenantId: number, id: number): Promise<KeyResultResponseDto> {
    const keyResult = await this.keyResultRepository.findById(tenantId, id);

    if (!keyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${id}`,
      );
    }

    return await this.mapToResponseDto(tenantId, keyResult);
  }

  /**
   * Update a key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Key result ID
   * @param dto Update key result DTO
   * @returns Updated key result response
   */
  async update(
    tenantId: number,
    id: number,
    dto: UpdateKeyResultDto,
  ): Promise<KeyResultResponseDto> {
    // Check if key result exists
    const existingKeyResult = await this.keyResultRepository.findById(
      tenantId,
      id,
    );

    if (!existingKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${id}`,
      );
    }

    // Validate target value is different from start value if both are provided
    if (
      dto.targetValue !== undefined &&
      dto.startValue !== undefined &&
      dto.targetValue === dto.startValue
    ) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_INVALID_VALUES,
        'Giá trị mục tiêu phải khác giá trị ban đầu',
      );
    } else if (
      dto.targetValue !== undefined &&
      dto.startValue === undefined &&
      dto.targetValue === existingKeyResult.startValue
    ) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_INVALID_VALUES,
        'Giá trị mục tiêu phải khác giá trị ban đầu',
      );
    } else if (
      dto.targetValue === undefined &&
      dto.startValue !== undefined &&
      existingKeyResult.targetValue === dto.startValue
    ) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_INVALID_VALUES,
        'Giá trị mục tiêu phải khác giá trị ban đầu',
      );
    }

    // Calculate progress if current value, target value, or start value is updated
    let progress = existingKeyResult.progress;
    if (
      dto.currentValue !== undefined ||
      dto.targetValue !== undefined ||
      dto.startValue !== undefined
    ) {
      const startValue =
        dto.startValue !== undefined
          ? dto.startValue
          : existingKeyResult.startValue || 0;
      const targetValue =
        dto.targetValue !== undefined
          ? dto.targetValue
          : existingKeyResult.targetValue;
      const currentValue =
        dto.currentValue !== undefined
          ? dto.currentValue
          : existingKeyResult.currentValue || 0;

      // Calculate progress based on the direction (increasing or decreasing)
      if (targetValue > startValue) {
        // Increasing direction
        progress = Math.round(
          ((currentValue - startValue) / (targetValue - startValue)) * 100,
        );
      } else {
        // Decreasing direction
        progress = Math.round(
          ((startValue - currentValue) / (startValue - targetValue)) * 100,
        );
      }

      // Ensure progress is between 0 and 100
      progress = Math.max(0, Math.min(100, progress));
    }

    // Update key result
    const updatedKeyResult = await this.keyResultRepository.update(
      tenantId,
      id,
      {
        ...dto,
        progress,
        updatedAt: Date.now(),
      },
    );

    if (!updatedKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${id}`,
      );
    }

    // Update objective progress
    if (updatedKeyResult.objectiveId) {
      await this.updateObjectiveProgress(
        tenantId,
        updatedKeyResult.objectiveId,
      );
    }

    return await this.mapToResponseDto(tenantId, updatedKeyResult);
  }

  /**
   * Delete a key result
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Key result ID
   * @returns True if deleted
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    // Check if key result exists
    const existingKeyResult = await this.keyResultRepository.findById(
      tenantId,
      id,
    );

    if (!existingKeyResult) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${id}`,
      );
    }

    // Delete key result
    const deleted = await this.keyResultRepository.delete(tenantId, id);

    if (!deleted) {
      throw new AppException(
        OKRS_ERROR_CODES.KEY_RESULT_NOT_FOUND,
        `Không tìm thấy kết quả chính với ID ${id}`,
      );
    }

    // Update objective progress
    if (existingKeyResult.objectiveId) {
      await this.updateObjectiveProgress(
        tenantId,
        existingKeyResult.objectiveId,
      );
    }

    return true;
  }

  /**
   * Get key results by objective ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param objectiveId Objective ID
   * @returns List of key result responses
   */
  async findByObjectiveId(
    tenantId: number,
    objectiveId: number,
  ): Promise<KeyResultResponseDto[]> {
    // Check if objective exists
    const objective = await this.objectiveRepository.findById(
      tenantId,
      objectiveId,
    );

    if (!objective) {
      throw new AppException(
        OKRS_ERROR_CODES.OBJECTIVE_NOT_FOUND,
        `Không tìm thấy mục tiêu với ID ${objectiveId}`,
      );
    }

    // Get key results
    const keyResults = await this.keyResultRepository.findByObjectiveId(
      tenantId,
      objectiveId,
    );

    const result: KeyResultResponseDto[] = [];
    for (const keyResult of keyResults) {
      result.push(await this.mapToResponseDto(tenantId, keyResult));
    }

    return result;
  }

  /**
   * Update objective progress based on key results
   * @param tenantId ID tenant (required for tenant isolation)
   * @param objectiveId Objective ID
   */
  private async updateObjectiveProgress(
    tenantId: number,
    objectiveId: number,
  ): Promise<void> {
    // Get all key results for the objective
    const keyResults = await this.keyResultRepository.findByObjectiveId(
      tenantId,
      objectiveId,
    );

    if (keyResults.length === 0) {
      return;
    }

    // Calculate weighted average progress
    let totalWeight = 0;
    let weightedProgress = 0;

    keyResults.forEach((kr) => {
      const weight = kr.weight || 100;
      totalWeight += weight;
      weightedProgress += (kr.progress || 0) * weight;
    });

    const averageProgress = Math.round(weightedProgress / totalWeight);

    // Update objective progress
    await this.objectiveRepository.updateProgress(
      tenantId,
      objectiveId,
      averageProgress,
    );
  }

  /**
   * Map key result entity to response DTO
   * @param tenantId ID tenant (required for tenant isolation)
   * @param keyResult Key result entity
   * @returns Key result response DTO
   */
  private async mapToResponseDto(
    tenantId: number,
    keyResult: KeyResult,
  ): Promise<KeyResultResponseDto> {
    const response = new KeyResultResponseDto();

    response.id = keyResult.id;
    response.objectiveId = keyResult.objectiveId;
    response.title = keyResult.title;
    response.description = keyResult.description;
    response.targetValue = keyResult.targetValue;
    response.currentValue = keyResult.currentValue;
    response.startValue = keyResult.startValue;
    response.unit = keyResult.unit;
    response.format = keyResult.format;
    response.progress = keyResult.progress;
    response.status = keyResult.status;
    response.measurementMethod = keyResult.measurementMethod;
    response.weight = keyResult.weight;
    response.checkInFrequency = keyResult.checkInFrequency;
    response.createdAt = keyResult.createdAt;
    response.updatedAt = keyResult.updatedAt;

    // Lấy danh sách các key result được hỗ trợ bởi key result này
    try {
      const keyResultSupports =
        await this.keyResultSupportRepository.findByChildId(keyResult.id);
      if (keyResultSupports && keyResultSupports.length > 0) {
        const supportingKeyResults: Array<{
          id: number;
          title: string;
          objectiveId: number | null;
        }> = [];
        for (const support of keyResultSupports) {
          const parentKeyResult = await this.keyResultRepository.findById(
            tenantId,
            support.parentId,
          );
          if (parentKeyResult) {
            supportingKeyResults.push({
              id: parentKeyResult.id,
              title: parentKeyResult.title,
              objectiveId: parentKeyResult.objectiveId,
            });
          }
        }
        if (supportingKeyResults.length > 0) {
          response.supportingKeyResults = supportingKeyResults;
        }
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách key result được hỗ trợ: ${error.message}`,
        error.stack,
      );
    }

    return response;
  }
}
