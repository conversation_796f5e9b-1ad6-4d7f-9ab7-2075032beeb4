module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../..',
  testRegex: 'src/modules/okrs/tests/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/okrs/**/*.(t|j)s'],
  coverageDirectory: './coverage/okrs',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
  },
  setupFilesAfterEnv: ['<rootDir>/src/jest-setup.ts'],
}
