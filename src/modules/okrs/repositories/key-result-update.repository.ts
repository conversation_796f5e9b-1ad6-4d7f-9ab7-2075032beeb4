import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { KeyResultUpdate } from '../entities/key-result-update.entity';
import { KeyResultUpdateQueryDto } from '../dto/key-result-update/key-result-update-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for key result updates
 */
@Injectable()
export class KeyResultUpdateRepository {
  private readonly logger = new Logger(KeyResultUpdateRepository.name);

  constructor(
    @InjectRepository(KeyResultUpdate)
    private readonly repository: Repository<KeyResultUpdate>,
  ) {}

  /**
   * Find all key result updates with pagination and filtering
   * @param tenantId Tenant ID
   * @param query Query parameters
   * @returns Paginated list of key result updates
   */
  async findAll(
    tenantId: number,
    query: KeyResultUpdateQueryDto,
  ): Promise<PaginatedResult<KeyResultUpdate>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'updatedDate',
      sortDirection = 'DESC',
      keyResultId,
      updateBy,
    } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('update')
      .where('update.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (keyResultId) {
      queryBuilder.andWhere('update.keyResultId = :keyResultId', {
        keyResultId,
      });
    }

    if (updateBy) {
      queryBuilder.andWhere('update.updateBy = :updateBy', { updateBy });
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('update.notes ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`update.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find a key result update by ID
   * @param id Key result update ID
   * @param tenantId Tenant ID
   * @returns Key result update or null if not found
   */
  async findById(
    id: number,
    tenantId: number,
  ): Promise<KeyResultUpdate | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Create a new key result update
   * @param data Key result update data
   * @returns Created key result update
   */
  async create(data: Partial<KeyResultUpdate>): Promise<KeyResultUpdate> {
    const update = this.repository.create(data);
    return this.repository.save(update);
  }

  /**
   * Find key result updates by key result ID
   * @param keyResultId Key result ID
   * @param tenantId Tenant ID
   * @returns List of key result updates
   */
  async findByKeyResultId(
    keyResultId: number,
    tenantId: number,
  ): Promise<KeyResultUpdate[]> {
    return this.repository.find({
      where: { keyResultId, tenantId },
      order: { updatedDate: 'DESC' },
    });
  }

  /**
   * Find the latest key result update
   * @param keyResultId Key result ID
   * @param tenantId Tenant ID
   * @returns Latest key result update or null if not found
   */
  async findLatestByKeyResultId(
    keyResultId: number,
    tenantId: number,
  ): Promise<KeyResultUpdate | null> {
    return this.repository.findOne({
      where: { keyResultId, tenantId },
      order: { updatedDate: 'DESC' },
    });
  }
}
