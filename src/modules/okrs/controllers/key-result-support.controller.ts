import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { KeyResultSupportService } from '../services/key-result-support.service';
import {
  CreateKeyResultSupportDto,
  KeyResultSupportResponseDto,
} from '../dto/key-result-support';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards';

/**
 * Controller cho mối quan hệ hỗ trợ kết quả chính
 */
@ApiTags(SWAGGER_API_TAG.OKRS)
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@Controller('api/okrs/key-results/:keyResultId/supports')
export class KeyResultSupportController {
  constructor(
    private readonly keyResultSupportService: KeyResultSupportService,
  ) {}

  /**
   * Thêm kết quả chính hỗ trợ cho kết quả chính cha
   * @param keyResultId ID kết quả chính cha
   * @param dto DTO tạo hỗ trợ kết quả chính
   * @returns Danh sách phản hồi hỗ trợ kết quả chính
   */
  @Post()
  @ApiOperation({ summary: 'Thêm kết quả chính hỗ trợ cho kết quả chính cha' })
  @ApiParam({ name: 'keyResultId', description: 'ID kết quả chính cha' })
  @ApiResponse({
    status: 201,
    description: 'Kết quả chính hỗ trợ đã được thêm thành công.',
    type: ApiResponseDto,
  })
  async addSupports(
    @CurrentUser() user: JwtPayload,
    @Param('keyResultId', ParseIntPipe) keyResultId: number,
    @Body() dto: CreateKeyResultSupportDto,
  ): Promise<ApiResponseDto<KeyResultSupportResponseDto[]>> {
    const result = await this.keyResultSupportService.addSupports(
      Number(user.tenantId),
      keyResultId,
      dto,
    );
    return ApiResponseDto.created(result);
  }

  /**
   * Lấy kết quả chính hỗ trợ cho kết quả chính cha
   * @param keyResultId ID kết quả chính cha
   * @returns Danh sách phản hồi hỗ trợ kết quả chính
   */
  @Get()
  @ApiOperation({ summary: 'Lấy kết quả chính hỗ trợ cho kết quả chính cha' })
  @ApiParam({ name: 'keyResultId', description: 'ID kết quả chính cha' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kết quả chính hỗ trợ.',
    type: ApiResponseDto,
  })
  async getSupports(
    @CurrentUser() user: JwtPayload,
    @Param('keyResultId', ParseIntPipe) keyResultId: number,
  ): Promise<ApiResponseDto<KeyResultSupportResponseDto[]>> {
    const result = await this.keyResultSupportService.getSupports(
      Number(user.tenantId),
      keyResultId,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa kết quả chính hỗ trợ khỏi kết quả chính cha
   * @param keyResultId ID kết quả chính cha
   * @param childId ID kết quả chính con
   * @returns Thông báo thành công
   */
  @Delete(':childId')
  @ApiOperation({ summary: 'Xóa kết quả chính hỗ trợ khỏi kết quả chính cha' })
  @ApiParam({ name: 'keyResultId', description: 'ID kết quả chính cha' })
  @ApiParam({ name: 'childId', description: 'ID kết quả chính con' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả chính hỗ trợ đã được xóa thành công.',
    type: ApiResponseDto,
  })
  async removeSupport(
    @CurrentUser() user: JwtPayload,
    @Param('keyResultId', ParseIntPipe) keyResultId: number,
    @Param('childId', ParseIntPipe) childId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.keyResultSupportService.removeSupport(
      Number(user.tenantId),
      keyResultId,
      childId,
    );
    return ApiResponseDto.deleted(result);
  }
}
