import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

/**
 * DTO for creating a new key result update
 */
export class CreateKeyResultUpdateDto {
  /**
   * New value of the key result
   * @example 800
   */
  @ApiProperty({
    description: 'Giá trị mới',
    example: 800,
  })
  @IsNumber()
  newValue: number;

  /**
   * Confidence level for completing the key result (1-5)
   * @example 4
   */
  @ApiProperty({
    description: 'Mức độ tự tin (1-5)',
    example: 4,
    required: false,
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  confidenceLevel?: number;

  /**
   * Notes for the update
   * @example "Đã hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> chú cập nhật',
    example:
      'Đ<PERSON> hoàn thành chiến dịch marketing trên Facebook, tăng thêm 300 khách hàng mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  /**
   * Type of check-in
   * @example "manual"
   */
  @ApiProperty({
    description: 'Loại check-in',
    example: 'manual',
    required: false,
  })
  @IsString()
  @IsOptional()
  checkInType?: string;
}
