import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  IsArray,
} from 'class-validator';
import { CheckInFrequency } from '../../enum/check-in-frequency.enum';

/**
 * DTO for creating a new key result
 */
export class CreateKeyResultDto {
  /**
   * ID of the objective this key result belongs to
   * @example 1
   */
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  @IsNumber()
  objectiveId: number;

  /**
   * Title of the key result
   * @example "Tăng số lượng khách hàng mới lên 1000"
   */
  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1000',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  /**
   * Detailed description of the key result
   * @example "Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết kết quả chính',
    example:
      'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Target value of the key result
   * @example 1000
   */
  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1000,
  })
  @IsNumber()
  targetValue: number;

  /**
   * Initial value of the key result
   * @example 500
   */
  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  startValue?: number;

  /**
   * Unit of measurement for the key result
   * @example "khách hàng"
   */
  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    required: false,
  })
  @IsString()
  @IsOptional()
  unit?: string;

  /**
   * Display format for the key result value
   * @example "number"
   */
  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    required: false,
  })
  @IsString()
  @IsOptional()
  format?: string;

  /**
   * Method used to measure the key result
   * @example "Số lượng đăng ký mới"
   */
  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  measurementMethod?: string;

  /**
   * Weight of the key result within its objective (0-100)
   * @example 30
   */
  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  weight?: number;

  /**
   * Frequency of key result updates
   * @example "WEEKLY"
   */
  @ApiProperty({
    description: 'Tần suất cập nhật',
    enum: CheckInFrequency,
    example: CheckInFrequency.WEEKLY,
    required: false,
  })
  @IsEnum(CheckInFrequency)
  @IsOptional()
  checkInFrequency?: CheckInFrequency;

  /**
   * IDs của các kết quả chính mà mục tiêu then chốt này phụ trợ
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description:
      'Danh sách ID các kết quả chính ở objective cha mà kết quả chính này hỗ trợ',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  supportingKeyResultIds?: number[];
}
