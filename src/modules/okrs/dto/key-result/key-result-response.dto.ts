import { ApiProperty } from '@nestjs/swagger';
import { KeyResultStatus } from '../../enum/key-result-status.enum';
import { CheckInFrequency } from '../../enum/check-in-frequency.enum';

/**
 * DTO for key result support information
 */
export class KeyResultSupportInfoDto {
  /**
   * ID của key result được hỗ trợ
   * @example 2
   */
  @ApiProperty({
    description: 'ID của kết quả chính được hỗ trợ',
    example: 2,
  })
  id: number;

  /**
   * Tiêu đề của key result được hỗ trợ
   * @example "Tăng doanh thu bán hàng lên 5 tỷ"
   */
  @ApiProperty({
    description: 'Tiêu đề của kết quả chính được hỗ trợ',
    example: 'Tăng doanh thu bán hàng lên 5 tỷ',
  })
  title: string;

  /**
   * ID của objective chứa key result được hỗ trợ
   * @example 3
   */
  @ApiProperty({
    description: 'ID của mục tiêu chứa kết quả chính được hỗ trợ',
    example: 3,
    nullable: true,
  })
  objectiveId: number | null;
}

/**
 * DTO for key result response
 */
export class KeyResultResponseDto {
  /**
   * Unique identifier for the key result
   * @example 1
   */
  @ApiProperty({
    description: 'ID của kết quả chính',
    example: 1,
  })
  id: number;

  /**
   * ID of the objective this key result belongs to
   * @example 1
   */
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
    nullable: true,
  })
  objectiveId: number | null;

  /**
   * Title of the key result
   * @example "Tăng số lượng khách hàng mới lên 1000"
   */
  @ApiProperty({
    description: 'Tiêu đề kết quả chính',
    example: 'Tăng số lượng khách hàng mới lên 1000',
  })
  title: string;

  /**
   * Detailed description of the key result
   * @example "Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết kết quả chính',
    example:
      'Tăng số lượng khách hàng mới thông qua các chiến dịch marketing trên mạng xã hội',
    nullable: true,
  })
  description: string | null;

  /**
   * Target value of the key result
   * @example 1000
   */
  @ApiProperty({
    description: 'Giá trị mục tiêu',
    example: 1000,
  })
  targetValue: number;

  /**
   * Current value of the key result
   * @example 800
   */
  @ApiProperty({
    description: 'Giá trị hiện tại',
    example: 800,
    nullable: true,
  })
  currentValue: number | null;

  /**
   * Initial value of the key result
   * @example 500
   */
  @ApiProperty({
    description: 'Giá trị ban đầu',
    example: 500,
    nullable: true,
  })
  startValue: number | null;

  /**
   * Unit of measurement for the key result
   * @example "khách hàng"
   */
  @ApiProperty({
    description: 'Đơn vị đo lường',
    example: 'khách hàng',
    nullable: true,
  })
  unit: string | null;

  /**
   * Display format for the key result value
   * @example "number"
   */
  @ApiProperty({
    description: 'Định dạng hiển thị',
    example: 'number',
    nullable: true,
  })
  format: string | null;

  /**
   * Key result completion progress (percentage)
   * @example 60
   */
  @ApiProperty({
    description: 'Tiến độ hoàn thành kết quả chính (phần trăm)',
    example: 60,
    nullable: true,
  })
  progress: number | null;

  /**
   * Status of the key result
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái kết quả chính',
    enum: KeyResultStatus,
    example: KeyResultStatus.ACTIVE,
    nullable: true,
  })
  status: KeyResultStatus | null;

  /**
   * Method used to measure the key result
   * @example "Số lượng đăng ký mới"
   */
  @ApiProperty({
    description: 'Phương pháp đo lường',
    example: 'Số lượng đăng ký mới',
    nullable: true,
  })
  measurementMethod: string | null;

  /**
   * Weight of the key result within its objective (0-100)
   * @example 30
   */
  @ApiProperty({
    description: 'Trọng số trong mục tiêu (0-100)',
    example: 30,
    nullable: true,
  })
  weight: number | null;

  /**
   * Frequency of key result updates
   * @example "WEEKLY"
   */
  @ApiProperty({
    description: 'Tần suất cập nhật',
    enum: CheckInFrequency,
    example: CheckInFrequency.WEEKLY,
    nullable: true,
  })
  checkInFrequency: CheckInFrequency | null;

  /**
   * Creation timestamp (in milliseconds)
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (tính bằng mili giây)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   * @example 1672617600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (tính bằng mili giây)',
    example: 1672617600000,
    nullable: true,
  })
  updatedAt: number | null;

  /**
   * Danh sách các kết quả chính mà kết quả chính này hỗ trợ
   * @example [{ id: 2, title: "Tăng doanh thu bán hàng lên 5 tỷ", objectiveId: 3 }]
   */
  @ApiProperty({
    description:
      'Danh sách các kết quả chính được hỗ trợ bởi kết quả chính này',
    type: [KeyResultSupportInfoDto],
    nullable: true,
  })
  supportingKeyResults?: KeyResultSupportInfoDto[];
}
