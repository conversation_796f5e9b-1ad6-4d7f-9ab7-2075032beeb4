import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { TodoCollaborator } from '../entities/todo-collaborator.entity';
import { TodoCollaboratorQueryDto } from '../dto/todo-collaborator/todo-collaborator-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository xử lý truy vấn dữ liệu cho cộng tác viên công việc
 */
@Injectable()
export class TodoCollaboratorRepository {
  constructor(
    @InjectRepository(TodoCollaborator)
    private readonly repository: Repository<TodoCollaborator>,
  ) {}

  /**
   * Lấy repository gốc
   * @returns Repository gốc
   */
  getRepository(): Repository<TodoCollaborator> {
    return this.repository;
  }

  /**
   * Tạo mới cộng tác viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu cộng tác viên
   * @param entityManager EntityManager cho transaction
   * @returns Cộng tác viên đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<TodoCollaborator>,
    entityManager?: EntityManager,
  ): Promise<TodoCollaborator> {
    const repository = entityManager
      ? entityManager.getRepository(TodoCollaborator)
      : this.repository;
    const collaborator = repository.create({ ...data, tenantId });
    return repository.save(collaborator);
  }

  /**
   * Tìm cộng tác viên theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID cộng tác viên
   * @returns Cộng tác viên nếu tìm thấy, null nếu không
   */
  async findById(
    tenantId: number,
    id: number,
  ): Promise<TodoCollaborator | null> {
    return this.repository.findOne({ where: { id, tenantId } });
  }

  /**
   * Tìm tất cả cộng tác viên với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách cộng tác viên đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoCollaboratorQueryDto,
  ): Promise<PaginatedResult<TodoCollaborator>> {
    const {
      page = 1,
      limit = 10,
      todoId,
      userId,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('collaborator');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('collaborator.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc todoId nếu được cung cấp
    if (todoId) {
      queryBuilder.andWhere('collaborator.todoId = :todoId', { todoId });
    }

    // Áp dụng bộ lọc userId nếu được cung cấp
    if (userId) {
      queryBuilder.andWhere('collaborator.userId = :userId', { userId });
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`collaborator.${sortBy}`, sortDirection);

    // Áp dụng phân trang
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm tất cả cộng tác viên của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách cộng tác viên
   */
  async findByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoCollaborator[]> {
    return this.repository.find({
      where: { todoId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Kiểm tra người dùng có phải là cộng tác viên của công việc không
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @param userId ID người dùng
   * @returns true nếu là cộng tác viên, false nếu không
   */
  async isCollaborator(
    tenantId: number,
    todoId: number,
    userId: number,
  ): Promise<boolean> {
    const count = await this.repository.count({
      where: { todoId, userId, tenantId },
    });
    return count > 0;
  }

  /**
   * Xóa cộng tác viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID cộng tác viên
   * @returns Kết quả xóa
   */
  async remove(tenantId: number, id: number): Promise<void> {
    await this.repository.delete({ id, tenantId });
  }

  /**
   * Xóa cộng tác viên theo todoId và userId
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @param userId ID người dùng
   * @param entityManager EntityManager cho transaction
   * @returns Kết quả xóa
   */
  async removeByTodoIdAndUserId(
    tenantId: number,
    todoId: number,
    userId: number,
    entityManager?: EntityManager,
  ): Promise<void> {
    const repository = entityManager
      ? entityManager.getRepository(TodoCollaborator)
      : this.repository;
    await repository.delete({ todoId, userId, tenantId });
  }
}
