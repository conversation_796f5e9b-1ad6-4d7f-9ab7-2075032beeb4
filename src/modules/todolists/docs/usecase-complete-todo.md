## 2.2.2.11 <PERSON><PERSON> tả use case <PERSON><PERSON><PERSON> thành công việc (Complete Todo)

**<PERSON>ô tả ngắn:** Cho phép người dùng đánh dấu một công việc là đã hoàn thành và cung cấp thông tin bổ sung về kết quả công việc.

### Luồng các sự kiện:

#### Luồng cơ bản:
1) <PERSON>hi người dùng chọn "Hoàn thành công việc", hệ thống hiển thị chi tiết công việc đang được xử lý.
2) Người dùng nhấn vào nút "<PERSON><PERSON>h dấu hoàn thành" trên giao diện chi tiết công việc.
3) Hệ thống hiển thị form hoàn thành công việc với các trường: phần trăm hoàn thành, mô tả kết quả, và tệp đ<PERSON><PERSON> kè<PERSON> (nếu cần).
4) Người dùng nhập thông tin hoàn thành công việc và nhấn "Xác nhận hoàn thành".
5) Hệ thống kiểm tra dữ liệu nhập vào, cập nhật trạng thái công việc thành "Hoàn thành" (COMPLETED), lưu thời điểm hoàn thành và thông báo "Công việc đã được hoàn thành".
6) Use case kết thúc.

#### Luồng rẽ nhánh:
1) Nếu người dùng không nhập đủ thông tin bắt buộc (phần trăm hoàn thành, mô tả kết quả), hệ thống hiển thị cảnh báo và yêu cầu người dùng nhập đầy đủ thông tin.
2) Nếu người dùng nhấn "Hủy", hệ thống trở về màn hình chi tiết công việc mà không thay đổi trạng thái công việc.
3) Nếu công việc có các công việc con chưa hoàn thành, hệ thống hiển thị cảnh báo và yêu cầu người dùng hoàn thành tất cả công việc con trước khi đánh dấu công việc cha là hoàn thành.
4) Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.

### Các yêu cầu đặc biệt:
- Cho phép người dùng đính kèm tệp minh chứng hoàn thành công việc.
- Hệ thống tự động gửi thông báo cho người tạo công việc khi công việc được hoàn thành.

### Tiền điều kiện:
- Người dùng đã đăng nhập.
- Người dùng là người được giao công việc hoặc người tạo công việc.
- Công việc đang ở trạng thái "Đang xử lý" (IN_PROGRESS).

### Hậu điều kiện:
- Công việc được chuyển sang trạng thái "Hoàn thành" (COMPLETED).
- Thời điểm hoàn thành (completedAt) được cập nhật.
- Người tạo công việc nhận được thông báo về việc hoàn thành.

### Điểm mở rộng:
- Tích hợp với hệ thống chấm điểm để tự động đánh giá kết quả công việc dựa trên thời gian hoàn thành và chất lượng.
- Tích hợp với module thống kê để cập nhật hiệu suất làm việc của người dùng. 