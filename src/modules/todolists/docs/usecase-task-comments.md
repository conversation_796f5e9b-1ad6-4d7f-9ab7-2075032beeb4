## 2.2.2.15 <PERSON><PERSON>ả use case Bình luận và thảo luận trên công việc

**Mô tả ngắn:** Cho phép người dùng bình luận, thảo luận và trao đổi thông tin về một công việc.

### Luồng các sự kiện:

#### Luồng cơ bản:
1) Khi người dùng mở màn hình chi tiết công việc, hệ thống hiển thị danh sách các bình luận hiện có và form để thêm bình luận mới.
2) Người dùng nhập nội dung bình luận (text, hình ảnh, hoặc đính kèm tệp).
3) Người dùng nhấn "Gửi" và hệ thống lưu bình luận vào cơ sở dữ liệu.
4) Hệ thống hiển thị bình luận mới trong danh sách bình luận và gửi thông báo cho những người liên quan.
5) Use case kết thúc.

#### Luồng rẽ nhánh:
1) Nếu người dùng không nhập nội dung bình luận nhưng nhấn "Gửi", hệ thống hiển thị cảnh báo "Vui lòng nhập nội dung bình luận".
2) Nếu người dùng chọn trả lời một bình luận cụ thể, hệ thống đánh dấu bình luận mới là phản hồi cho bình luận đó và hiển thị phân cấp phù hợp.
3) Nếu người dùng muốn chỉnh sửa bình luận đã gửi, họ có thể chọn "Chỉnh sửa" trên bình luận của mình, và hệ thống cho phép sửa nội dung.
4) Nếu người dùng muốn xóa bình luận, họ có thể chọn "Xóa" trên bình luận của mình, hệ thống hiển thị hộp xác nhận và xóa bình luận nếu người dùng xác nhận.
5) Khi có lỗi hệ thống/DB, hiển thị thông báo "Lỗi hệ thống, vui lòng thử lại sau" và kết thúc use case.

### Các yêu cầu đặc biệt:
- Hỗ trợ rich text, emoji, và đính kèm tệp trong bình luận.
- Cho phép đề cập (@mention) người dùng khác để gửi thông báo trực tiếp.
- Lưu trữ lịch sử chỉnh sửa bình luận để người dùng có thể xem phiên bản trước.

### Tiền điều kiện:
- Người dùng đã đăng nhập.
- Người dùng có quyền xem và bình luận về công việc (người tạo, người được giao, cộng tác viên).

### Hậu điều kiện:
- Bình luận mới được lưu và hiển thị trong danh sách bình luận.
- Những người liên quan nhận được thông báo về bình luận mới.
- Nếu bình luận chứa đề cập (@mention), người được đề cập nhận được thông báo riêng.

### Điểm mở rộng:
- Tích hợp với hệ thống AI để tự động phân loại và gắn thẻ bình luận theo nội dung.
- Bổ sung tính năng phản ứng (reaction) với bình luận như thích, không thích, vui, buồn.
- Cho phép tạo task mới từ bình luận hoặc chuyển đổi bình luận thành task. 