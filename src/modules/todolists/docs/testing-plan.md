# 3.3. <PERSON><PERSON><PERSON> thử

## 3.3.1. <PERSON><PERSON> hoạch kiểm thử

### <PERSON><PERSON><PERSON> tiêu kiểm thử
- <PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống hoạt động đúng theo các yêu cầu chức năng đã xác định.
Phát hiện và khắc phục lỗi trước khi triển khai hệ thống vào môi trường thực tế.
<PERSON><PERSON>c minh tính đúng đắn của luồng hoạt động chính và các trường hợp ngoại lệ.
<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống có t<PERSON>h <PERSON>n đ<PERSON>nh, bảo mật và hiệu suất đáp ứng nhu cầu sử dụng.
Đ<PERSON>h giá trải nghiệm người dùng trên các tính năng chính của hệ thống.

### Phạm vi kiểm thử
- **<PERSON><PERSON><PERSON> thử chức năng**: <PERSON><PERSON><PERSON> tra tất cả các chức năng cơ bản củ<PERSON> hệ thống bao gồ<PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, qu<PERSON>n lý OK<PERSON>s, quản lý công việc và báo cáo thống kê.
- **Kiểm thử giao diện người dùng**: Đảm bảo giao diện người dùng thân thiện, dễ sử dụng và hiển thị đúng trên các thiết bị khác nhau.
- **Kiểm thử bảo mật**: Kiểm tra tính bảo mật của hệ thống, đảm bảo dữ liệu người dùng được bảo vệ và chỉ người có quyền mới có thể truy cập.
- **Kiểm thử hiệu suất**: Đảm bảo hệ thống đáp ứng nhanh, xử lý được lượng dữ liệu lớn và số lượng người dùng đồng thời.

### Chiến lược kiểm thử
1. **Kiểm thử đơn vị (Unit Testing)**:
Kiểm thử từng thành phần riêng lẻ của hệ thống (service, repository, controller).
Sử dụng Jest framework để tự động hóa kiểm thử.
Tập trung vào logic nghiệp vụ phức tạp trong các service.

2. **Kiểm thử tích hợp (Integration Testing)**:
Kiểm thử sự tương tác giữa các thành phần hệ thống.
Tập trung vào API endpoints và luồng dữ liệu từ controller đến database.
Sử dụng cả môi trường giả lập và môi trường test thực.

3. **Kiểm thử hệ thống (System Testing)**:
Kiểm thử toàn bộ hệ thống như một đơn vị hoàn chỉnh.
Thực hiện kiểm thử end-to-end các luồng nghiệp vụ.
Sử dụng công cụ kiểm thử tự động kết hợp kiểm thử thủ công.

4. **Kiểm thử chấp nhận (Acceptance Testing)**:
Kiểm thử dựa trên các use case và kịch bản người dùng thực tế.
Đánh giá khả năng đáp ứng yêu cầu nghiệp vụ của hệ thống.
Có sự tham gia của người dùng cuối trong quá trình kiểm thử.

### Tiêu chí bắt đầu và kết thúc kiểm thử

**Tiêu chí bắt đầu**:
Hoàn thành phát triển các chức năng cần kiểm thử.
Môi trường kiểm thử đã được cấu hình đầy đủ và sẵn sàng.
Các tài liệu use case và kịch bản kiểm thử đã được chuẩn bị.
Dữ liệu test đã được tạo và sẵn sàng sử dụng.

**Tiêu chí kết thúc**:
Tất cả test case đã được thực thi ít nhất một lần.
Độ bao phủ code đạt ít nhất 80% cho các thành phần quan trọng.
Không còn lỗi nghiêm trọng hay lỗi ảnh hưởng đến chức năng chính.
Tất cả các use case đã được kiểm thử và đạt kết quả mong đợi.
Hiệu suất hệ thống đáp ứng tiêu chuẩn đã đề ra.

## 3.3.2. Kịch bản kiểm thử

### Đăng ký và Đăng nhập

#### Đăng ký tài khoản công ty

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| REG-001 | 1. Truy cập trang đăng ký<br>2. Nhập đầy đủ thông tin hợp lệ (tên công ty, email, mật khẩu, thông tin liên hệ)<br>3. Nhấn nút "Đăng ký" | - Hệ thống tạo tài khoản thành công<br>- Hiển thị thông báo đăng ký thành công<br>- Chuyển hướng đến trang xác nhận email |
| REG-002 | 1. Truy cập trang đăng ký<br>2. Nhập email đã tồn tại trong hệ thống<br>3. Nhập các thông tin còn lại hợp lệ<br>4. Nhấn nút "Đăng ký" | - Hệ thống hiển thị thông báo lỗi "Email đã được sử dụng"<br>- Không tạo tài khoản mới |
| REG-003 | 1. Truy cập trang đăng ký<br>2. Bỏ trống các trường bắt buộc<br>3. Nhấn nút "Đăng ký" | - Hệ thống hiển thị thông báo lỗi cho các trường bắt buộc<br>- Form vẫn giữ nguyên thông tin đã nhập |

#### Đăng nhập hệ thống

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| LOGIN-001 | 1. Truy cập trang đăng nhập<br>2. Nhập email và mật khẩu hợp lệ<br>3. Nhấn nút "Đăng nhập" | - Đăng nhập thành công<br>- Chuyển hướng đến trang dashboard<br>- Hiển thị thông tin người dùng đã đăng nhập |
| LOGIN-002 | 1. Truy cập trang đăng nhập<br>2. Nhập email không tồn tại<br>3. Nhập mật khẩu bất kỳ<br>4. Nhấn nút "Đăng nhập" | - Hệ thống hiển thị thông báo "Tài khoản không tồn tại hoặc mật khẩu không đúng"<br>- Không cho phép đăng nhập |
| LOGIN-003 | 1. Truy cập trang đăng nhập<br>2. Nhập email hợp lệ<br>3. Nhập mật khẩu sai<br>4. Nhấn nút "Đăng nhập" | - Hệ thống hiển thị thông báo "Tài khoản không tồn tại hoặc mật khẩu không đúng"<br>- Không cho phép đăng nhập |

#### Đăng xuất

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| LOGOUT-001 | 1. Đăng nhập thành công vào hệ thống<br>2. Nhấn nút đăng xuất | - Người dùng đăng xuất thành công<br>- Phiên làm việc kết thúc<br>- Chuyển hướng đến trang đăng nhập |
| LOGOUT-002 | 1. Đăng nhập thành công vào hệ thống<br>2. Không thực hiện thao tác nào trong 30 phút<br>3. Thực hiện một hành động bất kỳ | - Hệ thống tự động đăng xuất<br>- Hiển thị thông báo hết phiên làm việc<br>- Chuyển hướng đến trang đăng nhập |

### Quản lý người dùng

#### Thêm nhân viên

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| EMP-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý nhân viên"<br>3. Nhấn "Thêm nhân viên"<br>4. Nhập đầy đủ thông tin hợp lệ<br>5. Nhấn "Lưu" | - Hệ thống tạo tài khoản nhân viên mới<br>- Hiển thị thông báo thành công<br>- Nhân viên mới xuất hiện trong danh sách |
| EMP-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý nhân viên"<br>3. Nhấn "Thêm nhân viên"<br>4. Nhập email đã tồn tại<br>5. Nhấn "Lưu" | - Hệ thống hiển thị thông báo lỗi "Email đã tồn tại"<br>- Không tạo tài khoản mới |

#### Phân quyền cho tài khoản User

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| PERM-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý người dùng"<br>3. Chọn một người dùng<br>4. Mở tab "Phân quyền"<br>5. Chọn các quyền cần cấp<br>6. Nhấn "Lưu thay đổi" | - Hệ thống cập nhật quyền cho người dùng<br>- Hiển thị thông báo "Cập nhật quyền thành công"<br>- Các quyền mới được áp dụng ngay lập tức |
| PERM-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý người dùng"<br>3. Chọn một người dùng<br>4. Mở tab "Phân quyền"<br>5. Hủy tất cả quyền<br>6. Nhấn "Lưu thay đổi" | - Hệ thống cập nhật quyền cho người dùng<br>- Hiển thị thông báo "Cập nhật quyền thành công"<br>- Người dùng không có quyền truy cập các chức năng |

### Quản lý OKRs

#### Thiết lập OKRs cho công ty/nhóm

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| OKR-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Nhấn "Tạo OKRs mới"<br>4. Chọn loại "OKRs công ty/nhóm"<br>5. Nhập tiêu đề, mục tiêu, thời gian<br>6. Thêm các key results<br>7. Nhấn "Lưu" | - Hệ thống tạo OKRs mới cho công ty/nhóm<br>- Hiển thị thông báo tạo thành công<br>- OKRs mới xuất hiện trong danh sách |
| OKR-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Nhấn "Tạo OKRs mới"<br>4. Chọn loại "OKRs công ty/nhóm"<br>5. Bỏ trống các trường bắt buộc<br>6. Nhấn "Lưu" | - Hệ thống hiển thị thông báo lỗi cho các trường bắt buộc<br>- Không tạo OKRs mới |

#### Xem toàn bộ OKRs

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| OKR-003 | 1. Đăng nhập với tài khoản admin hoặc user<br>2. Truy cập trang "Danh sách OKRs"<br>3. Xem danh sách OKRs | - Hiển thị danh sách OKRs phân theo loại (công ty, nhóm, cá nhân)<br>- Hiển thị tiến độ hoàn thành của từng OKR<br>- Cho phép lọc và tìm kiếm |
| OKR-004 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Danh sách OKRs"<br>3. Tìm kiếm OKRs theo từ khóa<br>4. Lọc theo loại OKRs | - Hệ thống hiển thị kết quả tìm kiếm phù hợp<br>- Các bộ lọc hoạt động chính xác |

#### Gửi nhắc nhở Check-in

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| CHECKIN-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Gửi nhắc nhở Check-in"<br>5. Chọn người nhận và nội dung<br>6. Nhấn "Gửi" | - Hệ thống gửi thông báo đến người nhận<br>- Hiển thị thông báo gửi thành công<br>- Lưu lại lịch sử gửi nhắc nhở |
| CHECKIN-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Gửi nhắc nhở Check-in"<br>5. Không chọn người nhận<br>6. Nhấn "Gửi" | - Hệ thống hiển thị thông báo lỗi "Vui lòng chọn ít nhất một người nhận"<br>- Không gửi thông báo |

#### Đánh giá cuối chu kỳ OKRs

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| EVAL-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR đã kết thúc chu kỳ<br>4. Nhấn "Đánh giá cuối kỳ"<br>5. Nhập đánh giá cho từng key result<br>6. Nhấn "Hoàn tất đánh giá" | - Hệ thống lưu đánh giá cuối kỳ<br>- Cập nhật trạng thái OKR thành "Đã đánh giá"<br>- Hiển thị thông báo đánh giá thành công |
| EVAL-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR chưa kết thúc chu kỳ<br>4. Thử nhấn "Đánh giá cuối kỳ" | - Hệ thống hiển thị thông báo "Chưa đến thời điểm đánh giá cuối kỳ"<br>- Không cho phép đánh giá |

#### Thiết lập OKRs cá nhân

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| OKR-005 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Nhấn "Tạo OKRs cá nhân"<br>4. Liên kết với OKRs công ty (nếu cần)<br>5. Nhập mục tiêu và key results<br>6. Nhấn "Lưu" | - Hệ thống tạo OKRs cá nhân mới<br>- Hiển thị thông báo tạo thành công<br>- OKRs mới xuất hiện trong danh sách OKRs cá nhân |
| OKR-006 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Nhấn "Tạo OKRs cá nhân"<br>4. Nhập nội dung không hợp lệ (quá ít key results, mục tiêu không rõ ràng)<br>5. Nhấn "Lưu" | - Hệ thống hiển thị các gợi ý cải thiện<br>- Cho phép chỉnh sửa trước khi lưu |

#### Tự đánh giá OKRs

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| SELF-EVAL-001 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Tự đánh giá"<br>5. Nhập phần trăm hoàn thành và ghi chú cho mỗi key result<br>6. Nhấn "Gửi đánh giá" | - Hệ thống lưu thông tin tự đánh giá<br>- Cập nhật trạng thái thành "Đã tự đánh giá"<br>- Hiển thị thông báo "Đánh giá thành công" |
| SELF-EVAL-002 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Tự đánh giá"<br>5. Bỏ trống phần ghi chú cho key result có % hoàn thành thấp<br>6. Nhấn "Gửi đánh giá" | - Hệ thống hiển thị thông báo lỗi "Vui lòng nhập ghi chú cho key results có phần trăm hoàn thành dưới 70%"<br>- Không lưu đánh giá |

### Quản lý công việc

#### Tạo công việc mới

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| TASK-001 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Nhập đầy đủ thông tin (tiêu đề, mô tả, người được giao, dự án, độ ưu tiên)<br>5. Nhấn "Tạo công việc" | - Hệ thống tạo công việc mới với trạng thái "Chờ xử lý"<br>- Hiển thị thông báo "Công việc đã được tạo thành công"<br>- Công việc mới xuất hiện trong danh sách |
| TASK-002 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Bỏ trống trường tiêu đề<br>5. Nhấn "Tạo công việc" | - Hệ thống hiển thị thông báo lỗi "Vui lòng nhập tiêu đề công việc"<br>- Không tạo công việc mới |
| TASK-003 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Chọn dự án user không phải thành viên<br>5. Nhấn "Tạo công việc" | - Hệ thống hiển thị thông báo lỗi "Bạn không phải là thành viên của dự án này"<br>- Không tạo công việc mới |

#### Xem thống kê công việc

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| STATS-001 | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Thống kê công việc"<br>3. Chọn khoảng thời gian (tuần/tháng/quý)<br>4. Nhấn "Xem thống kê" | - Hệ thống hiển thị biểu đồ và số liệu thống kê<br>- Thông tin về số lượng công việc theo trạng thái, điểm trung bình, thời gian hoàn thành<br>- Dữ liệu phù hợp với khoảng thời gian đã chọn |
| STATS-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Thống kê công việc"<br>3. Chọn chế độ xem "Toàn bộ công ty"<br>4. Chọn khoảng thời gian<br>5. Nhấn "Xem thống kê" | - Hệ thống hiển thị thống kê toàn công ty<br>- Hiển thị biểu đồ so sánh hiệu suất các phòng ban<br>- Hiển thị biểu đồ phân bố công việc |

#### Chấm điểm công việc

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| SCORE-001 | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc đã hoàn thành<br>3. Nhấn "Chấm điểm"<br>4. Chọn số sao và nhập nhận xét<br>5. Nhấn "Xác nhận đánh giá" | - Hệ thống lưu điểm đánh giá<br>- Cập nhật số sao cho công việc<br>- Hiển thị thông báo "Đã chấm điểm thành công"<br>- Gửi thông báo cho người thực hiện công việc |
| SCORE-002 | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc chưa hoàn thành<br>3. Thử chấm điểm | - Hệ thống hiển thị thông báo "Chỉ có thể chấm điểm cho công việc đã hoàn thành"<br>- Không cho phép chấm điểm |
| SCORE-003 | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc đã hoàn thành<br>3. Nhấn "Chấm điểm"<br>4. Chọn số sao thấp (1-2 sao)<br>5. Không nhập nhận xét<br>6. Nhấn "Xác nhận đánh giá" | - Hệ thống hiển thị thông báo "Vui lòng nhập nhận xét khi chấm điểm thấp"<br>- Không lưu đánh giá |

### Tìm kiếm và quản lý thông tin

#### Tìm kiếm trong hệ thống

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| SEARCH-001 | 1. Đăng nhập vào hệ thống<br>2. Nhập từ khóa vào ô tìm kiếm chung<br>3. Nhấn Enter hoặc biểu tượng tìm kiếm | - Hệ thống hiển thị kết quả tìm kiếm phù hợp<br>- Kết quả được phân loại theo nhóm (OKRs, Công việc, Nhân viên)<br>- Cho phép lọc kết quả theo loại |
| SEARCH-002 | 1. Đăng nhập vào hệ thống<br>2. Nhập từ khóa không có kết quả tìm kiếm<br>3. Nhấn Enter | - Hệ thống hiển thị thông báo "Không tìm thấy kết quả nào phù hợp"<br>- Hiển thị gợi ý tìm kiếm |

#### Quản lý thông tin cá nhân

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| PROFILE-001 | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Thông tin cá nhân"<br>3. Nhấn "Chỉnh sửa thông tin"<br>4. Cập nhật thông tin<br>5. Nhấn "Lưu thay đổi" | - Hệ thống cập nhật thông tin cá nhân<br>- Hiển thị thông báo "Cập nhật thông tin thành công"<br>- Thông tin mới được hiển thị |
| PROFILE-002 | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Thông tin cá nhân"<br>3. Nhấn "Đổi mật khẩu"<br>4. Nhập mật khẩu hiện tại, mật khẩu mới và xác nhận mật khẩu mới<br>5. Nhấn "Cập nhật mật khẩu" | - Hệ thống cập nhật mật khẩu mới<br>- Hiển thị thông báo "Đổi mật khẩu thành công"<br>- Yêu cầu đăng nhập lại |

### Hỗ trợ và báo cáo

#### Gửi yêu cầu hỗ trợ

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| SUPPORT-001 | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Hỗ trợ"<br>3. Chọn loại yêu cầu hỗ trợ<br>4. Nhập nội dung yêu cầu<br>5. Nhấn "Gửi yêu cầu" | - Hệ thống lưu yêu cầu hỗ trợ<br>- Hiển thị thông báo "Đã gửi yêu cầu hỗ trợ thành công"<br>- Yêu cầu xuất hiện trong danh sách |
| SUPPORT-002 | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Hỗ trợ"<br>3. Chọn loại yêu cầu hỗ trợ<br>4. Không nhập nội dung<br>5. Nhấn "Gửi yêu cầu" | - Hệ thống hiển thị thông báo lỗi "Vui lòng nhập nội dung yêu cầu"<br>- Không gửi yêu cầu |

#### Xuất báo cáo

| Test ID | Các bước kiểm thử | Kết quả mong đợi |
|---------|-------------------|------------------|
| REPORT-001 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Báo cáo"<br>3. Chọn loại báo cáo<br>4. Chọn khoảng thời gian<br>5. Nhấn "Xuất báo cáo" | - Hệ thống tạo và tải xuống file báo cáo<br>- File báo cáo chứa đúng dữ liệu đã chọn<br>- Định dạng file đúng (PDF/Excel) |
| REPORT-002 | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Báo cáo"<br>3. Chọn loại báo cáo<br>4. Nhấn "Xuất báo cáo" mà không chọn khoảng thời gian | - Hệ thống hiển thị thông báo "Vui lòng chọn khoảng thời gian"<br>- Không xuất báo cáo |

## 3.3.3. Kết quả kiểm thử

| Test ID | Chức năng | Các bước kiểm thử | Kết quả mong đợi | Kết quả thực tế |
|---------|-----------|-------------------|------------------|-----------------|
| REG-001 | Đăng ký tài khoản công ty | 1. Truy cập trang đăng ký<br>2. Nhập đầy đủ thông tin hợp lệ<br>3. Nhấn "Đăng ký" | - Tạo tài khoản thành công<br>- Hiển thị thông báo đăng ký thành công<br>- Chuyển hướng đến trang xác nhận email | [Kết quả thực tế sẽ được điền khi thực hiện] |
| REG-002 | Đăng ký tài khoản công ty | 1. Truy cập trang đăng ký<br>2. Nhập email đã tồn tại trong hệ thống<br>3. Nhập các thông tin còn lại hợp lệ<br>4. Nhấn nút "Đăng ký" | - Hệ thống hiển thị thông báo lỗi "Email đã được sử dụng"<br>- Không tạo tài khoản mới | [Kết quả thực tế sẽ được điền khi thực hiện] |
| REG-003 | Đăng ký tài khoản công ty | 1. Truy cập trang đăng ký<br>2. Bỏ trống các trường bắt buộc<br>3. Nhấn nút "Đăng ký" | - Hệ thống hiển thị thông báo lỗi cho các trường bắt buộc<br>- Form vẫn giữ nguyên thông tin đã nhập | [Kết quả thực tế sẽ được điền khi thực hiện] |
| LOGIN-001 | Đăng nhập hệ thống | 1. Truy cập trang đăng nhập<br>2. Nhập email và mật khẩu hợp lệ<br>3. Nhấn "Đăng nhập" | - Đăng nhập thành công<br>- Chuyển hướng đến dashboard<br>- Hiển thị thông tin người dùng đã đăng nhập | [Kết quả thực tế sẽ được điền khi thực hiện] |
| LOGIN-002 | Đăng nhập hệ thống | 1. Truy cập trang đăng nhập<br>2. Nhập email không tồn tại<br>3. Nhập mật khẩu bất kỳ<br>4. Nhấn nút "Đăng nhập" | - Hệ thống hiển thị thông báo "Tài khoản không tồn tại hoặc mật khẩu không đúng"<br>- Không cho phép đăng nhập | [Kết quả thực tế sẽ được điền khi thực hiện] |
| LOGIN-003 | Đăng nhập hệ thống | 1. Truy cập trang đăng nhập<br>2. Nhập email hợp lệ<br>3. Nhập mật khẩu sai<br>4. Nhấn nút "Đăng nhập" | - Hệ thống hiển thị thông báo "Tài khoản không tồn tại hoặc mật khẩu không đúng"<br>- Không cho phép đăng nhập | [Kết quả thực tế sẽ được điền khi thực hiện] |
| EMP-001 | Thêm nhân viên | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý nhân viên"<br>3. Nhấn "Thêm nhân viên"<br>4. Nhập đầy đủ thông tin hợp lệ<br>5. Nhấn "Lưu" | - Hệ thống tạo tài khoản nhân viên mới<br>- Hiển thị thông báo thành công<br>- Nhân viên mới xuất hiện trong danh sách | [Kết quả thực tế sẽ được điền khi thực hiện] |
| EMP-002 | Thêm nhân viên | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý nhân viên"<br>3. Nhấn "Thêm nhân viên"<br>4. Nhập email đã tồn tại<br>5. Nhấn "Lưu" | - Hệ thống hiển thị thông báo lỗi "Email đã tồn tại"<br>- Không tạo tài khoản mới | [Kết quả thực tế sẽ được điền khi thực hiện] |
| PERM-001 | Phân quyền cho tài khoản User | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý người dùng"<br>3. Chọn một người dùng<br>4. Mở tab "Phân quyền"<br>5. Chọn các quyền cần cấp<br>6. Nhấn "Lưu thay đổi" | - Hệ thống cập nhật quyền cho người dùng<br>- Hiển thị thông báo "Cập nhật quyền thành công"<br>- Các quyền mới được áp dụng ngay lập tức | [Kết quả thực tế sẽ được điền khi thực hiện] |
| OKR-001 | Thiết lập OKRs cho công ty/nhóm | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Nhấn "Tạo OKRs mới"<br>4. Chọn loại "OKRs công ty/nhóm"<br>5. Nhập tiêu đề, mục tiêu, thời gian<br>6. Thêm các key results<br>7. Nhấn "Lưu" | - Hệ thống tạo OKRs mới cho công ty/nhóm<br>- Hiển thị thông báo tạo thành công<br>- OKRs mới xuất hiện trong danh sách | [Kết quả thực tế sẽ được điền khi thực hiện] |
| OKR-004 | Xem toàn bộ OKRs | 1. Đăng nhập với tài khoản admin hoặc user<br>2. Truy cập trang "Danh sách OKRs"<br>3. Xem danh sách OKRs | - Hiển thị danh sách OKRs phân theo loại (công ty, nhóm, cá nhân)<br>- Hiển thị tiến độ hoàn thành của từng OKR<br>- Cho phép lọc và tìm kiếm | [Kết quả thực tế sẽ được điền khi thực hiện] |
| CHECKIN-001 | Gửi nhắc nhở Check-in | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Gửi nhắc nhở Check-in"<br>5. Chọn người nhận và nội dung<br>6. Nhấn "Gửi" | - Hệ thống gửi thông báo đến người nhận<br>- Hiển thị thông báo gửi thành công<br>- Lưu lại lịch sử gửi nhắc nhở | [Kết quả thực tế sẽ được điền khi thực hiện] |
| EVAL-001 | Đánh giá cuối chu kỳ OKRs | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Quản lý OKRs"<br>3. Chọn một OKR đã kết thúc chu kỳ<br>4. Nhấn "Đánh giá cuối kỳ"<br>5. Nhập đánh giá cho từng key result<br>6. Nhấn "Hoàn tất đánh giá" | - Hệ thống lưu đánh giá cuối kỳ<br>- Cập nhật trạng thái OKR thành "Đã đánh giá"<br>- Hiển thị thông báo đánh giá thành công | [Kết quả thực tế sẽ được điền khi thực hiện] |
| OKR-003 | Thiết lập OKRs cá nhân | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Nhấn "Tạo OKRs cá nhân"<br>4. Liên kết với OKRs công ty (nếu cần)<br>5. Nhập mục tiêu và key results<br>6. Nhấn "Lưu" | - Hệ thống tạo OKRs cá nhân mới<br>- Hiển thị thông báo tạo thành công<br>- OKRs mới xuất hiện trong danh sách OKRs cá nhân | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SELF-EVAL-001 | Tự đánh giá OKRs | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "OKRs cá nhân"<br>3. Chọn một OKR đang hoạt động<br>4. Nhấn "Tự đánh giá"<br>5. Nhập phần trăm hoàn thành và ghi chú cho mỗi key result<br>6. Nhấn "Gửi đánh giá" | - Hệ thống lưu thông tin tự đánh giá<br>- Cập nhật trạng thái thành "Đã tự đánh giá"<br>- Hiển thị thông báo "Đánh giá thành công" | [Kết quả thực tế sẽ được điền khi thực hiện] |
| TASK-001 | Tạo công việc mới | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Nhập đầy đủ thông tin (tiêu đề, mô tả, người được giao, dự án, độ ưu tiên)<br>5. Nhấn "Tạo công việc" | - Hệ thống tạo công việc mới với trạng thái "Chờ xử lý"<br>- Hiển thị thông báo "Công việc đã được tạo thành công"<br>- Công việc mới xuất hiện trong danh sách | [Kết quả thực tế sẽ được điền khi thực hiện] |
| TASK-002 | Tạo công việc mới | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Bỏ trống trường tiêu đề<br>5. Nhấn "Tạo công việc" | - Hệ thống hiển thị thông báo lỗi "Vui lòng nhập tiêu đề công việc"<br>- Không tạo công việc mới | [Kết quả thực tế sẽ được điền khi thực hiện] |
| TASK-003 | Tạo công việc mới | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Công việc"<br>3. Nhấn "Tạo công việc mới"<br>4. Chọn dự án user không phải thành viên<br>5. Nhấn "Tạo công việc" | - Hệ thống hiển thị thông báo lỗi "Bạn không phải là thành viên của dự án này"<br>- Không tạo công việc mới | [Kết quả thực tế sẽ được điền khi thực hiện] |
| STATS-001 | Xem thống kê công việc | 1. Đăng nhập với tài khoản user<br>2. Truy cập trang "Thống kê công việc"<br>3. Chọn khoảng thời gian (tuần/tháng/quý)<br>4. Nhấn "Xem thống kê" | - Hệ thống hiển thị biểu đồ và số liệu thống kê<br>- Thông tin về số lượng công việc theo trạng thái, điểm trung bình, thời gian hoàn thành<br>- Dữ liệu phù hợp với khoảng thời gian đã chọn | [Kết quả thực tế sẽ được điền khi thực hiện] |
| STATS-002 | Xem thống kê công việc | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Thống kê công việc"<br>3. Chọn chế độ xem "Toàn bộ công ty"<br>4. Chọn khoảng thời gian<br>5. Nhấn "Xem thống kê" | - Hệ thống hiển thị thống kê toàn công ty<br>- Hiển thị biểu đồ so sánh hiệu suất các phòng ban<br>- Hiển thị biểu đồ phân bố công việc | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SCORE-001 | Chấm điểm công việc | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc đã hoàn thành<br>3. Nhấn "Chấm điểm"<br>4. Chọn số sao và nhập nhận xét<br>5. Nhấn "Xác nhận đánh giá" | - Hệ thống lưu điểm đánh giá<br>- Cập nhật số sao cho công việc<br>- Hiển thị thông báo "Đã chấm điểm thành công"<br>- Gửi thông báo cho người thực hiện công việc | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SCORE-002 | Chấm điểm công việc | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc chưa hoàn thành<br>3. Thử chấm điểm | - Hệ thống hiển thị thông báo "Chỉ có thể chấm điểm cho công việc đã hoàn thành"<br>- Không cho phép chấm điểm | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SCORE-003 | Chấm điểm công việc | 1. Đăng nhập với tài khoản có quyền chấm điểm<br>2. Truy cập chi tiết công việc đã hoàn thành<br>3. Nhấn "Chấm điểm"<br>4. Chọn số sao thấp (1-2 sao)<br>5. Không nhập nhận xét<br>6. Nhấn "Xác nhận đánh giá" | - Hệ thống hiển thị thông báo "Vui lòng nhập nhận xét khi chấm điểm thấp"<br>- Không lưu đánh giá | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SEARCH-001 | Tìm kiếm trong hệ thống | 1. Đăng nhập vào hệ thống<br>2. Nhập từ khóa vào ô tìm kiếm chung<br>3. Nhấn Enter hoặc biểu tượng tìm kiếm | - Hệ thống hiển thị kết quả tìm kiếm phù hợp<br>- Kết quả được phân loại theo nhóm (OKRs, Công việc, Nhân viên)<br>- Cho phép lọc kết quả theo loại | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SEARCH-002 | Tìm kiếm trong hệ thống | 1. Đăng nhập vào hệ thống<br>2. Nhập từ khóa không có kết quả tìm kiếm<br>3. Nhấn Enter | - Hệ thống hiển thị thông báo "Không tìm thấy kết quả nào phù hợp"<br>- Hiển thị gợi ý tìm kiếm | [Kết quả thực tế sẽ được điền khi thực hiện] |
| PROFILE-001 | Quản lý thông tin cá nhân | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Thông tin cá nhân"<br>3. Nhấn "Chỉnh sửa thông tin"<br>4. Cập nhật thông tin<br>5. Nhấn "Lưu thay đổi" | - Hệ thống cập nhật thông tin cá nhân<br>- Hiển thị thông báo "Cập nhật thông tin thành công"<br>- Thông tin mới được hiển thị | [Kết quả thực tế sẽ được điền khi thực hiện] |
| PROFILE-002 | Quản lý thông tin cá nhân | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Thông tin cá nhân"<br>3. Nhấn "Đổi mật khẩu"<br>4. Nhập mật khẩu hiện tại, mật khẩu mới và xác nhận mật khẩu mới<br>5. Nhấn "Cập nhật mật khẩu" | - Hệ thống cập nhật mật khẩu mới<br>- Hiển thị thông báo "Đổi mật khẩu thành công"<br>- Yêu cầu đăng nhập lại | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SUPPORT-001 | Gửi yêu cầu hỗ trợ | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Hỗ trợ"<br>3. Chọn loại yêu cầu hỗ trợ<br>4. Nhập nội dung yêu cầu<br>5. Nhấn "Gửi yêu cầu" | - Hệ thống lưu yêu cầu hỗ trợ<br>- Hiển thị thông báo "Đã gửi yêu cầu hỗ trợ thành công"<br>- Yêu cầu xuất hiện trong danh sách | [Kết quả thực tế sẽ được điền khi thực hiện] |
| SUPPORT-002 | Gửi yêu cầu hỗ trợ | 1. Đăng nhập vào hệ thống<br>2. Truy cập trang "Hỗ trợ"<br>3. Chọn loại yêu cầu hỗ trợ<br>4. Không nhập nội dung<br>5. Nhấn "Gửi yêu cầu" | - Hệ thống hiển thị thông báo lỗi "Vui lòng nhập nội dung yêu cầu"<br>- Không gửi yêu cầu | [Kết quả thực tế sẽ được điền khi thực hiện] |
| REPORT-001 | Xuất báo cáo | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Báo cáo"<br>3. Chọn loại báo cáo<br>4. Chọn khoảng thời gian<br>5. Nhấn "Xuất báo cáo" | - Hệ thống tạo và tải xuống file báo cáo<br>- File báo cáo chứa đúng dữ liệu đã chọn<br>- Định dạng file đúng (PDF/Excel) | [Kết quả thực tế sẽ được điền khi thực hiện] |
| REPORT-002 | Xuất báo cáo | 1. Đăng nhập với tài khoản admin<br>2. Truy cập trang "Báo cáo"<br>3. Chọn loại báo cáo<br>4. Nhấn "Xuất báo cáo" mà không chọn khoảng thời gian | - Hệ thống hiển thị thông báo "Vui lòng chọn khoảng thời gian"<br>- Không xuất báo cáo | [Kết quả thực tế sẽ được điền khi thực hiện] |
</rewritten_file> 