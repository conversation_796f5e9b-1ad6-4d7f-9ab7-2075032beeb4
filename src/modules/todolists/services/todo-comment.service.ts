import { Injectable, Logger } from '@nestjs/common';
import { TodoCommentRepository } from '../repositories/todo-comment.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { TodoComment } from '../entities/todo-comment.entity';
import { CreateTodoCommentDto } from '../dto/todo-comment/create-todo-comment.dto';
import { CreateSystemEventDto } from '../dto/todo-comment/create-system-event.dto';
import { TodoCommentQueryDto } from '../dto/todo-comment/todo-comment-query.dto';
import { TodoCommentResponseDto } from '../dto/todo-comment/todo-comment-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { SystemEventType } from '../enum/system-event-type.enum';

/**
 * Interface cho sự kiện thay đổi trạng thái
 */
interface StatusChangedEventData {
  eventType: SystemEventType.STATUS_CHANGED;
  oldValue: string;
  newValue: string;
  actorId: number;
  changedAt: number;
}

/**
 * Interface cho sự kiện thay đổi cộng tác viên
 */
interface CollaboratorChangedEventData {
  eventType: SystemEventType.COLLABORATOR_CHANGED;
  action: 'added' | 'removed';
  userId: number;
  actorId: number;
  changedAt: number;
}

/**
 * Interface cho sự kiện thay đổi người được giao việc
 */
interface AssigneeChangedEventData {
  eventType: SystemEventType.ASSIGNEE_CHANGED;
  oldUserId: number | null;
  newUserId: number | null;
  actorId: number;
  changedAt: number;
}

/**
 * Union type cho dữ liệu sự kiện hệ thống
 */
type SystemEventData =
  | StatusChangedEventData
  | CollaboratorChangedEventData
  | AssigneeChangedEventData;

/**
 * Service xử lý logic nghiệp vụ cho bình luận công việc
 */
@Injectable()
export class TodoCommentService {
  private readonly logger = new Logger(TodoCommentService.name);

  constructor(
    private readonly todoCommentRepository: TodoCommentRepository,
    private readonly todoRepository: TodoRepository,
  ) {}

  /**
   * Chuyển đổi entity sang DTO
   * @param comment Entity bình luận
   * @returns DTO bình luận
   */
  private mapToDto(comment: TodoComment): TodoCommentResponseDto {
    return {
      id: comment.id,
      todoId: comment.todoId,
      userId: comment.userId,
      contentHtml: comment.contentHtml,
      createdAt: comment.createdAt,
      parentId: comment.parentId,
      commentType: comment.commentType,
      isSystemEvent: comment.isSystemEvent,
      eventData: comment.eventData,
      resources: comment.resources,
      mentions: comment.mentions,
    };
  }

  /**
   * Tạo bình luận mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu bình luận
   * @returns Thông tin bình luận đã tạo
   */
  async createComment(
    tenantId: number,
    currentUserId: number,
    dto: CreateTodoCommentDto,
  ): Promise<TodoCommentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Kiểm tra bình luận cha tồn tại nếu có
      if (dto.parentId) {
        const parentComment = await this.todoCommentRepository.findById(
          tenantId,
          dto.parentId,
        );
        if (!parentComment) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.COMMENT_NOT_FOUND,
            `Không tìm thấy bình luận cha với ID ${dto.parentId}`,
          );
        }
      }

      // Tạo bình luận mới
      const now = Date.now();
      const comment = await this.todoCommentRepository.create(tenantId, {
        todoId: dto.todoId,
        userId: currentUserId,
        contentHtml: dto.contentHtml,
        createdAt: now,
        parentId: dto.parentId || null,
        commentType: dto.commentType,
        isSystemEvent: false,
        resources: dto.resources || null,
        mentions: dto.mentions || null,
      });

      return this.mapToDto(comment);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo bình luận: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_CREATION_FAILED,
        'Không thể tạo bình luận',
      );
    }
  }

  /**
   * Tạo sự kiện hệ thống
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto Dữ liệu sự kiện
   * @returns Thông tin sự kiện đã tạo
   */
  async createSystemEvent(
    tenantId: number,
    dto: CreateSystemEventDto,
  ): Promise<TodoCommentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Tạo nội dung HTML cho sự kiện
      let contentHtml = '';
      switch (dto.eventType) {
        case SystemEventType.STATUS_CHANGED:
          if ('oldValue' in dto.eventData && 'newValue' in dto.eventData) {
            contentHtml = `<p>Trạng thái đã thay đổi từ <strong>${dto.eventData.oldValue}</strong> thành <strong>${dto.eventData.newValue}</strong></p>`;
          }
          break;
        case SystemEventType.COLLABORATOR_CHANGED:
          if ('action' in dto.eventData) {
            if (dto.eventData.action === 'added') {
              contentHtml = `<p>Đã thêm cộng tác viên mới</p>`;
            } else {
              contentHtml = `<p>Đã xóa cộng tác viên</p>`;
            }
          }
          break;
        case SystemEventType.ASSIGNEE_CHANGED:
          if ('oldUserId' in dto.eventData && 'newUserId' in dto.eventData) {
            if (!dto.eventData.oldUserId && dto.eventData.newUserId) {
              contentHtml = `<p>Đã gán công việc cho người thực hiện</p>`;
            } else if (dto.eventData.oldUserId && !dto.eventData.newUserId) {
              contentHtml = `<p>Đã bỏ gán người thực hiện</p>`;
            } else {
              contentHtml = `<p>Đã thay đổi người thực hiện công việc</p>`;
            }
          }
          break;
        default:
          contentHtml = `<p>Sự kiện hệ thống</p>`;
      }

      // Tạo sự kiện hệ thống
      const now = Date.now();
      const systemEvent = await this.todoCommentRepository.create(tenantId, {
        todoId: dto.todoId,
        userId: dto.eventData.actorId,
        contentHtml,
        createdAt: now,
        isSystemEvent: true,
        eventData: dto.eventData as SystemEventData,
      });

      return this.mapToDto(systemEvent);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo sự kiện hệ thống: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.SYSTEM_EVENT_CREATION_FAILED,
        'Không thể tạo sự kiện hệ thống',
      );
    }
  }

  /**
   * Lấy danh sách bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách bình luận đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoCommentQueryDto,
  ): Promise<PaginatedResult<TodoCommentResponseDto>> {
    try {
      const paginatedResult = await this.todoCommentRepository.findAll(
        tenantId,
        query,
      );

      return {
        items: paginatedResult.items.map((comment) => this.mapToDto(comment)),
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách bình luận: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_FETCH_FAILED,
        'Không thể lấy danh sách bình luận',
      );
    }
  }

  /**
   * Lấy danh sách bình luận của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách bình luận
   */
  async findByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoCommentResponseDto[]> {
    try {
      const comments = await this.todoCommentRepository.findByTodoId(
        tenantId,
        todoId,
      );
      return comments.map((comment) => this.mapToDto(comment));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách bình luận của công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_FETCH_FAILED,
        'Không thể lấy danh sách bình luận của công việc',
      );
    }
  }

  /**
   * Lấy chi tiết bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID bình luận
   * @returns Thông tin chi tiết bình luận
   */
  async findById(
    tenantId: number,
    id: number,
  ): Promise<TodoCommentResponseDto> {
    try {
      const comment = await this.todoCommentRepository.findById(tenantId, id);
      if (!comment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.COMMENT_NOT_FOUND,
          `Không tìm thấy bình luận với ID ${id}`,
        );
      }
      return this.mapToDto(comment);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết bình luận: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_FETCH_FAILED,
        'Không thể lấy chi tiết bình luận',
      );
    }
  }

  /**
   * Lấy danh sách trả lời của một bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param parentId ID bình luận cha
   * @returns Danh sách trả lời
   */
  async findReplies(
    tenantId: number,
    parentId: number,
  ): Promise<TodoCommentResponseDto[]> {
    try {
      const replies = await this.todoCommentRepository.findReplies(
        tenantId,
        parentId,
      );
      return replies.map((reply) => this.mapToDto(reply));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách trả lời: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_FETCH_FAILED,
        'Không thể lấy danh sách trả lời',
      );
    }
  }

  /**
   * Xóa bình luận
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param id ID bình luận
   */
  async removeComment(
    tenantId: number,
    currentUserId: number,
    id: number,
  ): Promise<void> {
    try {
      // Kiểm tra bình luận tồn tại
      const comment = await this.todoCommentRepository.findById(tenantId, id);
      if (!comment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.COMMENT_NOT_FOUND,
          `Không tìm thấy bình luận với ID ${id}`,
        );
      }

      // Không cho phép xóa sự kiện hệ thống
      if (comment.isSystemEvent) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.COMMENT_DELETION_FAILED,
          'Không thể xóa sự kiện hệ thống',
        );
      }

      // Kiểm tra quyền xóa bình luận
      if (comment.userId !== currentUserId) {
        // Kiểm tra xem người dùng có phải là người tạo công việc hoặc người được giao việc không
        if (comment.todoId) {
          const todo = await this.todoRepository.findById(
            tenantId,
            comment.todoId,
          );
          if (
            !todo ||
            (todo.createdBy !== currentUserId &&
              todo.assigneeId !== currentUserId)
          ) {
            throw new AppException(
              TODOLISTS_ERROR_CODES.NOT_COMMENT_OWNER,
              'Bạn không có quyền xóa bình luận này',
            );
          }
        } else {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_COMMENT_OWNER,
            'Bạn không có quyền xóa bình luận này',
          );
        }
      }

      // Kiểm tra xem bình luận có trả lời không
      const replies = await this.todoCommentRepository.findReplies(
        tenantId,
        id,
      );
      if (replies.length > 0) {
        // Có thể xóa tất cả trả lời hoặc cập nhật parentId của các trả lời
        // Ở đây chọn xóa tất cả trả lời
        for (const reply of replies) {
          await this.todoCommentRepository.remove(tenantId, reply.id);
        }
      }

      // Xóa bình luận
      await this.todoCommentRepository.remove(tenantId, id);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa bình luận: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.COMMENT_DELETION_FAILED,
        'Không thể xóa bình luận',
      );
    }
  }
}
