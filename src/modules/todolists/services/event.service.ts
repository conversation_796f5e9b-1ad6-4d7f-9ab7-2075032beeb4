import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { TodoCommentRepository } from '../repositories/todo-comment.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { SystemEventType } from '../enum/system-event-type.enum';
import { TodoCommentResponseDto } from '../dto/todo-comment/todo-comment-response.dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { TodoComment } from '../entities/todo-comment.entity';

/**
 * Interface cho sự kiện thay đổi trạng thái
 */
interface StatusChangedEventData {
  eventType: SystemEventType.STATUS_CHANGED;
  oldValue: string;
  newValue: string;
  actorId: number;
  changedAt: number;
}

/**
 * Interface cho sự kiện thay đổi cộng tác viên
 */
interface CollaboratorChangedEventData {
  eventType: SystemEventType.COLLABORATOR_CHANGED;
  action: 'added' | 'removed';
  userId: number;
  actorId: number;
  changedAt: number;
}

/**
 * Interface cho sự kiện thay đổi người được giao việc
 */
interface AssigneeChangedEventData {
  eventType: SystemEventType.ASSIGNEE_CHANGED;
  oldUserId: number | null;
  newUserId: number | null;
  actorId: number;
  changedAt: number;
}

/**
 * Union type cho dữ liệu sự kiện hệ thống
 */
type SystemEventData =
  | StatusChangedEventData
  | CollaboratorChangedEventData
  | AssigneeChangedEventData;

/**
 * Service xử lý các sự kiện hệ thống
 * Được tạo để tránh phụ thuộc vòng tròn giữa các service
 */
@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);
  private readonly todoCommentRepository: TodoCommentRepository;
  private readonly todoCommentRepo: Repository<TodoComment>;

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly todoRepository: TodoRepository,
  ) {
    // Khởi tạo repository thủ công để tránh phụ thuộc vòng tròn
    this.todoCommentRepo = this.entityManager.getRepository(TodoComment);
    this.todoCommentRepository = new TodoCommentRepository(
      this.todoCommentRepo,
    );
  }

  /**
   * Tạo sự kiện hệ thống khi thay đổi cộng tác viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @param userId ID người dùng được thêm/xóa
   * @param actorId ID người thực hiện hành động
   * @param action 'added' hoặc 'removed'
   * @param entityManager EntityManager cho transaction
   * @returns Thông tin sự kiện đã tạo
   */
  async createCollaboratorChangedEvent(
    tenantId: number,
    todoId: number,
    userId: number,
    actorId: number,
    action: 'added' | 'removed',
    entityManager?: EntityManager,
  ): Promise<TodoCommentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Tạo nội dung HTML cho sự kiện
      const contentHtml =
        action === 'added'
          ? `<p>Đã thêm cộng tác viên mới</p>`
          : `<p>Đã xóa cộng tác viên</p>`;

      // Tạo sự kiện hệ thống
      const now = Date.now();

      // Tạo đối tượng comment
      const eventData: CollaboratorChangedEventData = {
        eventType: SystemEventType.COLLABORATOR_CHANGED,
        action,
        userId,
        actorId,
        changedAt: now,
      };

      const commentData = {
        todoId,
        userId: actorId,
        contentHtml,
        createdAt: now,
        isSystemEvent: true,
        eventData,
        tenantId, // Add tenantId to comment data
      };

      // Sử dụng repository được truyền vào hoặc repository mặc định
      if (entityManager) {
        const commentRepo = entityManager.getRepository(TodoComment);
        const systemEvent = commentRepo.create(commentData);
        const savedEvent = await commentRepo.save(systemEvent);
        return this.mapToDto(savedEvent);
      } else {
        const comment = await this.todoCommentRepository.create(
          tenantId,
          commentData,
        );
        return this.mapToDto(comment);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo sự kiện thay đổi cộng tác viên: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.SYSTEM_EVENT_CREATION_FAILED,
        'Không thể tạo sự kiện hệ thống',
      );
    }
  }

  /**
   * Tạo sự kiện hệ thống khi thay đổi trạng thái
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @param oldValue Giá trị cũ
   * @param newValue Giá trị mới
   * @param actorId ID người thực hiện hành động
   * @param entityManager EntityManager cho transaction
   * @returns Thông tin sự kiện đã tạo
   */
  async createStatusChangedEvent(
    tenantId: number,
    todoId: number,
    oldValue: string,
    newValue: string,
    actorId: number,
    entityManager?: EntityManager,
  ): Promise<TodoCommentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${todoId}`,
        );
      }

      // Tạo nội dung HTML cho sự kiện
      const contentHtml = `<p>Trạng thái đã thay đổi từ <strong>${oldValue}</strong> thành <strong>${newValue}</strong></p>`;

      // Tạo sự kiện hệ thống
      const now = Date.now();

      // Tạo đối tượng comment
      const eventData: StatusChangedEventData = {
        eventType: SystemEventType.STATUS_CHANGED,
        oldValue,
        newValue,
        actorId,
        changedAt: now,
      };

      const commentData = {
        todoId,
        userId: actorId,
        contentHtml,
        createdAt: now,
        isSystemEvent: true,
        eventData,
        tenantId, // Add tenantId to comment data
      };

      // Sử dụng repository được truyền vào hoặc repository mặc định
      if (entityManager) {
        const commentRepo = entityManager.getRepository(TodoComment);
        const systemEvent = commentRepo.create(commentData);
        const savedEvent = await commentRepo.save(systemEvent);
        return this.mapToDto(savedEvent);
      } else {
        const comment = await this.todoCommentRepository.create(
          tenantId,
          commentData,
        );
        return this.mapToDto(comment);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo sự kiện thay đổi trạng thái: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.SYSTEM_EVENT_CREATION_FAILED,
        'Không thể tạo sự kiện hệ thống',
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param comment Entity bình luận
   * @returns DTO bình luận
   */
  private mapToDto(comment: any): TodoCommentResponseDto {
    return {
      id: comment.id,
      todoId: comment.todoId,
      userId: comment.userId,
      contentHtml: comment.contentHtml,
      createdAt: comment.createdAt,
      parentId: comment.parentId,
      commentType: comment.commentType,
      isSystemEvent: comment.isSystemEvent,
      eventData: comment.eventData,
      resources: comment.resources,
      mentions: comment.mentions,
    };
  }
}
