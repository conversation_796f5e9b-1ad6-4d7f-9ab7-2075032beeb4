import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { TodoStatus } from '../../enum/todo-status.enum';

/**
 * DTO cho cập nhật trạng thái công việc
 */
export class UpdateTodoStatusDto {
  /**
   * Trạng thái mới của công việc
   * @example "completed"
   */
  @ApiProperty({
    description: 'Trạng thái mới của công việc',
    enum: TodoStatus,
    example: TodoStatus.COMPLETED,
    required: true,
  })
  @IsNotEmpty({ message: 'Trạng thái không được để trống' })
  @IsEnum(TodoStatus, { message: 'Trạng thái không hợp lệ' })
  status: TodoStatus;
}
