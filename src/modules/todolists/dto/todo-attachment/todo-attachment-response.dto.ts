import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tệp đính kèm của công việc
 */
export class TodoAttachmentResponseDto {
  /**
   * ID của tệp đính kèm
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tệp đính kèm',
    example: 1,
  })
  id: number;

  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
  })
  todoId: number;

  /**
   * Tên tệp đính kèm
   * @example "tài liệu dự án.pdf"
   */
  @ApiProperty({
    description: 'Tên tệp đính kèm',
    example: 'tài liệu dự án.pdf',
  })
  filename: string;

  /**
   * URL của tệp đính kèm
   * @example "https://example.com/files/document.pdf"
   */
  @ApiProperty({
    description: 'URL của tệp đính kèm',
    example: 'https://example.com/files/document.pdf',
  })
  url: string;

  /**
   * Loại nội dung của tệp (MIME type)
   * @example "application/pdf"
   */
  @ApiProperty({
    description: 'Loại nội dung của tệp (MIME type)',
    example: 'application/pdf',
    nullable: true,
  })
  contentType: string | null;

  /**
   * Kích thước tệp (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước tệp (byte)',
    example: 1024000,
    nullable: true,
  })
  size: number | null;

  /**
   * Thời gian tạo (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * ID của người tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người tạo',
    example: 1,
    nullable: true,
  })
  createdBy: number | null;
}
