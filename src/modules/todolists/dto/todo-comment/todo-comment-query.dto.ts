import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsInt, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { CommentType } from '../../enum/comment-type.enum';

/**
 * DTO cho truy vấn danh sách bình luận của công việc
 */
export class TodoCommentQueryDto extends QueryDto {
  /**
   * Lọc theo ID công việc
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID công việc',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId?: number;

  /**
   * Lọc theo ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> theo ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID người dùng phải là số nguyên' })
  @Min(1, { message: 'ID người dùng phải lớn hơn 0' })
  userId?: number;

  /**
   * Lọc theo ID bình luận cha
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID bình luận cha',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID bình luận cha phải là số nguyên' })
  @Min(1, { message: 'ID bình luận cha phải lớn hơn 0' })
  parentId?: number;

  /**
   * Lọc theo loại bình luận
   * @example "note"
   */
  @ApiProperty({
    description: 'Lọc theo loại bình luận',
    enum: CommentType,
    example: CommentType.NOTE,
    required: false,
  })
  @IsOptional()
  @IsEnum(CommentType, { message: 'Loại bình luận không hợp lệ' })
  commentType?: CommentType;

  /**
   * Lọc theo loại (bình luận người dùng hoặc sự kiện hệ thống)
   * @example false
   */
  @ApiProperty({
    description: 'Lọc theo loại (bình luận người dùng hoặc sự kiện hệ thống)',
    example: false,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean({ message: 'isSystemEvent phải là boolean' })
  isSystemEvent?: boolean;
}
