import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách dự án
 */
export class ProjectQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái hoạt động
   * @example true
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;
}
