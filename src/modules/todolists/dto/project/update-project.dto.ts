import { ApiProperty } from '@nestjs/swagger';
import { IsString, MaxLength, IsOptional, IsBoolean } from 'class-validator';

/**
 * DTO cho cập nhật dự án
 */
export class UpdateProjectDto {
  /**
   * Tiêu đề dự án
   * @example "Dự án phát triển website (cập nhật)"
   */
  @ApiProperty({
    description: 'Tiêu đề dự án',
    example: 'Dự án phát triển website (cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  title?: string;

  /**
   * Mô tả dự án
   * @example "Dự án phát triển website cho công ty ABC (cập nhật)"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả dự án',
    example: 'Dự án phát triển website cho công ty ABC (cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Trạng thái hoạt động của dự án
   * @example true
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của dự án',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  isActive?: boolean;
}
