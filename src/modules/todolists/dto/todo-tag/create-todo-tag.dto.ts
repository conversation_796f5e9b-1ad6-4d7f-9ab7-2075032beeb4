import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho tạo liên kết giữa todo và label
 */
export class CreateTodoTagDto {
  /**
   * ID của label
   * @example 1
   */
  @ApiProperty({
    description: 'ID của label',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID label không được để trống' })
  @IsInt({ message: 'ID label phải là số nguyên' })
  @Min(1, { message: 'ID label phải lớn hơn 0' })
  labelsId: number;
}
