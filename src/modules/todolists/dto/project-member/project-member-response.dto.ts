import { ApiProperty } from '@nestjs/swagger';
import { ProjectMemberRole } from '../../enum/project-member-role.enum';

/**
 * DTO cho phản hồi thông tin thành viên dự án
 */
export class ProjectMemberResponseDto {
  /**
   * ID thành viên dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID thành viên dự án',
    example: 1,
  })
  id: number;

  /**
   * ID dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID dự án',
    example: 1,
    nullable: true,
  })
  projectId: number | null;

  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    nullable: true,
  })
  userId: number | null;

  /**
   * Vai trò của thành viên trong dự án
   * @example "member"
   */
  @ApiProperty({
    description: 'Vai trò của thành viên trong dự án',
    enum: ProjectMemberRole,
    example: ProjectMemberRole.MEMBER,
  })
  role: ProjectMemberRole;

  /**
   * Thời gian thêm thành viên (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian thêm thành viên (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Thời gian cập nhật thành viên (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật thành viên (timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  updatedAt: number | null;
}
