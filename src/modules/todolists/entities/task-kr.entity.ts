import { Entity, Column } from 'typeorm';

/**
 * Entity representing the mapping between tasks and key results
 */
@Entity('task_kr')
export class TaskKr {
  /**
   * ID of the task from the todos table
   */
  @Column({ name: 'task_id', type: 'integer', primary: true, nullable: false })
  taskId: number;

  /**
   * ID of the key result from the key_results table
   */
  @Column({ name: 'kr_id', type: 'integer', primary: true, nullable: false })
  krId: number;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
