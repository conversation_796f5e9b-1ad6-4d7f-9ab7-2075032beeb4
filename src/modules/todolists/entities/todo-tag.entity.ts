import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing labels attached to todos
 */
@Entity('todo_tags')
export class TodoTag {
  /**
   * Unique identifier for the tag record
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the todo
   */
  @Column({ name: 'todo_id', type: 'integer', nullable: true })
  todoId: number | null;

  /**
   * ID of the label
   */
  @Column({ name: 'labels_id', type: 'integer', nullable: true })
  labelsId: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
