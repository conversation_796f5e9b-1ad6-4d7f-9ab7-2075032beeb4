import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TodoCollaboratorService } from '../services/todo-collaborator.service';
import { CreateTodoCollaboratorDto } from '../dto/todo-collaborator/create-todo-collaborator.dto';
import { TodoCollaboratorQueryDto } from '../dto/todo-collaborator/todo-collaborator-query.dto';
import { TodoCollaboratorResponseDto } from '../dto/todo-collaborator/todo-collaborator-response.dto';

/**
 * Controller xử lý các API liên quan đến cộng tác viên công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TodoCollaboratorResponseDto)
@Controller('api/v1/todo-collaborators')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoCollaboratorController {
  constructor(
    private readonly todoCollaboratorService: TodoCollaboratorService,
  ) {}

  /**
   * Thêm cộng tác viên cho công việc
   */
  @Post()
  @ApiOperation({ summary: 'Thêm cộng tác viên cho công việc' })
  @ApiResponse({
    status: 201,
    description: 'Cộng tác viên đã được thêm thành công',
    schema: ApiResponseDto.getSchema(TodoCollaboratorResponseDto),
  })
  async addCollaborator(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTodoCollaboratorDto,
  ): Promise<ApiResponseDto<TodoCollaboratorResponseDto>> {
    const collaborator = await this.todoCollaboratorService.addCollaborator(
      Number(user.tenantId),
      user.id,
      createDto,
    );
    return ApiResponseDto.created(
      collaborator,
      'Đã thêm cộng tác viên thành công',
    );
  }

  /**
   * Lấy danh sách cộng tác viên
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách cộng tác viên' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách cộng tác viên',
    schema: ApiResponseDto.getPaginatedSchema(TodoCollaboratorResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: TodoCollaboratorQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoCollaboratorResponseDto>>> {
    const paginatedCollaborators = await this.todoCollaboratorService.findAll(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(
      paginatedCollaborators,
      'Lấy danh sách cộng tác viên thành công',
    );
  }

  /**
   * Lấy danh sách cộng tác viên của một công việc
   */
  @Get('by-todo/:todoId')
  @ApiOperation({ summary: 'Lấy danh sách cộng tác viên của một công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách cộng tác viên của công việc',
    schema: ApiResponseDto.getArraySchema(TodoCollaboratorResponseDto),
  })
  async findByTodoId(
    @CurrentUser() user: JwtPayload,
    @Param('todoId', ParseIntPipe) todoId: number,
  ): Promise<ApiResponseDto<TodoCollaboratorResponseDto[]>> {
    const collaborators = await this.todoCollaboratorService.findByTodoId(
      Number(user.tenantId),
      todoId,
    );
    return ApiResponseDto.success(
      collaborators,
      'Lấy danh sách cộng tác viên của công việc thành công',
    );
  }

  /**
   * Xóa cộng tác viên
   */
  @Delete(':todoId/:userId')
  @ApiOperation({ summary: 'Xóa cộng tác viên' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiParam({ name: 'userId', description: 'ID người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Cộng tác viên đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  async removeCollaborator(
    @CurrentUser() user: JwtPayload,
    @Param('todoId', ParseIntPipe) todoId: number,
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.todoCollaboratorService.removeCollaborator(
      Number(user.tenantId),
      user.id,
      todoId,
      userId,
    );
    return ApiResponseDto.success(null, 'Đã xóa cộng tác viên thành công');
  }
}
