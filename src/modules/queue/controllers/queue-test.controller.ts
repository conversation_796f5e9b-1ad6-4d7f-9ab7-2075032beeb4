import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { QueueService } from '../queue.service';
import { QueueName } from '../queue.constants';
import { SendEmailDto } from '../dto/send-email.dto';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller test các chức năng của queue
 */
@ApiTags(SWAGGER_API_TAG.SYSTEM)
@Controller('api/queue-test')
@UseGuards(JwtUserGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class QueueTestController {
  constructor(private readonly queueService: QueueService) {}

  /**
   * Test gửi email qua queue
   */
  @Post('send-email')
  @RequirePermissionEnum(Permission.SYSTEM_SETTING_MANAGE_EMAIL)
  @ApiOperation({ summary: 'Test gửi email qua queue' })
  @ApiResponse({
    status: 201,
    description: 'Đã thêm job gửi email vào queue thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  async testSendEmail(
    @Body() sendEmailDto: SendEmailDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    // Thêm job vào queue email
    const job = await this.queueService.addJob(QueueName.EMAIL, {
      ...sendEmailDto,
      from: '<EMAIL>', // Lấy email của user hiện tại làm người gửi
      createdBy: user.id, // Lưu ID của user tạo job
      createdAt: Date.now(), // Thời gian tạo job
    });

    return ApiResponseDto.created({
      jobId: job.id,
      message: 'Đã thêm job gửi email vào queue thành công',
    });
  }
}
