import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { SendEmailDto } from '../interface/send-email.dto';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly transporter: nodemailer.Transporter;
  private readonly emailConfig: any;

  constructor(private readonly configService: ConfigService) {
    // L<PERSON>y cấu hình email từ ConfigService
    this.emailConfig = {
      smtpHost: this.configService.get<string>('EMAIL_SMTP_HOST'),
      smtpPort: this.configService.get<number>('EMAIL_SMTP_PORT'),
      smtpUser: this.configService.get<string>('EMAIL_SMTP_USER'),
      smtpPass: this.configService.get<string>('EMAIL_SMTP_PASS'),
    };

    // Kiểm tra xem có cấu hình SMTP không
    if (this.emailConfig.smtpUser && this.emailConfig.smtpPass) {
      // Tạo transporter với cấu hình SMTP
      this.transporter = this.createTransporter();
      this.logger.log('Email service initialized with SMTP configuration');
    } else {
      this.logger.warn(
        'SMTP credentials not configured. EmailService will not be available for direct SMTP sending.',
      );
      // Không tạo transporter, sẽ throw error khi gọi sendEmail
    }
  }

  /**
   * Tạo transporter cho Nodemailer
   */
  private createTransporter(): nodemailer.Transporter {
    // Sử dụng cấu hình từ ConfigService hoặc fallback
    const smtpHost =
      this.emailConfig.smtpHost ||
      this.configService.get<string>('EMAIL_SMTP_HOST') ||
      'smtp.gmail.com';
    const smtpPort =
      this.emailConfig.smtpPort ||
      this.configService.get<number>('EMAIL_SMTP_PORT') ||
      587;
    const smtpUser =
      this.emailConfig.smtpUser ||
      this.configService.get<string>('EMAIL_SMTP_USER');
    const smtpPass =
      this.emailConfig.smtpPass ||
      this.configService.get<string>('EMAIL_SMTP_PASS');

    return nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: smtpPort === 465, // true for 465, false for other ports
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    });
  }

  /**
   * Gửi email thông qua SMTP.
   * @param sendEmailDto - Dữ liệu email (to, subject, body).
   * @returns Promise chứa kết quả gửi email hoặc ném lỗi nếu thất bại.
   */
  async sendEmail(sendEmailDto: SendEmailDto): Promise<any> {
    this.logger.log(`Sending email via SMTP to ${sendEmailDto.to}`);

    // Kiểm tra xem transporter có được khởi tạo không
    if (!this.transporter) {
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'SMTP not configured. Please set EMAIL_SMTP_USER and EMAIL_SMTP_PASS environment variables.',
      );
    }

    try {
      // Chuẩn bị dữ liệu email
      const mailOptions = {
        from:
          this.configService.get<string>('EMAIL_FROM') ||
          this.emailConfig.smtpUser,
        to: sendEmailDto.to,
        subject: sendEmailDto.subject,
        html: sendEmailDto.body,
      };

      // Gửi email
      const result = await this.transporter.sendMail(mailOptions);

      this.logger.log(
        `Successfully sent email to ${sendEmailDto.to}. Message ID: ${result.messageId}`,
      );

      return {
        success: true,
        messageId: result.messageId,
        response: result.response,
        envelope: result.envelope,
      };
    } catch (error) {
      this.logger.error(
        `Error sending email to ${sendEmailDto.to}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        `Failed to send email via SMTP: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra kết nối SMTP
   * @returns Promise<boolean> - true nếu kết nối thành công
   */
  async verifyConnection(): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn(
        'SMTP transporter not initialized. Cannot verify connection.',
      );
      return false;
    }

    try {
      await this.transporter.verify();
      this.logger.log('SMTP connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error(
        `SMTP connection verification failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
