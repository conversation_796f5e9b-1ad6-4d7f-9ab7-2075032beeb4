/**
 * Class cho đối tượng xác thực hai lớp (chuyển từ Java TwoFA)
 *
 * Java annotations:
 * @Setter
 * @Getter
 * @Builder
 * @NoArgsConstructor
 * @AllArgsConstructor
 * @FieldDefaults(level = AccessLevel.PRIVATE)
 */
export class TwoFA {
  private _code: string;

  constructor(code?: string) {
    this._code = code || '';
  }

  // Getter
  get code(): string {
    return this._code;
  }

  // Setter
  set code(value: string) {
    this._code = value;
  }
}
