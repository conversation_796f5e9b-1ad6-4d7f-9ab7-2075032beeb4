/**
 * Class cho đối tượng mật khẩu mới (chuyển từ Java NewPassword)
 *
 * Java annotations:
 * @Setter
 * @Getter
 * @Builder
 * @NoArgsConstructor
 * @AllArgsConstructor
 * @FieldDefaults(level = AccessLevel.PRIVATE)
 */
export class NewPassword {
  private _password: string;

  constructor(password?: string) {
    this._password = password || '';
  }

  // Getter
  get password(): string {
    return this._password;
  }

  // Setter
  set password(value: string) {
    this._password = value;
  }
}
