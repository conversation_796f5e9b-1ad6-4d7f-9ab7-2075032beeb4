/**
 * Enum định nghĩa các placeholder có thể sử dụng trong template email.
 * Đ<PERSON><PERSON>c tạo dựa trên PlaceholdersEnum.java
 */
export enum PlaceholdersEnum {
  // Người dùng
  NAME = 'NAME',
  EMAIL = 'EMAIL',
  ADDRESS = 'ADDRESS',
  PHONE = 'PHONE',
  USER_ID = 'USER_ID',
  USER_EMAIL = 'USER_EMAIL',
  USER_PHONE = 'USER_PHONE',
  USER_ADDRESS = 'USER_ADDRESS',
  USER_NAME = 'USER_NAME',
  USER_POINT = 'USER_POINT',
  USER_TYPE = 'USER_TYPE',
  USER_IS_VERIFY = 'USER_IS_VERIFY',
  USER_GENDER = 'USER_GENDER',
  USER_DATE_OF_BIRTH = 'USER_DATE_OF_BIRTH',
  USER_NATION = 'USER_NATION',
  USER_FILE_SLOT_REMAINING = 'USER_FILE_SLOT_REMAINING',
  USER_STORAGE_REMAINING = 'USER_STORAGE_REMAINING',
  USER_AVATAR = 'USER_AVATAR',
  USER_CREATE_DATE = 'USER_CREATE_DATE',
  USER_IS_ENABLE = 'USER_IS_ENABLE',
  USER_TOTAL_DEPOSITE = 'USER_TOTAL_DEPOSITE',
  USER_PLATFORM = 'USER_PLATFORM',
  USER_IS_PAID = 'USER_IS_PAID',
  USER_AFFILIATE_ID = 'USER_AFFILIATE_ID',
  URL_VERIFY_EMAIL = 'URL_VERIFY_EMAIL',
  NEW_PASSWORD = 'NEW_PASSWORD',
  TWO_FA_CODE = 'TWO_FA_CODE',
  NAME_NO_DIACRITICS = 'NAME_NO_DIACRITICS',
  USER_REASON_DISABLE = 'USER_REASON_DISABLE',

  // Đơn hàng
  ORDER_ID = 'ORDER_ID',
  ORDER_STATUS = 'ORDER_STATUS',
  ORDER_TOTAL = 'ORDER_TOTAL',
  ORDER_POINT = 'ORDER_POINT',
  ORDER_CREATE_DATE = 'ORDER_CREATE_DATE',
  ORDER_PAYMENT_DATE = 'ORDER_PAYMENT_DATE',
  ORDER_POINT_NAME = 'ORDER_POINT_NAME',
  ORDER_VND_COUPON_TOTAL = 'ORDER_VND_COUPON_TOTAL',

  // Hóa đơn
  INVOICE_REASON_REJECT = 'INVOICE_REASON_REJECT',

  // Yêu cầu rút tiền
  WITHDRAW_TOTAl = 'WITHDRAW_TOTAl',
  WITHDRAW_ID = 'WITHDRAW_ID',
  WITH_DRAW_REQUEST_DATE = 'WITH_DRAW_REQUEST_DATE',
  WITH_DRAW_STATUS = 'WITH_DRAW_STATUS',
  WITH_DRAW_FINISH_DATE = 'WITH_DRAW_FINISH_DATE',
  THUE_TNCN = 'THUE_TNCN',
  TIEN_THUC_NHAN = 'TIEN_THUC_NHAN',
  WITH_DRAW_REASON = 'WITH_DRAW_REASON',

  // Nhân viên
  EMPLOYEE_ID = 'EMPLOYEE_ID',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME',
  EMPLOYEE_EMAIL = 'EMPLOYEE_EMAIL',
  EMPLOYEE_PHONE = 'EMPLOYEE_PHONE',
  EMPLOYEE_PASSWORD = 'EMPLOYEE_PASSWORD',

  // R_POINT
  RPOINT_NAME = 'RPOINT_NAME',
  RPOINT_PRICE = 'RPOINT_PRICE',

  // Mã khuyến mãi VND
  VND_COUPON_CODE = 'VND_COUPON_CODE',
  VND_COUPON_TOTAL = 'VND_COUPON_TOTAL',

  // Mã khuyến mãi flash sale
  FLASH_SALE_ID = 'FLASH_SALE_ID',
  FLASH_SALE_START_DATE = 'FLASH_SALE_START_DATE',
  FLASH_SALE_END_DATE = 'FLASH_SALE_END_DATE',
  FLASH_SALE_QUANTITY = 'FLASH_SALE_QUANTITY',

  // Hợp đồng nguyên tắc
  RULE_CONTRACT_ID = 'RULE_CONTRACT_ID',
  RULE_CONTRACT_SIGNED_DATE = 'RULE_CONTRACT_SIGNED_DATE',
  RULE_CONTRACT_CREATE_DATE = 'RULE_CONTRACT_CREATE_DATE',
  RULE_REASON = 'RULE_REASON',

  // Affiliate
  AFFLIATE_ACCOUNT_ID = 'AFFLIATE_ACCOUNT_ID',
  AFFILIATE_ACCOUNT_STATUS = 'AFFILIATE_ACCOUNT_STATUS',
  AFFILIATE_RANK_UPDATE_DATE = 'AFFILIATE_RANK_UPDATE_DATE',
  AFFILIATE_ACCOUNT_TYPE = 'AFFILIATE_ACCOUNT_TYPE',
  AFFILIATE_REFERRAL_LINK = 'AFFILIATE_REFERRAL_LINK',
  AFFILIATE_CLICK_COUNT = 'AFFILIATE_CLICK_COUNT',
  AFFILIATE_BALANCE = 'AFFILIATE_BALANCE',
  AFFILIATE_CREATE_DATE = 'AFFILIATE_CREATE_DATE',
  AFFILIATE_STEP = 'AFFILIATE_STEP',
  AFFILIATE_SIGNED_DATE = 'AFFILIATE_SIGNED_DATE',
  AFFILIATE_PERFORMANCE_TOTAL = 'AFFILIATE_PERFORMANCE_TOTAL',
  AFFILIATE_REASON = 'AFFILIATE_REASON',

  // Thông tin ngân hàng
  BANK_NAME = 'BANK_NAME',
  BANK_ACCOUNT_NUMBER = 'BANK_ACCOUNT_NUMBER',
  BANK_ACCOUNT_NAME = 'BANK_ACCOUNT_NAME',
  BANK_LOGO = 'BANK_LOGO',
  BANK_BRANCH = 'BANK_BRANCH',

  // Rank affiliate
  RANK_COMMISSION = 'RANK_COMMISSION',
  RANK_LOGO = 'RANK_LOGO',
  RANK_ID = 'RANK_ID',
  RANK_MIN_CONDITION = 'RANK_MIN_CONDITION',
  RANK_MAX_CODITION = 'RANK_MAX_CODITION',
  RANK_NAME = 'RANK_NAME',

  // Business
  BUSINESS_REPRESENTATIVE_NAME = 'BUSINESS_REPRESENTATIVE_NAME',
  BUSINESS_REPRESENTATIVE_POSITION = 'BUSINESS_REPRESENTATIVE_POSITION',
  BUSINESS_TAX_CODE = 'BUSINESS_TAX_CODE',
  BUSINESS_NAME = 'BUSINESS_NAME',
  BUSINESS_EMAIL = 'BUSINESS_EMAIL',
  BUSINESS_PHONE = 'BUSINESS_PHONE',
  BUSINESS_ADDRESS = 'BUSINESS_ADDRESS',
  BUSINESS_UPDATED = 'BUSINESS_UPDATED',

  // Hệ thống
  SYSTEM_TOTAL_ACCOUNT = 'SYSTEM_TOTAL_ACCOUNT',
  SYSTEM_TOTAL_EMPLOYEEM = 'SYSTEM_TOTAL_EMPLOYEEM',
  SYSTEM_TOTAL_REVENUE = 'SYSTEM_TOTAL_REVENUE',

  // Facebook
  PAGE_NAME = 'PAGE_NAME',

  // Cổng thanh toán
  PAYMENT_GATEWAYS_BANK_LOGO = 'PAYMENT_GATEWAYS_BANK_LOGO',
  PAYMENT_GATEWAYS_BANK_NAME = 'PAYMENT_GATEWAYS_BANK_NAME',
  PAYMENT_GATEWAYS_BANK_ACCOUNT_NAME = 'PAYMENT_GATEWAYS_BANK_ACCOUNT_NAME',
  PAYMENT_GATEWAYS_BANK_ACCOUNT_NUMBER = 'PAYMENT_GATEWAYS_BANK_ACCOUNT_NUMBER',
  PAYMENT_GATEWAYS_BANK_BRANCH = 'PAYMENT_GATEWAYS_BANK_BRANCH',

  // Đề xuất tính năng
  FEATURE_REQUIRE_ID = 'FEATURE_REQUIRE_ID',
  FEATURE_REQUIRE_CREATED_AT = 'FEATURE_REQUIRE_CREATED_AT',
  FEATURE_REQUIRE_CONTENT = 'FEATURE_REQUIRE_CONTENT',

  // Ngày hết hạn yêu cầu
  DEADLINE_DATE = 'DEADLINE_DATE',

  // OTHER
  DATE_NOW = 'DATE_NOW',

  // TICKET
  TIKET_ID = 'TIKET_ID',
}

/**
 * Cung cấp mô tả tiếng Việt cho từng PlaceholdersEnum.
 */
export const PlaceholderDescriptions: Record<PlaceholdersEnum, string> = {
  // Người dùng
  [PlaceholdersEnum.NAME]: 'Tên người dùng',
  [PlaceholdersEnum.EMAIL]: 'Email người dùng',
  [PlaceholdersEnum.ADDRESS]: 'Địa chỉ',
  [PlaceholdersEnum.PHONE]: 'Số điện thoại',
  [PlaceholdersEnum.USER_ID]: 'Mã người dùng',
  [PlaceholdersEnum.USER_EMAIL]: 'Email user',
  [PlaceholdersEnum.USER_PHONE]: 'Số điện thoại user',
  [PlaceholdersEnum.USER_ADDRESS]: 'Địa chỉ user',
  [PlaceholdersEnum.USER_NAME]: 'Tên user', // Lưu ý: Trùng lặp với NAME?
  [PlaceholdersEnum.USER_POINT]: 'Số point hiện tại của người dùng',
  [PlaceholdersEnum.USER_TYPE]: 'Loại tài khoản của người dùng',
  [PlaceholdersEnum.USER_IS_VERIFY]:
    'Trạng thái đã xác thực tài khoản người dùng hay chưa',
  [PlaceholdersEnum.USER_GENDER]: 'Giới tính user',
  [PlaceholdersEnum.USER_DATE_OF_BIRTH]: 'Ngày sinh của người dùng',
  [PlaceholdersEnum.USER_NATION]: 'Quốc gia của người dùng',
  [PlaceholdersEnum.USER_FILE_SLOT_REMAINING]:
    'Slot file còn lại của người dùng',
  [PlaceholdersEnum.USER_STORAGE_REMAINING]:
    'Dung lượng còn lại của người dùng',
  [PlaceholdersEnum.USER_AVATAR]: 'Ảnh đại diện của người dùng',
  [PlaceholdersEnum.USER_CREATE_DATE]: 'Ngày đăng ký tài khoản của người dùng',
  [PlaceholdersEnum.USER_IS_ENABLE]: 'Trạng thái hoạt động của người dùng',
  [PlaceholdersEnum.USER_TOTAL_DEPOSITE]:
    'Tổng tiền chi của người dùng trong hệ thống',
  [PlaceholdersEnum.USER_PLATFORM]: 'Nền tảng tham gia của người dùng',
  [PlaceholdersEnum.USER_IS_PAID]: 'Khách hàng trả phí hay không',
  [PlaceholdersEnum.USER_AFFILIATE_ID]: 'ID người giới thiệu',
  [PlaceholdersEnum.URL_VERIFY_EMAIL]: 'URL xác thực',
  [PlaceholdersEnum.NEW_PASSWORD]: 'Mật khẩu mới',
  [PlaceholdersEnum.TWO_FA_CODE]: 'Mã xác thực hai lớp',
  [PlaceholdersEnum.NAME_NO_DIACRITICS]: 'Tên khách hàng không có dấu',
  [PlaceholdersEnum.USER_REASON_DISABLE]: 'Lý do tạm ngừng tài khoản',

  // Đơn hàng
  [PlaceholdersEnum.ORDER_ID]: 'Mã đơn hàng',
  [PlaceholdersEnum.ORDER_STATUS]: 'Trạng thái đơn hàng',
  [PlaceholdersEnum.ORDER_TOTAL]: 'Giá đơn hàng',
  [PlaceholdersEnum.ORDER_POINT]: 'Point đơn hàng',
  [PlaceholdersEnum.ORDER_CREATE_DATE]: 'Thời gian đặt hàng',
  [PlaceholdersEnum.ORDER_PAYMENT_DATE]: 'Thời gian thanh toán',
  [PlaceholdersEnum.ORDER_POINT_NAME]: 'Tên gói RPOINT',
  [PlaceholdersEnum.ORDER_VND_COUPON_TOTAL]: 'Tiền khuyến mãi',

  // Hóa đơn
  [PlaceholdersEnum.INVOICE_REASON_REJECT]: 'Lý do từ chối hóa đơn',

  // Yêu cầu rút tiền
  [PlaceholdersEnum.WITHDRAW_TOTAl]: 'Tiền rút',
  [PlaceholdersEnum.WITHDRAW_ID]: 'Mã rút tiền',
  [PlaceholdersEnum.WITH_DRAW_REQUEST_DATE]: 'Ngày tạo yêu cầu',
  [PlaceholdersEnum.WITH_DRAW_STATUS]: 'Trạng thái yêu cầu',
  [PlaceholdersEnum.WITH_DRAW_FINISH_DATE]: 'Ngày kết thúc yêu cầu',
  [PlaceholdersEnum.THUE_TNCN]: 'Tiền thuế thu nhập cá nhân',
  [PlaceholdersEnum.TIEN_THUC_NHAN]: 'Tiền thực nhận',
  [PlaceholdersEnum.WITH_DRAW_REASON]: 'Lý do từ chối yêu cầu rút tiền',

  // Nhân viên
  [PlaceholdersEnum.EMPLOYEE_ID]: 'Mã nhân viên',
  [PlaceholdersEnum.EMPLOYEE_NAME]: 'Tên nhân viên',
  [PlaceholdersEnum.EMPLOYEE_EMAIL]: 'Email nhân viên',
  [PlaceholdersEnum.EMPLOYEE_PHONE]: 'Số điện thoại nhân viên',
  [PlaceholdersEnum.EMPLOYEE_PASSWORD]: 'Mật khẩu nhân viên',

  // R_POINT
  [PlaceholdersEnum.RPOINT_NAME]: 'Tên gói RPoint',
  [PlaceholdersEnum.RPOINT_PRICE]: 'Giá tiền RPoint',

  // Mã khuyến mãi VND
  [PlaceholdersEnum.VND_COUPON_CODE]: 'Mã code khuyến mãi',
  [PlaceholdersEnum.VND_COUPON_TOTAL]: 'Tiền khuyến mãi', // Trùng lặp?

  // Mã khuyến mãi flash sale
  [PlaceholdersEnum.FLASH_SALE_ID]: 'Mã Flash Sale',
  [PlaceholdersEnum.FLASH_SALE_START_DATE]: 'Ngày bắt đầu Flash Sale',
  [PlaceholdersEnum.FLASH_SALE_END_DATE]: 'Ngày kết thúc Flash Sale',
  [PlaceholdersEnum.FLASH_SALE_QUANTITY]: 'Số lượng Flash Sale',

  // Hợp đồng nguyên tắc
  [PlaceholdersEnum.RULE_CONTRACT_ID]: 'Mã hợp đồng nguyên tắc',
  [PlaceholdersEnum.RULE_CONTRACT_SIGNED_DATE]: 'Ngày ký hợp đồng nguyên tắc',
  [PlaceholdersEnum.RULE_CONTRACT_CREATE_DATE]: 'Ngày tạo hợp đồng nguyên tắc',
  [PlaceholdersEnum.RULE_REASON]: 'Lý do từ chối hợp đồng nguyên tắc', // Trùng lặp?

  // Affiliate
  [PlaceholdersEnum.AFFLIATE_ACCOUNT_ID]: 'Mã tài khoản Affiliate',
  [PlaceholdersEnum.AFFILIATE_ACCOUNT_STATUS]: 'Trạng thái đối tác Affiliate',
  [PlaceholdersEnum.AFFILIATE_RANK_UPDATE_DATE]: 'Ngày cập nhật rank Affiliate',
  [PlaceholdersEnum.AFFILIATE_ACCOUNT_TYPE]: 'Loại tài khoản Affiliate',
  [PlaceholdersEnum.AFFILIATE_REFERRAL_LINK]: 'Link giới thiệu Affiliate',
  [PlaceholdersEnum.AFFILIATE_CLICK_COUNT]: 'Số lượt click',
  [PlaceholdersEnum.AFFILIATE_BALANCE]:
    'Số tiền hiện tại của tài khoản Affiliate',
  [PlaceholdersEnum.AFFILIATE_CREATE_DATE]: 'Ngày đăng ký Affiliate',
  [PlaceholdersEnum.AFFILIATE_STEP]: 'Step hiện tại của tài khoản',
  [PlaceholdersEnum.AFFILIATE_SIGNED_DATE]: 'Ngày ký tài khoản Affiliate',
  [PlaceholdersEnum.AFFILIATE_PERFORMANCE_TOTAL]:
    'Chỉ số để tính Affiliate rank',
  [PlaceholdersEnum.AFFILIATE_REASON]: 'Lý do từ chối tài khoản Affiliate',

  // Thông tin ngân hàng
  [PlaceholdersEnum.BANK_NAME]: 'Tên ngân hàng của người dùng',
  [PlaceholdersEnum.BANK_ACCOUNT_NUMBER]: 'Số tài khoản ngân hàng',
  [PlaceholdersEnum.BANK_ACCOUNT_NAME]: 'Tên tài khoản ngân hàng',
  [PlaceholdersEnum.BANK_LOGO]: 'Logo ngân hàng',
  [PlaceholdersEnum.BANK_BRANCH]: 'Chi nhánh ngân hàng',

  // Rank affiliate
  [PlaceholdersEnum.RANK_COMMISSION]: 'Hoa hồng của mức rank Affiliate',
  [PlaceholdersEnum.RANK_LOGO]: 'Logo rank Affiliate',
  [PlaceholdersEnum.RANK_ID]: 'ID rank',
  [PlaceholdersEnum.RANK_MIN_CONDITION]: 'Giới hạn dưới của rank',
  [PlaceholdersEnum.RANK_MAX_CODITION]: 'Giới hạn trên của rank',
  [PlaceholdersEnum.RANK_NAME]: 'Tên rank',

  // Business
  [PlaceholdersEnum.BUSINESS_REPRESENTATIVE_NAME]: 'Tên người đại diện',
  [PlaceholdersEnum.BUSINESS_REPRESENTATIVE_POSITION]: 'Vị trí người đại diện',
  [PlaceholdersEnum.BUSINESS_TAX_CODE]: 'Mã số thuế doanh nghiệp',
  [PlaceholdersEnum.BUSINESS_NAME]: 'Tên doanh nghiệp',
  [PlaceholdersEnum.BUSINESS_EMAIL]: 'Email doanh nghiệp',
  [PlaceholdersEnum.BUSINESS_PHONE]: 'Số điện thoại doanh nghiệp',
  [PlaceholdersEnum.BUSINESS_ADDRESS]: 'Địa chỉ doanh nghiệp',
  [PlaceholdersEnum.BUSINESS_UPDATED]:
    'Thời gian cập nhật thông tin doanh nghiệp',

  // Hệ thống
  [PlaceholdersEnum.SYSTEM_TOTAL_ACCOUNT]: 'Tổng số tài khoản của hệ thống',
  [PlaceholdersEnum.SYSTEM_TOTAL_EMPLOYEEM]: 'Tổng số nhân viên của hệ thống',
  [PlaceholdersEnum.SYSTEM_TOTAL_REVENUE]: 'Tổng doanh thu của hệ thống',

  // Facebook
  [PlaceholdersEnum.PAGE_NAME]: 'Tên page facebook của người dùng',

  // Cổng thanh toán
  [PlaceholdersEnum.PAYMENT_GATEWAYS_BANK_NAME]: 'Tên ngân hàng',
  [PlaceholdersEnum.PAYMENT_GATEWAYS_BANK_BRANCH]: 'Chi nhánh ngân hàng',
  [PlaceholdersEnum.PAYMENT_GATEWAYS_BANK_ACCOUNT_NAME]: 'Tên tài khoản',
  [PlaceholdersEnum.PAYMENT_GATEWAYS_BANK_ACCOUNT_NUMBER]: 'Số tài khoản',
  [PlaceholdersEnum.PAYMENT_GATEWAYS_BANK_LOGO]: 'Logo ngân hàng',

  // Đề xuất tính năng
  [PlaceholdersEnum.FEATURE_REQUIRE_ID]: 'Mã đề xuất tính năng',
  [PlaceholdersEnum.FEATURE_REQUIRE_CREATED_AT]:
    'Thời gian gửi đề xuất tính năng',
  [PlaceholdersEnum.FEATURE_REQUIRE_CONTENT]: 'Nội dung đề xuất tính năng',

  // Ngày hết hạn yêu cầu
  [PlaceholdersEnum.DEADLINE_DATE]: 'Ngày hết hạn yêu cầu',

  // OTHER
  [PlaceholdersEnum.DATE_NOW]: 'Ngày hiện tại',

  // TICKET
  [PlaceholdersEnum.TIKET_ID]: 'Mã ticket',
};

// Optional: Hàm trợ giúp để lấy mô tả, tương tự getDescription trong Java
export function getPlaceholderDescription(
  placeholder: PlaceholdersEnum,
): string | undefined {
  return PlaceholderDescriptions[placeholder];
}
