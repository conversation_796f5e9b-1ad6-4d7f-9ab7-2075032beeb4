import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Email (11000-11099)
 */
export const EMAIL_ERROR_CODES = {
  /**
   * Lỗi khi gửi email thất bại
   */
  EMAIL_SEND_FAILED: new ErrorCode(
    11000,
    'Không thể gửi email, vui lòng thử lại sau',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi dữ liệu email không hợp lệ
   */
  INVALID_EMAIL_DATA: new ErrorCode(
    11001,
    'Dữ liệu email không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy mẫu email
   */
  TEMPLATE_NOT_FOUND: new ErrorCode(
    11002,
    'Không tìm thấy mẫu email',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi dữ liệu mẫu không đủ để render email
   */
  TEMPLATE_DATA_INVALID: new ErrorCode(
    11003,
    'Dữ liệu mẫu email không hợp lệ hoặc không đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không đủ quyền gửi email hệ thống
   */
  EMAIL_PERMISSION_DENIED: new ErrorCode(
    11004,
    'Không đủ quyền để gửi email hệ thống',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi tệp đính kèm không hợp lệ
   */
  INVALID_ATTACHMENT: new ErrorCode(
    11005,
    'Tệp đính kèm không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi vượt quá số lượng email gửi trong một khoảng thời gian
   */
  EMAIL_RATE_LIMIT_EXCEEDED: new ErrorCode(
    11006,
    'Đã vượt quá giới hạn số lượng email có thể gửi',
    HttpStatus.TOO_MANY_REQUESTS,
  ),
};
