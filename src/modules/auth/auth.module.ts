import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtUserGuard } from './guards/jwt-user.guard';
import { JwtUtilService } from './guards/jwt.util';
import { ServicesModule } from '@shared/services/services.module';
import { RedisService } from '@shared/services/redis.service';
import { CompanyAccount } from './entities/company-account.entity';
import { User } from './entities/user.entity';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';
import { RolePermission } from './entities/role-permission.entity';
import { UserRole } from './entities/user-role.entity';
import { SocialAccount } from './entities/social-account.entity';
import { CompanyAccountRepository } from './repositories/company-account.repository';
import { UserRepository } from './repositories/user.repository';
import { PermissionRepository } from './repositories/permission.repository';
import { SocialAccountRepository } from './repositories/social-account.repository';
import { CompanyAuthService } from './services/company-auth.service';
import { UserAuthService } from './services/user-auth.service';
import { SocialAuthService } from './services/social-auth.service';
import { GoogleAuthService } from './services/social/google-auth.service';
import { FacebookAuthService } from './services/social/facebook-auth.service';
import { ZaloAuthService } from './services/social/zalo-auth.service';
import { CompanyAuthController } from './controllers/company-auth.controller';
import { UserAuthController } from './controllers/user-auth.controller';
import { SocialAuthController } from './controllers/social-auth.controller';

/**
 * Module quản lý xác thực và ủy quyền
 */
@Global()
@Module({
  imports: [
    ServicesModule,
    TypeOrmModule.forFeature([
      CompanyAccount,
      User,
      Permission,
      Role,
      RolePermission,
      UserRole,
      SocialAccount,
    ]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION_TIME', '1d'),
        },
      }),
    }),
  ],
  controllers: [
    CompanyAuthController,
    UserAuthController,
    SocialAuthController,
  ],
  providers: [
    JwtUserGuard,
    JwtUtilService,
    CompanyAccountRepository,
    UserRepository,
    PermissionRepository,
    SocialAccountRepository,
    CompanyAuthService,
    UserAuthService,
    SocialAuthService,
    GoogleAuthService,
    FacebookAuthService,
    ZaloAuthService,
    // Sử dụng RedisService thực tế từ ServicesModule
    RedisService,
  ],
  exports: [
    PassportModule,
    JwtUserGuard,
    JwtUtilService,
    JwtModule,
    CompanyAccountRepository,
    UserRepository,
    PermissionRepository,
    SocialAccountRepository,
    CompanyAuthService,
    UserAuthService,
    SocialAuthService,
    GoogleAuthService,
    FacebookAuthService,
    ZaloAuthService,
    // RedisService đã được import từ ServicesModule, không cần export lại
  ],
})
export class AuthModule {}
