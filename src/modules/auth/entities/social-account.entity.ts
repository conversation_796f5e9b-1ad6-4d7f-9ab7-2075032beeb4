import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { SocialProvider } from '@/modules/auth/enum';

/**
 * Entity representing social media accounts linked to users
 */
@Entity('social_accounts')
export class SocialAccount {
  /**
   * Unique identifier for the social account
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the user this social account is linked to
   */
  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  /**
   * Provider of the social account (Google, Facebook, Zalo, etc.)
   */
  @Column({ name: 'provider', type: 'varchar', length: 50, nullable: false })
  provider: SocialProvider;

  /**
   * ID of the user on the social platform
   */
  @Column({ name: 'social_id', type: 'varchar', length: 255, nullable: false })
  socialId: string;

  /**
   * Email associated with the social account
   */
  @Column({ name: 'email', type: 'varchar', length: 255, nullable: true })
  email: string | null;

  /**
   * Display name on the social platform
   */
  @Column({
    name: 'display_name',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  displayName: string | null;

  /**
   * URL to profile picture on the social platform
   */
  @Column({ name: 'photo_url', type: 'varchar', length: 500, nullable: true })
  photoUrl: string | null;

  /**
   * Access token from the social platform (encrypted)
   */
  @Column({ name: 'access_token', type: 'text', nullable: true })
  accessToken: string | null;

  /**
   * Refresh token from the social platform (encrypted)
   */
  @Column({ name: 'refresh_token', type: 'text', nullable: true })
  refreshToken: string | null;

  /**
   * Expiration timestamp for the access token
   */
  @Column({ name: 'token_expires_at', type: 'bigint', nullable: true })
  tokenExpiresAt: number | null;

  /**
   * Raw profile data from the social platform (JSON)
   */
  @Column({ name: 'profile_data', type: 'jsonb', nullable: true })
  profileData: any;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
