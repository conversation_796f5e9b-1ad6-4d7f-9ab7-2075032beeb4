import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing special permissions assigned directly to users
 */
@Entity('user_has_permissions')
export class UserPermission {
  /**
   * Unique identifier for the link
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the user
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID of the permission
   */
  @Column({ name: 'permission_id', type: 'integer', nullable: true })
  permissionId: number | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
