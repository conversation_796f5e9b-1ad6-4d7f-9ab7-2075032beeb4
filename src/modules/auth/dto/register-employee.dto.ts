import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

/**
 * DTO cho đăng ký tài khoản nhân viên
 */
export class RegisterEmployeeDto {
  /**
   * Tên đăng nhập của nhân viên
   * @example "nhanvien123"
   */
  @ApiProperty({
    description: 'Tên đăng nhập của nhân viên',
    example: 'nhanvien123',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  @Length(3, 50, { message: 'Tên đăng nhập phải có từ 3 đến 50 ký tự' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới',
  })
  username: string;

  /**
   * Email của nhân viên
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của nhân viên',
    example: '<EMAIL>',
    required: true,
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  /**
   * Mật khẩu của nhân viên
   * @example "Password123!"
   */
  @ApiProperty({
    description: 'Mật khẩu của nhân viên',
    example: 'Password123!',
    required: true,
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @Length(8, 50, { message: 'Mật khẩu phải có từ 8 đến 50 ký tự' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
    {
      message:
        'Mật khẩu phải chứa ít nhất một chữ cái viết thường, một chữ cái viết hoa, một số và một ký tự đặc biệt',
    },
  )
  password: string;

  /**
   * Họ và tên của nhân viên
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Họ và tên của nhân viên',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsNotEmpty({ message: 'Họ và tên không được để trống' })
  @IsString({ message: 'Họ và tên phải là chuỗi' })
  @Length(2, 100, { message: 'Họ và tên phải có từ 2 đến 100 ký tự' })
  fullName: string;

  /**
   * ID phòng ban của nhân viên
   * @example 1
   */
  @ApiProperty({
    description: 'ID phòng ban của nhân viên',
    example: 1,
    required: false,
  })
  @IsOptional()
  departmentId?: number;
}

/**
 * DTO cho phản hồi đăng ký tài khoản nhân viên
 */
export class RegisterEmployeeResponseDto {
  /**
   * Thông báo đăng ký thành công
   * @example "Đăng ký tài khoản nhân viên thành công"
   */
  @ApiProperty({
    description: 'Thông báo đăng ký thành công',
    example: 'Đăng ký tài khoản nhân viên thành công',
  })
  message: string;
}
