import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

/**
 * DTO cho đăng ký tài khoản công ty
 */
export class CompanyRegisterDto {
  @ApiProperty({
    description: 'Tên công ty',
    example: 'Công ty TNHH ABC',
  })
  @IsNotEmpty({ message: 'Tên công ty không được để trống' })
  @IsString({ message: 'Tên công ty phải là chuỗi' })
  @Length(3, 255, { message: 'Tên công ty phải từ 3 đến 255 ký tự' })
  companyName: string;

  @ApiProperty({
    description: 'Email liên hệ của công ty',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: '<PERSON><PERSON> không hợp lệ' })
  companyEmail: string;

  @ApiProperty({
    description: 'Mật khẩu tài khoản',
    example: 'StrongPassword123!',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @Length(8, 50, { message: 'Mật khẩu phải từ 8 đến 50 ký tự' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
    {
      message:
        'Mật khẩu phải chứa ít nhất một chữ cái viết thường, một chữ cái viết hoa, một chữ số và một ký tự đặc biệt',
    },
  )
  password: string;

  // @ApiProperty({
  //   description: 'Số điện thoại liên hệ của công ty',
  //   example: '0912345678',
  //   required: false,
  // })
  // @IsOptional()
  // @IsString({ message: 'Số điện thoại phải là chuỗi' })
  // @Length(10, 15, { message: 'Số điện thoại phải từ 10 đến 15 ký tự' })
  // phoneNumber?: string;

  @ApiProperty({
    description: 'Token reCAPTCHA từ client',
    example: '03AGdBq24PBCbwiDRgMN...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  recaptchaToken?: string;
}
