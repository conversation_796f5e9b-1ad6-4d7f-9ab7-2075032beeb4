import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEmail,
  IsDate,
  IsEnum,
  MaxLength,
  IsISO8601,
} from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho cập nhật thông tin cá nhân của người dùng
 */
export class UpdateUserProfileDto {
  /**
   * Họ tên đầy đủ của người dùng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Họ tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Họ tên phải là chuỗi' })
  @MaxLength(255, { message: 'Họ tên không được vượt quá 255 ký tự' })
  fullName?: string;

  /**
   * Vị trí công việc của người dùng
   * @example "Nhân viên kế toán"
   */
  @ApiProperty({
    description: 'Vị trí công việc của người dùng',
    example: '<PERSON>hân viên kế toán',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Vị trí công việc phải là chuỗi' })
  @MaxLength(255, { message: 'Vị trí công việc không được vượt quá 255 ký tự' })
  position?: string;

  /**
   * Địa chỉ nhà của người dùng
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ nhà của người dùng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address?: string;

  /**
   * Số điện thoại liên hệ của người dùng
   * @example "0912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại liên hệ của người dùng',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @MaxLength(20, { message: 'Số điện thoại không được vượt quá 20 ký tự' })
  phoneNumber?: string;

  /**
   * Ngày sinh của người dùng (định dạng ISO)
   * @example "1990-01-01"
   */
  @ApiProperty({
    description: 'Ngày sinh của người dùng (định dạng ISO)',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsISO8601(
    { strict: true },
    { message: 'Ngày sinh phải có định dạng ISO 8601 (YYYY-MM-DD)' },
  )
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  birthDate?: Date;

  /**
   * Giới tính của người dùng
   * @example "Nam"
   */
  @ApiProperty({
    description: 'Giới tính của người dùng',
    example: 'Nam',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giới tính phải là chuỗi' })
  @MaxLength(10, { message: 'Giới tính không được vượt quá 10 ký tự' })
  gender?: string;

  /**
   * Số CMND/CCCD của người dùng
   * @example "079201012345"
   */
  @ApiProperty({
    description: 'Số CMND/CCCD của người dùng',
    example: '079201012345',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số CMND/CCCD phải là chuỗi' })
  @MaxLength(20, { message: 'Số CMND/CCCD không được vượt quá 20 ký tự' })
  idCardNumber?: string;

  /**
   * Ngày cấp CMND/CCCD (định dạng ISO)
   * @example "2020-01-01"
   */
  @ApiProperty({
    description: 'Ngày cấp CMND/CCCD (định dạng ISO)',
    example: '2020-01-01',
    required: false,
  })
  @IsOptional()
  @IsISO8601(
    { strict: true },
    { message: 'Ngày cấp CMND/CCCD phải có định dạng ISO 8601 (YYYY-MM-DD)' },
  )
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  idCardIssueDate?: Date;

  /**
   * Nơi cấp CMND/CCCD
   * @example "Cục Cảnh sát quản lý hành chính về trật tự xã hội"
   */
  @ApiProperty({
    description: 'Nơi cấp CMND/CCCD',
    example: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nơi cấp CMND/CCCD phải là chuỗi' })
  @MaxLength(255, {
    message: 'Nơi cấp CMND/CCCD không được vượt quá 255 ký tự',
  })
  idCardIssuePlace?: string;

  /**
   * Số tài khoản ngân hàng
   * @example "**********"
   */
  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số tài khoản ngân hàng phải là chuỗi' })
  @MaxLength(50, {
    message: 'Số tài khoản ngân hàng không được vượt quá 50 ký tự',
  })
  bankAccountNumber?: string;

  /**
   * Tên ngân hàng
   * @example "Vietcombank"
   */
  @ApiProperty({
    description: 'Tên ngân hàng',
    example: 'Vietcombank',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên ngân hàng phải là chuỗi' })
  @MaxLength(255, { message: 'Tên ngân hàng không được vượt quá 255 ký tự' })
  bankName?: string;

  /**
   * Mã số thuế cá nhân
   * @example "**********"
   */
  @ApiProperty({
    description: 'Mã số thuế cá nhân',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mã số thuế phải là chuỗi' })
  @MaxLength(50, { message: 'Mã số thuế không được vượt quá 50 ký tự' })
  taxCode?: string;

  /**
   * Số bảo hiểm xã hội
   * @example "**********"
   */
  @ApiProperty({
    description: 'Số bảo hiểm xã hội',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số bảo hiểm xã hội phải là chuỗi' })
  @MaxLength(50, { message: 'Số bảo hiểm xã hội không được vượt quá 50 ký tự' })
  insuranceNumber?: string;
}
