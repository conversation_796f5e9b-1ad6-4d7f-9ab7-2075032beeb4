/**
 * Enum định nghĩa các quyền hạn trong hệ thống.
 * <PERSON><PERSON><PERSON> trị của mỗi enum member theo định dạng "module:action".
 */
export enum Permission {
  USER_VIEW_LIST = 'user:view_list',
  USER_VIEW_DETAIL = 'user:view_detail',
  USER_CREATE = 'user:create',
  USER_UPDATE_PROFILE = 'user:update_profile',
  USER_UPDATE_STATUS = 'user:update_status',
  USER_ASSIGN_ROLE = 'user:assign_role',
  USER_RESET_PASSWORD = 'user:reset_password',
  USER_DELETE = 'user:delete',
  ROLE_VIEW_LIST = 'role:view_list',
  ROLE_VIEW_DETAIL = 'role:view_detail',
  ROLE_CREATE = 'role:create',
  ROLE_UPDATE = 'role:update',
  ROLE_ASSIGN_PERMISSION = 'role:assign_permission',
  ROLE_DELETE = 'role:delete',
  PERMISSION_VIEW_LIST = 'permission:view_list',
  PROJECT_VIEW_LIST = 'project:view_list',
  PROJECT_VIEW_DETAIL = 'project:view_detail',
  PROJECT_CREATE = 'project:create',
  PROJECT_UPDATE_INFO = 'project:update_info',
  PROJECT_UPDATE_STATUS = 'project:update_status',
  PROJECT_DELETE = 'project:delete',
  PROJECT_MANAGE_MEMBERS = 'project:manage_members',
  PROJECT_VIEW_TASKS = 'project:view_tasks',
  PROJECT_CREATE_TASK = 'project:create_task',
  PROJECT_UPDATE_TASK = 'project:update_task',
  PROJECT_DELETE_TASK = 'project:delete_task',
  PROJECT_ASSIGN_TASK = 'project:assign_task',
  PROJECT_UPDATE_TASK_STATUS = 'project:update_task_status',
  PROJECT_LOG_TIME = 'project:log_time',
  PROJECT_VIEW_TIMESHEETS = 'project:view_timesheets',
  PROJECT_MANAGE_FILES = 'project:manage_files',
  PROJECT_VIEW_REPORTS = 'project:view_reports',
  PROJECT_MANAGE_SETTINGS = 'project:manage_settings',
  PROJECT_APPROVE_TIMESHEET = 'project:approve_timesheet',
  CUSTOMER_VIEW_LIST = 'customer:view_list',
  CUSTOMER_VIEW_DETAIL = 'customer:view_detail',
  CUSTOMER_CREATE = 'customer:create',
  CUSTOMER_UPDATE = 'customer:update',
  CUSTOMER_DELETE = 'customer:delete',
  CUSTOMER_MANAGE_CONTACTS = 'customer:manage_contacts',
  CUSTOMER_VIEW_INTERACTIONS = 'customer:view_interactions',
  CUSTOMER_LOG_INTERACTION = 'customer:log_interaction',
  SALES_ORDER_VIEW_LIST = 'sales_order:view_list',
  SALES_ORDER_VIEW_DETAIL = 'sales_order:view_detail',
  SALES_ORDER_CREATE = 'sales_order:create',
  SALES_ORDER_UPDATE = 'sales_order:update',
  SALES_ORDER_DELETE = 'sales_order:delete',
  SALES_ORDER_APPROVE = 'sales_order:approve',
  SALES_ORDER_GENERATE_INVOICE = 'sales_order:generate_invoice',
  INVENTORY_VIEW_ITEMS = 'inventory:view_items',
  INVENTORY_VIEW_ITEM_DETAILS = 'inventory:view_item_details',
  INVENTORY_CREATE_ITEM = 'inventory:create_item',
  INVENTORY_UPDATE_ITEM = 'inventory:update_item',
  INVENTORY_DELETE_ITEM = 'inventory:delete_item',
  INVENTORY_ADJUST_STOCK = 'inventory:adjust_stock',
  INVENTORY_TRANSFER_STOCK = 'inventory:transfer_stock',
  INVENTORY_VIEW_REPORTS = 'inventory:view_reports',
  WAREHOUSE_MANAGE = 'warehouse:manage',
  PURCHASE_ORDER_VIEW_LIST = 'purchase_order:view_list',
  PURCHASE_ORDER_VIEW_DETAIL = 'purchase_order:view_detail',
  PURCHASE_ORDER_CREATE = 'purchase_order:create',
  PURCHASE_ORDER_UPDATE = 'purchase_order:update',
  PURCHASE_ORDER_DELETE = 'purchase_order:delete',
  PURCHASE_ORDER_APPROVE = 'purchase_order:approve',
  PURCHASE_ORDER_RECEIVE_ITEMS = 'purchase_order:receive_items',
  SUPPLIER_VIEW_LIST = 'supplier:view_list',
  SUPPLIER_VIEW_DETAIL = 'supplier:view_detail',
  SUPPLIER_CREATE = 'supplier:create',
  SUPPLIER_UPDATE = 'supplier:update',
  SUPPLIER_DELETE = 'supplier:delete',
  INVOICE_SALES_VIEW_LIST = 'invoice_sales:view_list',
  INVOICE_SALES_VIEW_DETAIL = 'invoice_sales:view_detail',
  INVOICE_SALES_CREATE = 'invoice_sales:create',
  INVOICE_SALES_UPDATE = 'invoice_sales:update',
  INVOICE_SALES_DELETE = 'invoice_sales:delete',
  INVOICE_SALES_SEND_TO_CUSTOMER = 'invoice_sales:send_to_customer',
  PAYMENT_RECEIVABLE_RECORD = 'payment_receivable:record',
  PAYMENT_RECEIVABLE_VIEW_LIST = 'payment_receivable:view_list',
  INVOICE_PURCHASE_VIEW_LIST = 'invoice_purchase:view_list',
  INVOICE_PURCHASE_VIEW_DETAIL = 'invoice_purchase:view_detail',
  INVOICE_PURCHASE_CREATE = 'invoice_purchase:create',
  INVOICE_PURCHASE_UPDATE = 'invoice_purchase:update',
  INVOICE_PURCHASE_DELETE = 'invoice_purchase:delete',
  INVOICE_PURCHASE_APPROVE_FOR_PAYMENT = 'invoice_purchase:approve_for_payment',
  PAYMENT_PAYABLE_RECORD = 'payment_payable:record',
  PAYMENT_PAYABLE_VIEW_LIST = 'payment_payable:view_list',
  EMPLOYEE_VIEW_LIST = 'employee:view_list',
  EMPLOYEE_VIEW_PERSONAL_INFO = 'employee:view_personal_info',
  EMPLOYEE_VIEW_EMPLOYMENT_INFO = 'employee:view_employment_info',
  EMPLOYEE_CREATE = 'employee:create',
  EMPLOYEE_UPDATE_PERSONAL_INFO = 'employee:update_personal_info',
  EMPLOYEE_UPDATE_EMPLOYMENT_INFO = 'employee:update_employment_info',
  EMPLOYEE_TERMINATE = 'employee:terminate',
  EMPLOYEE_VIEW_SALARY_INFO = 'employee:view_salary_info',
  EMPLOYEE_MANAGE_DOCUMENTS = 'employee:manage_documents',
  TIMESHEET_SUBMIT_OWN = 'timesheet:submit_own',
  TIMESHEET_APPROVE_TEAM = 'timesheet:approve_team',
  TIMESHEET_VIEW_ALL = 'timesheet:view_all',
  TIMESHEET_MANAGE_SETTINGS = 'timesheet:manage_settings',
  LEAVE_REQUEST_OWN = 'leave:request_own',
  LEAVE_APPROVE_REQUEST = 'leave:approve_request',
  LEAVE_VIEW_BALANCE = 'leave:view_balance',
  LEAVE_VIEW_CALENDAR = 'leave:view_calendar',
  LEAVE_MANAGE_TYPES = 'leave:manage_types',
  REPORT_VIEW_STANDARD = 'report:view_standard',
  REPORT_CREATE_CUSTOM = 'report:create_custom',
  REPORT_VIEW_FINANCIAL = 'report:view_financial',
  REPORT_VIEW_SALES = 'report:view_sales',
  REPORT_VIEW_INVENTORY = 'report:view_inventory',
  REPORT_VIEW_HR = 'report:view_hr',
  DASHBOARD_VIEW_OWN = 'dashboard:view_own',
  DASHBOARD_VIEW_MANAGEMENT = 'dashboard:view_management',
  SYSTEM_SETTING_MANAGE_GENERAL = 'system_setting:manage_general',
  SYSTEM_SETTING_MANAGE_LOCALIZATION = 'system_setting:manage_localization',
  SYSTEM_SETTING_MANAGE_EMAIL = 'system_setting:manage_email',
  SYSTEM_SETTING_MANAGE_INTEGRATIONS = 'system_setting:manage_integrations',
  AUDIT_LOG_VIEW = 'audit_log:view',
  OKR_CREATE_COMPANY = 'okr:create_company',
  OKR_CREATE_DEPARTMENT = 'okr:create_department',
  HR_VIEW_DASHBOARD = 'hr:view_dashboard',
  DEPARTMENT_VIEW_LIST = 'department:view_list',
  DEPARTMENT_UPDATE = 'department:update',
  DEPARTMENT_VIEW_DETAIL = 'department:view_detail',
  DEPARTMENT_CREATE = 'department:create',
  DEPARTMENT_DELETE = 'department:delete',
  TIMESHEET_EDIT_OWN = 'timesheet:edit_own',
  TIMESHEET_EDIT_TEAM = 'timesheet:edit_team',
  TIMESHEET_DELETE_OWN = 'timesheet:delete_own',
  TIMESHEET_DELETE_TEAM = 'timesheet:delete_team',
  TIMESHEET_EXPORT_OWN = 'timesheet:export_own',
  TIMESHEET_EXPORT_ALL = 'timesheet:export_all',
  TIMESHEET_VIEW_TEAM = 'timesheet:view_team',
  TIMESHEET_VIEW_HISTORY = 'timesheet:view_history',
  TIMESHEET_RECEIVE_NOTIFICATIONS = 'timesheet:receive_notifications',
  TIMESHEET_DELEGATE_APPROVAL = 'timesheet:delegate_approval',
  TIMESHEET_TRACK_CHANGES = 'timesheet:track_changes',
  TIMESHEET_FLAG_DISCREPANCY = 'timesheet:flag_discrepancy',
  TIMESHEET_TRACK_OVERTIME = 'timesheet:track_overtime',
  TIMESHEET_REQUEST_CORRECTION = 'timesheet:request_correction',
  DEPARTMENT_MANAGE_MEMBERS = 'department:manage_members',
  TIMESHEET_TEMP_ACCESS = 'timesheet:temp_access',
  TIMESHEET_SYNC_EXTERNAL = 'timesheet:sync_external',
  TIMESHEET_GENERATE_REPORTS = 'timesheet:generate_reports',
  TIMESHEET_VIEW_STATS = 'timesheet:view_stats',
  TIMESHEET_SET_WORKING_HOURS = 'timesheet:set_working_hours',
  TIMESHEET_MANAGE_TIME_OFF = 'timesheet:manage_time_off',
  TIMESHEET_OVERRIDE_APPROVAL = 'timesheet:override_approval',
  LEAVE_CANCEL_OWN = 'leave:cancel_own',
  LEAVE_CANCEL_TEAM = 'leave:cancel_team',
  LEAVE_EDIT_OWN = 'leave:edit_own',
  LEAVE_EDIT_TEAM = 'leave:edit_team',
  LEAVE_VIEW_ALL = 'leave:view_all',
  LEAVE_EXPORT_OWN = 'leave:export_own',
  LEAVE_EXPORT_ALL = 'leave:export_all',
  LEAVE_MANAGE_BALANCES = 'leave:manage_balances',
  LEAVE_DELEGATE_APPROVAL = 'leave:delegate_approval',
  LEAVE_TRACK_USAGE = 'leave:track_usage',
  LEAVE_VIEW_HISTORY = 'leave:view_history',
  LEAVE_MANAGE_WORKFLOWS = 'leave:manage_workflows',
  LEAVE_RECEIVE_NOTIFICATIONS = 'leave:receive_notifications',

  OKR_VIEW_DETAIL = 'okr:view_detail',
  OKR_CREATE_OBJECTIVE = 'okr:create_objective',
  OKR_UPDATE_OBJECTIVE = 'okr:update_objective',
  OKR_DELETE_OBJECTIVE = 'okr:delete_objective',
  OKR_CREATE_KEY_RESULT = 'okr:create_key_result',
  OKR_UPDATE_KEY_RESULT = 'okr:update_key_result',
  OKR_DELETE_KEY_RESULT = 'okr:delete_key_result',
  OKR_CREATE_CYCLE = 'okr:create_cycle',
  OKR_UPDATE_CYCLE = 'okr:update_cycle',
  OKR_DELETE_CYCLE = 'okr:delete_cycle',
  OKR_CREATE_GOAL = 'okr:create_goal',
  OKR_UPDATE_GOAL = 'okr:update_goal',
  OKR_DELETE_GOAL = 'okr:delete_goal',
  OKR_VIEW_LIST = 'okr:view_list',
  OKR_VIEW_DASHBOARD = 'okr:view_dashboard',
  OKR_VIEW_REPORT = 'okr:view_report',
  OKR_VIEW_GOAL = 'okr:view_goal',
  OKR_VIEW_KEY_RESULT = 'okr:view_key_result',
  OKR_VIEW_CYCLE = 'okr:view_cycle',
  OKR_VIEW_OBJECTIVE = 'okr:view_objective',
  OKR_VIEW_TASK = 'okr:view_task',
  OKR_VIEW_TASK_DETAIL = 'okr:view_task_detail',
  OKR_CREATE_TASK = 'okr:create_task',
  OKR_UPDATE_TASK = 'okr:update_task',
  OKR_DELETE_TASK = 'okr:delete_task',
  OKR_VIEW_TASK_LIST = 'okr:view_task_list',
}
