import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ISocialAuthProvider,
  SocialUserInfo,
} from './social-provider.interface';
import { SocialProvider } from '../../enum';
import axios from 'axios';
import { AppException } from '@/common/exceptions/app.exception';
import { AUTH_ERROR_CODE } from '../../errors/auth-error.code';

/**
 * Service xử lý xác thực qua Google
 */
@Injectable()
export class GoogleAuthService implements ISocialAuthProvider {
  private readonly logger = new Logger(GoogleAuthService.name);
  private readonly googleApiUrl =
    'https://www.googleapis.com/oauth2/v3/userinfo';
  private readonly googleTokenInfoUrl =
    'https://oauth2.googleapis.com/tokeninfo';

  constructor(private readonly configService: ConfigService) {}

  /**
   * <PERSON><PERSON><PERSON> thông tin người dùng từ Google bằng access token
   * @param accessToken Access token từ Google
   * @returns Thông tin người dùng
   */
  async getUserInfo(accessToken: string): Promise<SocialUserInfo> {
    try {
      // Xác thực token trước khi lấy thông tin
      const isValid = await this.verifyToken(accessToken);
      if (!isValid) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_SOCIAL_TOKEN,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      // Gọi API Google để lấy thông tin người dùng
      const response = await axios.get(this.googleApiUrl, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const userData = response.data;

      // Chuyển đổi dữ liệu từ Google sang định dạng chung
      return {
        id: userData.sub,
        email: userData.email,
        name: userData.name,
        firstName: userData.given_name,
        lastName: userData.family_name,
        displayName: userData.name,
        photoUrl: userData.picture,
        provider: SocialProvider.GOOGLE,
        accessToken,
        rawProfile: userData,
      };
    } catch (error) {
      this.logger.error(
        `Error getting user info from Google: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        `Không thể lấy thông tin người dùng từ Google: ${error.message}`,
      );
    }
  }

  /**
   * Xác thực Google access token
   * @param accessToken Access token từ Google
   * @returns True nếu token hợp lệ, false nếu không
   */
  async verifyToken(accessToken: string): Promise<boolean> {
    try {
      // Gọi API Google để xác thực token
      const response = await axios.get(
        `${this.googleTokenInfoUrl}?access_token=${accessToken}`,
      );

      // Kiểm tra xem token có thuộc về ứng dụng của chúng ta không
      const clientId = this.configService.get<string>('GOOGLE_CLIENT_ID');
      if (clientId && response.data.aud !== clientId) {
        this.logger.warn(
          `Token belongs to different client ID: ${response.data.aud}`,
        );
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error verifying Google token: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Lấy loại nhà cung cấp mạng xã hội
   * @returns Google
   */
  getProvider(): SocialProvider {
    return SocialProvider.GOOGLE;
  }
}
