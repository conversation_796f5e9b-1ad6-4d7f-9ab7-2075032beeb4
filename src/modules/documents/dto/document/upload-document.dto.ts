import {
  IsString,
  IsOptional,
  IsNumber,
  IsEnum,
  IsArray,
  IsObject,
  MaxLength,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType } from '../../enums';

/**
 * D<PERSON> để upload tài liệu mới (multipart/form-data)
 */
export class UploadDocumentDto {
  /**
   * File tài liệu
   */
  @ApiProperty({
    description: 'File tài liệu cần upload',
    type: 'string',
    format: 'binary',
  })
  file: any;

  /**
   * Tiêu đề tài liệu
   */
  @ApiProperty({
    description: 'Tiêu đề tài liệu',
    example: 'Chính sách làm việc từ xa',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  title: string;

  /**
   * <PERSON>ô tả tài liệu
   */
  @ApiPropertyOptional({
    description: '<PERSON>ô tả tài liệu',
    example: 'Quy định về làm việc từ xa cho nhân viên công ty',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Loại tài liệu
   */
  @ApiProperty({
    description: 'Loại tài liệu',
    enum: DocumentType,
    example: DocumentType.POLICY,
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  /**
   * ID thư mục
   */
  @ApiPropertyOptional({
    description: 'ID thư mục chứa tài liệu',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  folderId?: number;

  /**
   * Tài liệu có công khai không
   */
  @ApiPropertyOptional({
    description: 'Tài liệu có công khai không (bỏ qua kiểm tra quyền)',
    example: false,
    default: false,
  })
  @IsOptional()
  isPublic?: boolean = false;

  /**
   * Thẻ tag (JSON string)
   */
  @ApiPropertyOptional({
    description: 'Danh sách thẻ tag (JSON string)',
    example: '["hr", "policy", "remote-work"]',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  /**
   * Metadata bổ sung (JSON string)
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung của tài liệu (JSON string)',
    example: '{"author": "HR Department", "version": "1.0"}',
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}

/**
 * DTO response cho upload tài liệu
 */
export class UploadDocumentResponseDto {
  /**
   * ID tài liệu đã tạo
   */
  @ApiProperty({
    description: 'ID tài liệu đã tạo',
    example: 1,
  })
  id: number;

  /**
   * Tiêu đề tài liệu
   */
  @ApiProperty({
    description: 'Tiêu đề tài liệu',
    example: 'Chính sách làm việc từ xa',
  })
  title: string;

  /**
   * Tên file
   */
  @ApiProperty({
    description: 'Tên file đã upload',
    example: 'chinh-sach-lam-viec-tu-xa.pdf',
  })
  fileName: string;

  /**
   * Kích thước file
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1048576,
  })
  fileSize: number;

  /**
   * Loại MIME
   */
  @ApiProperty({
    description: 'Loại MIME',
    example: 'application/pdf',
  })
  mimeType: string;

  /**
   * Khóa S3
   */
  @ApiProperty({
    description: 'Khóa object S3',
    example: 'documents/tenant-1/2024/01/chinh-sach-lam-viec-tu-xa.pdf',
  })
  s3Key: string;

  /**
   * Trạng thái xử lý
   */
  @ApiProperty({
    description: 'Trạng thái xử lý OpenAI',
    example: 'pending',
  })
  processingStatus: string;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * URL tạm thời để download (nếu có)
   */
  @ApiPropertyOptional({
    description: 'URL tạm thời để download file',
    example: 'https://s3.amazonaws.com/bucket/file.pdf?signature=...',
  })
  downloadUrl?: string;
}
