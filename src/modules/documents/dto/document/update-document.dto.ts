import { PartialType } from '@nestjs/swagger';
import { CreateDocumentDto } from './create-document.dto';
import { IsOptional, IsBoolean, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ProcessingStatus } from '../../enums';

/**
 * DTO để cập nhật tài liệu
 */
export class UpdateDocumentDto extends PartialType(CreateDocumentDto) {
  /**
   * Trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của tài liệu',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  /**
   * Phiên bản tài liệu
   */
  @ApiPropertyOptional({
    description: 'Phiên bản tài liệu',
    example: 2,
  })
  @IsOptional()
  version?: number;

  /**
   * Trạng thái xử lý OpenAI
   */
  @ApiPropertyOptional({
    description: 'Trạng thái xử lý OpenAI',
    enum: ProcessingStatus,
    example: ProcessingStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ProcessingStatus)
  processingStatus?: ProcessingStatus;

  /**
   * Lỗi xử lý
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi xử lý (nếu có)',
    example: 'File format not supported',
  })
  @IsOptional()
  processingError?: string;

  /**
   * ID file OpenAI
   */
  @ApiPropertyOptional({
    description: 'ID file OpenAI sau khi upload',
    example: 'file-abc123',
  })
  @IsOptional()
  openaiFileId?: string;

  /**
   * ID Vector Store OpenAI
   */
  @ApiPropertyOptional({
    description: 'ID Vector Store OpenAI',
    example: 'vs-abc123',
  })
  @IsOptional()
  openaiVectorStoreId?: string;
}
