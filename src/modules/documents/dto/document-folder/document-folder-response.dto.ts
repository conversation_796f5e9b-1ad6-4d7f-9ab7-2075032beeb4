import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO response cho thư mục tài liệu
 */
export class DocumentFolderResponseDto {
  /**
   * ID thư mục
   */
  @ApiProperty({
    description: 'ID thư mục',
    example: 1,
  })
  id: number;

  /**
   * Tên thư mục
   */
  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Chính sách nhân sự',
  })
  name: string;

  /**
   * <PERSON><PERSON> tả thư mục
   */
  @ApiPropertyOptional({
    description: '<PERSON>ô tả thư mục',
    example: 'Th<PERSON> mục chứa các chính sách liên quan đến nhân sự',
  })
  description: string | null;

  /**
   * ID thư mục cha
   */
  @ApiPropertyOptional({
    description: 'ID thư mục cha',
    example: null,
  })
  parentId: number | null;

  /**
   * Đường dẫn phân cấp
   */
  @ApiProperty({
    description: 'Đường dẫn phân cấp',
    example: '/chính-sách-nhân-sự',
  })
  path: string;

  /**
   * Cấp độ sâu trong phân cấp
   */
  @ApiProperty({
    description: 'Cấp độ sâu trong phân cấp (0 = gốc)',
    example: 0,
  })
  level: number;

  /**
   * Thứ tự sắp xếp
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp trong cùng cấp độ',
    example: 0,
  })
  sortOrder: number;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của thư mục',
    example: true,
  })
  isActive: boolean;

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung của thư mục',
    example: { color: '#blue', icon: 'folder' },
  })
  metadata: Record<string, any> | null;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'Thời điểm cập nhật cuối (timestamp)',
    example: 1640995200000,
  })
  updatedAt: number | null;

  /**
   * ID người tạo
   */
  @ApiProperty({
    description: 'ID người tạo thư mục',
    example: 1,
  })
  createdBy: number;

  /**
   * ID người cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật thư mục cuối cùng',
    example: 1,
  })
  updatedBy: number | null;

  /**
   * ID tenant
   */
  @ApiProperty({
    description: 'ID tenant',
    example: 1,
  })
  tenantId: number;

  // Computed properties

  /**
   * Đường dẫn dưới dạng mảng
   */
  @ApiProperty({
    description: 'Đường dẫn dưới dạng mảng',
    example: ['chính-sách-nhân-sự'],
  })
  pathArray: string[];

  /**
   * Có phải thư mục gốc không
   */
  @ApiProperty({
    description: 'Có phải thư mục gốc không',
    example: true,
  })
  isRoot: boolean;
}

/**
 * DTO response cho thư mục với thông tin thống kê
 */
export class DocumentFolderWithStatsResponseDto extends DocumentFolderResponseDto {
  /**
   * Số lượng thư mục con
   */
  @ApiProperty({
    description: 'Số lượng thư mục con',
    example: 5,
  })
  childrenCount: number;

  /**
   * Số lượng tài liệu trong thư mục
   */
  @ApiProperty({
    description: 'Số lượng tài liệu trong thư mục',
    example: 12,
  })
  documentsCount: number;

  /**
   * Tổng dung lượng tài liệu (bytes)
   */
  @ApiProperty({
    description: 'Tổng dung lượng tài liệu (bytes)',
    example: 1048576,
  })
  totalSize: number;
}
