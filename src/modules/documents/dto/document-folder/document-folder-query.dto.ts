import {
  IsOptional,
  IsString,
  IsN<PERSON>ber,
  IsBoolean,
  IsIn,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO để truy vấn danh sách thư mục tài liệu
 */
export class DocumentFolderQueryDto {
  /**
   * Số trang
   */
  @ApiPropertyOptional({
    description: 'Số trang',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  /**
   * Số lượng items per page
   */
  @ApiPropertyOptional({
    description: 'Số lượng items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  /**
   * Từ khóa tìm kiếm
   */
  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm trong tên và mô tả thư mục',
    example: 'ch<PERSON>h sách',
  })
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * ID thư mục cha
   */
  @ApiPropertyOptional({
    description: 'ID thư mục cha (null để lấy thư mục gốc)',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  parentId?: number;

  /**
   * Trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isActive?: boolean;

  /**
   * Trường sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Trường để sắp xếp',
    example: 'name',
    enum: ['name', 'createdAt', 'updatedAt', 'sortOrder', 'level'],
    default: 'name',
  })
  @IsOptional()
  @IsString()
  @IsIn(['name', 'createdAt', 'updatedAt', 'sortOrder', 'level'])
  sortBy?: string = 'name';

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    example: 'ASC',
    enum: ['ASC', 'DESC'],
    default: 'ASC',
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortDirection?: 'ASC' | 'DESC' = 'ASC';
}
