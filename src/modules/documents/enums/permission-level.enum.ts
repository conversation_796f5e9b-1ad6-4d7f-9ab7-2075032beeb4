/**
 * Enum for permission levels in the document management system
 */
export enum PermissionLevel {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
}

/**
 * Human-readable labels for permission levels
 */
export const PermissionLevelLabels = {
  [PermissionLevel.READ]: 'Đọc',
  [PermissionLevel.WRITE]: 'Ghi',
  [PermissionLevel.ADMIN]: 'Quản trị',
};

/**
 * Permission level hierarchy (higher number = more permissions)
 */
export const PermissionLevelHierarchy = {
  [PermissionLevel.READ]: 1,
  [PermissionLevel.WRITE]: 2,
  [PermissionLevel.ADMIN]: 3,
};

/**
 * Check if a permission level includes another permission level
 */
export function hasPermission(
  userLevel: PermissionLevel,
  requiredLevel: PermissionLevel,
): boolean {
  return (
    PermissionLevelHierarchy[userLevel] >=
    PermissionLevelHierarchy[requiredLevel]
  );
}
