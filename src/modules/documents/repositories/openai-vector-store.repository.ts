import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OpenAIVectorStore } from '../entities/openai-vector-store.entity';

/**
 * Repository cho entity OpenAIVectorStore với tenant isolation
 */
@Injectable()
export class OpenAIVectorStoreRepository {
  private readonly logger = new Logger(OpenAIVectorStoreRepository.name);

  constructor(
    @InjectRepository(OpenAIVectorStore)
    private readonly repository: Repository<OpenAIVectorStore>,
  ) {}

  /**
   * Tạo vector store mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu vector store
   * @returns Vector store đã tạo
   */
  async create(
    tenantId: number,
    data: Partial<OpenAIVectorStore>,
  ): Promise<OpenAIVectorStore> {
    const vectorStore = this.repository.create({
      ...data,
      tenantId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return this.repository.save(vectorStore);
  }

  /**
   * Tìm vector store theo tenant
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Vector store hoặc null
   */
  async findByTenant(tenantId: number): Promise<OpenAIVectorStore | null> {
    return this.repository.findOne({
      where: { tenantId },
    });
  }

  /**
   * Tìm vector store theo vectorStoreId
   * @param vectorStoreId ID vector store từ OpenAI
   * @returns Vector store hoặc null
   */
  async findByVectorStoreId(
    vectorStoreId: string,
  ): Promise<OpenAIVectorStore | null> {
    return this.repository.findOne({
      where: { vectorStoreId },
    });
  }

  /**
   * Tìm tất cả vector stores
   * @param options Tùy chọn truy vấn
   * @returns Danh sách vector stores
   */
  async findAll(
    options: {
      status?: 'active' | 'expired' | 'deleting';
      limit?: number;
    } = {},
  ): Promise<OpenAIVectorStore[]> {
    const { status, limit } = options;

    const queryBuilder = this.repository.createQueryBuilder('vectorStore');

    // Áp dụng bộ lọc status nếu được cung cấp
    if (status) {
      queryBuilder.andWhere('vectorStore.status = :status', { status });
    }

    // Sắp xếp theo ngày tạo
    queryBuilder.orderBy('vectorStore.createdAt', 'DESC');

    // Giới hạn số lượng nếu được cung cấp
    if (limit) {
      queryBuilder.take(limit);
    }

    return queryBuilder.getMany();
  }

  /**
   * Cập nhật vector store
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu cập nhật
   * @returns Vector store đã cập nhật hoặc null
   */
  async updateByTenant(
    tenantId: number,
    data: Partial<OpenAIVectorStore>,
  ): Promise<OpenAIVectorStore | null> {
    const vectorStore = await this.findByTenant(tenantId);
    if (!vectorStore) return null;

    Object.assign(vectorStore, data, { updatedAt: Date.now() });
    return this.repository.save(vectorStore);
  }

  /**
   * Cập nhật vector store theo vectorStoreId
   * @param vectorStoreId ID vector store từ OpenAI
   * @param data Dữ liệu cập nhật
   * @returns Vector store đã cập nhật hoặc null
   */
  async updateByVectorStoreId(
    vectorStoreId: string,
    data: Partial<OpenAIVectorStore>,
  ): Promise<OpenAIVectorStore | null> {
    const vectorStore = await this.findByVectorStoreId(vectorStoreId);
    if (!vectorStore) return null;

    Object.assign(vectorStore, data, { updatedAt: Date.now() });
    return this.repository.save(vectorStore);
  }

  /**
   * Cập nhật thống kê sử dụng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param fileCount Số lượng file
   * @param usageBytes Dung lượng sử dụng
   * @returns Vector store đã cập nhật hoặc null
   */
  async updateUsageStats(
    tenantId: number,
    fileCount: number,
    usageBytes: number,
  ): Promise<OpenAIVectorStore | null> {
    return this.updateByTenant(tenantId, {
      fileCount,
      usageBytes,
      lastSyncedAt: Date.now(),
    });
  }

  /**
   * Đánh dấu vector store là expired
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Vector store đã cập nhật hoặc null
   */
  async markAsExpired(tenantId: number): Promise<OpenAIVectorStore | null> {
    return this.updateByTenant(tenantId, {
      status: 'expired',
    });
  }

  /**
   * Đánh dấu vector store đang xóa
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Vector store đã cập nhật hoặc null
   */
  async markAsDeleting(tenantId: number): Promise<OpenAIVectorStore | null> {
    return this.updateByTenant(tenantId, {
      status: 'deleting',
    });
  }

  /**
   * Xóa vector store
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number): Promise<boolean> {
    const result = await this.repository.delete({ tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Tìm vector stores sắp hết hạn
   * @param daysBeforeExpiration Số ngày trước khi hết hạn
   * @returns Danh sách vector stores sắp hết hạn
   */
  async findExpiringSoon(
    daysBeforeExpiration: number = 7,
  ): Promise<OpenAIVectorStore[]> {
    const expirationThreshold =
      Date.now() + daysBeforeExpiration * 24 * 60 * 60 * 1000;

    return this.repository
      .createQueryBuilder('vectorStore')
      .where('vectorStore.status = :status', { status: 'active' })
      .andWhere('vectorStore.expiresAt IS NOT NULL')
      .andWhere('vectorStore.expiresAt <= :threshold', {
        threshold: expirationThreshold,
      })
      .orderBy('vectorStore.expiresAt', 'ASC')
      .getMany();
  }

  /**
   * Tìm vector stores đã hết hạn
   * @returns Danh sách vector stores đã hết hạn
   */
  async findExpired(): Promise<OpenAIVectorStore[]> {
    const now = Date.now();

    return this.repository
      .createQueryBuilder('vectorStore')
      .where('vectorStore.status = :status', { status: 'active' })
      .andWhere('vectorStore.expiresAt IS NOT NULL')
      .andWhere('vectorStore.expiresAt <= :now', { now })
      .orderBy('vectorStore.expiresAt', 'ASC')
      .getMany();
  }

  /**
   * Kiểm tra vector store có tồn tại cho tenant không
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns True nếu tồn tại
   */
  async existsForTenant(tenantId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { tenantId },
    });
    return count > 0;
  }

  /**
   * Lấy thống kê tổng quan
   * @returns Thống kê vector stores
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    deleting: number;
    totalFiles: number;
    totalUsageBytes: number;
  }> {
    const [total, active, expired, deleting] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { status: 'active' } }),
      this.repository.count({ where: { status: 'expired' } }),
      this.repository.count({ where: { status: 'deleting' } }),
    ]);

    const result = await this.repository
      .createQueryBuilder('vectorStore')
      .select('SUM(vectorStore.fileCount)', 'totalFiles')
      .addSelect('SUM(vectorStore.usageBytes)', 'totalUsageBytes')
      .getRawOne();

    return {
      total,
      active,
      expired,
      deleting,
      totalFiles: parseInt(result.totalFiles) || 0,
      totalUsageBytes: parseInt(result.totalUsageBytes) || 0,
    };
  }
}
