import { DocumentType, PermissionLevel, ProcessingStatus } from '../enums';

/**
 * Interface for document metadata
 */
export interface DocumentMetadata {
  fileSize?: number;
  mimeType?: string;
  pageCount?: number;
  wordCount?: number;
  language?: string;
  author?: string;
  createdDate?: string;
  modifiedDate?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

/**
 * Interface for OpenAI integration data
 */
export interface OpenAIIntegration {
  fileId?: string;
  vectorStoreId?: string;
  assistantId?: string;
  processingStatus?: ProcessingStatus;
  processingError?: string;
  lastProcessedAt?: number;
  retryCount?: number;
}

/**
 * Interface for S3 storage data
 */
export interface S3StorageData {
  bucket?: string;
  key?: string;
  region?: string;
  etag?: string;
  versionId?: string;
}

/**
 * Interface for document permission data
 */
export interface DocumentPermissionData {
  userId?: number;
  roleId?: number;
  departmentId?: number;
  level: PermissionLevel;
  grantedBy: number;
  grantedAt: number;
  expiresAt?: number;
}
