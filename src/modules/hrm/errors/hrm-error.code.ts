import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Mã lỗi cho module HRM
 * Phạm vi: 13000-13099
 */
export const HRM_ERROR_CODES = {
  // Department errors (13000-13019)
  DEPARTMENT_NOT_FOUND: new ErrorCode(
    13000,
    'Không tìm thấy phòng ban',
    HttpStatus.NOT_FOUND,
  ),
  DEPARTMENT_NAME_ALREADY_EXISTS: new ErrorCode(
    13001,
    'Tên phòng ban đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_CIRCULAR_REFERENCE: new ErrorCode(
    13002,
    'Phát hiện tham chiếu vòng tròn trong cấu trúc phòng ban',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_HAS_CHILDREN: new ErrorCode(
    13003,
    'Phòng ban có chứa phòng ban con, không thể xóa',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_PARENT_NOT_FOUND: new ErrorCode(
    13004,
    'Phòng ban cấp trên không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  DEPARTMENT_MANAGER_NOT_FOUND: new ErrorCode(
    13005,
    'Người quản lý phòng ban không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  // Employee errors (13020-13039)
  EMPLOYEE_NOT_FOUND: new ErrorCode(
    13020,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  ),
  EMPLOYEE_CODE_EXISTS: new ErrorCode(
    13021,
    'Mã nhân viên đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_CREATE_FAILED: new ErrorCode(
    13022,
    'Tạo nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_UPDATE_FAILED: new ErrorCode(
    13023,
    'Cập nhật nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_DELETE_FAILED: new ErrorCode(
    13024,
    'Xóa nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_FIND_FAILED: new ErrorCode(
    13025,
    'Tìm kiếm nhân viên thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_SELF_MANAGER: new ErrorCode(
    13026,
    'Nhân viên không thể là quản lý của chính mình',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_TERMINATION_REASON_REQUIRED: new ErrorCode(
    13027,
    'Cần cung cấp lý do chấm dứt hợp đồng',
    HttpStatus.BAD_REQUEST,
  ),
  USERNAME_EXISTS: new ErrorCode(
    13028,
    'Tên đăng nhập đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  EMAIL_EXISTS: new ErrorCode(
    13029,
    'Địa chỉ email đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  USER_CREATION_FAILED: new ErrorCode(
    13030,
    'Tạo tài khoản người dùng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  USER_NOT_FOUND: new ErrorCode(
    13031,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
  ),
  PERMISSION_FETCH_FAILED: new ErrorCode(
    13032,
    'Không thể lấy danh sách quyền',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ROLE_NOT_FOUND: new ErrorCode(
    13033,
    'Không tìm thấy vai trò',
    HttpStatus.NOT_FOUND,
  ),
  PERMISSION_NOT_FOUND: new ErrorCode(
    13034,
    'Không tìm thấy quyền',
    HttpStatus.NOT_FOUND,
  ),
  ROLE_UPDATE_FAILED: new ErrorCode(
    13035,
    'Cập nhật vai trò thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  PERMISSION_UPDATE_FAILED: new ErrorCode(
    13036,
    'Cập nhật quyền thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ROLE_FETCH_FAILED: new ErrorCode(
    13037,
    'Không thể lấy danh sách vai trò',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ROLE_DELETE_FAILED: new ErrorCode(
    13038,
    'Xóa vai trò thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ROLE_IN_USE: new ErrorCode(
    13039,
    'Vai trò đang được sử dụng bởi người dùng, không thể xóa',
    HttpStatus.BAD_REQUEST,
  ),
  EMPLOYEE_ALREADY_HAS_USER: new ErrorCode(
    13040,
    'Nhân viên đã có tài khoản người dùng',
    HttpStatus.BAD_REQUEST,
  ),
  USER_ALREADY_HAS_EMPLOYEE: new ErrorCode(
    13041,
    'Người dùng đã gắn với nhân viên khác',
    HttpStatus.BAD_REQUEST,
  ),

  // Contract errors (13042-13061)
  CONTRACT_NOT_FOUND: new ErrorCode(
    13042,
    'Không tìm thấy hợp đồng',
    HttpStatus.NOT_FOUND,
  ),
  CONTRACT_CODE_EXISTS: new ErrorCode(
    13043,
    'Mã hợp đồng đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_CREATE_FAILED: new ErrorCode(
    13044,
    'Tạo hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_UPDATE_FAILED: new ErrorCode(
    13045,
    'Cập nhật hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_DELETE_FAILED: new ErrorCode(
    13046,
    'Xóa hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_FIND_FAILED: new ErrorCode(
    13047,
    'Tìm kiếm hợp đồng thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_ALREADY_TERMINATED: new ErrorCode(
    13048,
    'Hợp đồng đã bị chấm dứt',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_CANNOT_UPDATE_TERMINATED: new ErrorCode(
    13049,
    'Không thể cập nhật hợp đồng đã chấm dứt',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_INVALID_STATUS_CHANGE: new ErrorCode(
    13050,
    'Thay đổi trạng thái hợp đồng không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Attendance errors (13062-13081)
  ATTENDANCE_NOT_FOUND: new ErrorCode(
    13062,
    'Không tìm thấy bản ghi chấm công',
    HttpStatus.NOT_FOUND,
  ),
  ATTENDANCE_ALREADY_EXISTS: new ErrorCode(
    13063,
    'Bản ghi chấm công đã tồn tại cho ngày này',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_CREATE_FAILED: new ErrorCode(
    13064,
    'Tạo bản ghi chấm công thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_UPDATE_FAILED: new ErrorCode(
    13065,
    'Cập nhật bản ghi chấm công thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_DELETE_FAILED: new ErrorCode(
    13066,
    'Xóa bản ghi chấm công thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_FIND_FAILED: new ErrorCode(
    13067,
    'Tìm kiếm bản ghi chấm công thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_ALREADY_CHECKED_IN: new ErrorCode(
    13068,
    'Nhân viên đã check-in hôm nay',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_ALREADY_CHECKED_OUT: new ErrorCode(
    13069,
    'Nhân viên đã check-out hôm nay',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_NOT_CHECKED_IN: new ErrorCode(
    13070,
    'Nhân viên chưa check-in hôm nay',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_CHECKIN_FAILED: new ErrorCode(
    13071,
    'Check-in thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_CHECKOUT_FAILED: new ErrorCode(
    13072,
    'Check-out thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  ATTENDANCE_STATS_FAILED: new ErrorCode(
    13073,
    'Lấy thống kê chấm công thất bại',
    HttpStatus.BAD_REQUEST,
  ),
};
