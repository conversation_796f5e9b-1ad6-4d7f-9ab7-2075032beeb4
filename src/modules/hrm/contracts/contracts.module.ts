import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Contract } from './entities/contract.entity';
import { ContractRepository } from './repositories/contract.repository';
import { ContractService } from './services/contract.service';
import { ContractController } from './controllers/contract.controller';

/**
 * <PERSON><PERSON><PERSON> quản lý hợp đồng nhân sự
 */
@Global()
@Module({
  imports: [TypeOrmModule.forFeature([Contract])],
  controllers: [ContractController],
  providers: [ContractService, ContractRepository],
  exports: [ContractService, ContractRepository],
})
export class ContractsModule {}
