import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Patch,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { ContractService } from '../services/contract.service';
import { CreateContractDto } from '../dto/create-contract.dto';
import { UpdateContractDto } from '../dto/update-contract.dto';
import { ContractQueryDto } from '../dto/contract-query.dto';
import { ContractResponseDto } from '../dto/contract-response.dto';
import { UpdateContractStatusDto } from '../dto/update-contract-status.dto';
import { TerminateContractDto } from '../dto/terminate-contract.dto';
import { Contract } from '../entities/contract.entity';

/**
 * Controller for contract management
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(ApiResponseDto, ContractResponseDto)
@Controller('api/hrm/contracts')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  /**
   * Get all contracts with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all contracts' })
  @ApiResponse({
    status: 200,
    description: 'List of contracts',
    schema: ApiResponseDto.getPaginatedSchema(ContractResponseDto),
  })
  async findAll(
    @Query() query: ContractQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<Contract>>> {
    const contracts = await this.contractService.findAll(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.success(contracts);
  }

  /**
   * Get contract by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get contract by ID' })
  @ApiParam({ name: 'id', description: 'Contract ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Contract details',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract>> {
    const contract = await this.contractService.findById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(contract);
  }

  /**
   * Get contracts by employee ID
   */
  @Get('employee/:employeeId')
  @ApiOperation({ summary: 'Get contracts by employee ID' })
  @ApiParam({ name: 'employeeId', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'List of contracts for employee',
    schema: ApiResponseDto.getArraySchema(ContractResponseDto),
  })
  async findByEmployeeId(
    @Param('employeeId', ParseIntPipe) employeeId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract[]>> {
    const contracts = await this.contractService.findByEmployeeId(
      Number(user.tenantId),
      employeeId,
    );
    return ApiResponseDto.success(contracts);
  }

  /**
   * Get active contract by employee ID
   */
  @Get('employee/:employeeId/active')
  @ApiOperation({ summary: 'Get active contract by employee ID' })
  @ApiParam({ name: 'employeeId', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Active contract for employee',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async findActiveContractByEmployeeId(
    @Param('employeeId', ParseIntPipe) employeeId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract | null>> {
    const contract = await this.contractService.findActiveContractByEmployeeId(
      Number(user.tenantId),
      employeeId,
    );
    return ApiResponseDto.success(contract);
  }

  /**
   * Get contracts expiring soon
   */
  @Get('expiring-soon')
  @ApiOperation({ summary: 'Get contracts expiring soon' })
  @ApiQuery({
    name: 'daysThreshold',
    description: 'Number of days to consider "soon"',
    type: 'number',
    required: false,
    example: 30,
  })
  @ApiResponse({
    status: 200,
    description: 'List of contracts expiring soon',
    schema: ApiResponseDto.getArraySchema(ContractResponseDto),
  })
  async findContractsExpiringSoon(
    @Query('daysThreshold') daysThreshold: number = 30,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract[]>> {
    const contracts = await this.contractService.findContractsExpiringSoon(
      Number(user.tenantId),
      daysThreshold,
    );
    return ApiResponseDto.success(contracts);
  }

  /**
   * Create a new contract
   */
  @Post()
  @ApiOperation({ summary: 'Create a new contract' })
  @ApiResponse({
    status: 201,
    description: 'Contract created successfully',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async create(
    @Body() createContractDto: CreateContractDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract>> {
    const contract = await this.contractService.create(
      Number(user.tenantId),
      createContractDto,
      user.id,
    );
    return ApiResponseDto.created(contract);
  }

  /**
   * Update contract
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update contract' })
  @ApiParam({ name: 'id', description: 'Contract ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Contract updated successfully',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateContractDto: UpdateContractDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract>> {
    const contract = await this.contractService.update(
      Number(user.tenantId),
      id,
      updateContractDto,
      user.id,
    );
    return ApiResponseDto.success(contract);
  }

  /**
   * Update contract status
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Update contract status' })
  @ApiParam({ name: 'id', description: 'Contract ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Contract status updated successfully',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateContractStatusDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract>> {
    const contract = await this.contractService.updateStatus(
      Number(user.tenantId),
      id,
      updateStatusDto,
      user.id,
    );
    return ApiResponseDto.success(contract);
  }

  /**
   * Terminate contract
   */
  @Patch(':id/terminate')
  @ApiOperation({ summary: 'Terminate contract' })
  @ApiParam({ name: 'id', description: 'Contract ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Contract terminated successfully',
    schema: ApiResponseDto.getSchema(ContractResponseDto),
  })
  async terminateContract(
    @Param('id', ParseIntPipe) id: number,
    @Body() terminateContractDto: TerminateContractDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Contract>> {
    const contract = await this.contractService.terminateContract(
      Number(user.tenantId),
      id,
      terminateContractDto,
      user.id,
    );
    return ApiResponseDto.success(contract);
  }

  /**
   * Delete contract
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete contract' })
  @ApiParam({ name: 'id', description: 'Contract ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Contract deleted successfully',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.contractService.delete(Number(user.tenantId), id);
    return ApiResponseDto.success(true);
  }
}
