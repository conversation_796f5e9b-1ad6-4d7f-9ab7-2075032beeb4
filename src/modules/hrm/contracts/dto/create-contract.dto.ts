import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsInt,
  IsOptional,
  IsEnum,
  IsDate,
  MaxLength,
  IsNumber,
  Min,
  IsPositive,
  IsDecimal,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * DTO for creating a new contract
 */
export class CreateContractDto {
  /**
   * Contract code (unique identifier)
   * @example "CT-2023-001"
   */
  @ApiProperty({
    description: 'Contract code (unique identifier)',
    example: 'CT-2023-001',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  contractCode: string;

  /**
   * ID of the employee associated with this contract
   * @example 1
   */
  @ApiProperty({
    description: 'ID of the employee associated with this contract',
    example: 1,
  })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  employeeId: number;

  /**
   * Contract type
   * @example "definite"
   */
  @ApiProperty({
    description: 'Contract type',
    enum: ContractType,
    example: ContractType.DEFINITE,
  })
  @IsNotEmpty()
  @IsEnum(ContractType)
  contractType: ContractType;

  /**
   * Contract title
   * @example "Employment Contract"
   */
  @ApiProperty({
    description: 'Contract title',
    example: 'Employment Contract',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  /**
   * Contract description
   * @example "Standard employment contract for software engineers"
   */
  @ApiProperty({
    required: false,
    description: 'Contract description',
    example: 'Standard employment contract for software engineers',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Contract start date
   * @example "2023-01-15"
   */
  @ApiProperty({ description: 'Contract start date', example: '2023-01-15' })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  startDate: Date;

  /**
   * Contract end date (null for indefinite contracts)
   * @example "2024-01-14"
   */
  @ApiProperty({
    required: false,
    description: 'Contract end date (null for indefinite contracts)',
    example: '2024-01-14',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  /**
   * Contract signing date
   * @example "2023-01-10"
   */
  @ApiProperty({
    required: false,
    description: 'Contract signing date',
    example: '2023-01-10',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  signingDate?: Date;

  /**
   * Contract status
   * @example "draft"
   */
  @ApiProperty({
    required: false,
    description: 'Contract status',
    enum: ContractStatus,
    default: ContractStatus.DRAFT,
    example: ContractStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(ContractStatus)
  status?: ContractStatus;

  /**
   * Base salary amount
   * @example 10000000
   */
  @ApiProperty({ description: 'Base salary amount', example: 10000000 })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  baseSalary: number;

  /**
   * Salary currency
   * @example "VND"
   */
  @ApiProperty({
    required: false,
    description: 'Salary currency',
    example: 'VND',
    default: 'VND',
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  currency?: string;

  /**
   * Working hours per week
   * @example 40
   */
  @ApiProperty({
    required: false,
    description: 'Working hours per week',
    example: 40,
  })
  @IsOptional()
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  workingHoursPerWeek?: number;

  /**
   * Probation period in days (if applicable)
   * @example 60
   */
  @ApiProperty({
    required: false,
    description: 'Probation period in days (if applicable)',
    example: 60,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  probationPeriodDays?: number;

  /**
   * Notice period in days
   * @example 30
   */
  @ApiProperty({
    required: false,
    description: 'Notice period in days',
    example: 30,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  noticePeriodDays?: number;

  /**
   * Path to contract document file
   * @example "contracts/2023/CT-2023-001.pdf"
   */
  @ApiProperty({
    required: false,
    description: 'Path to contract document file',
    example: 'contracts/2023/CT-2023-001.pdf',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  documentPath?: string;

  /**
   * Notes about the contract
   * @example "Contract includes special clauses for remote work"
   */
  @ApiProperty({
    required: false,
    description: 'Notes about the contract',
    example: 'Contract includes special clauses for remote work',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
