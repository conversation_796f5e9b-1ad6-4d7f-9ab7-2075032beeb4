import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsDate, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for terminating a contract
 */
export class TerminateContractDto {
  /**
   * Termination date
   * @example "2023-06-30"
   */
  @ApiProperty({ description: 'Termination date', example: '2023-06-30' })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  terminationDate: Date;

  /**
   * Reason for termination
   * @example "Employee resigned for personal reasons"
   */
  @ApiProperty({
    description: 'Reason for termination',
    example: 'Employee resigned for personal reasons',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(500)
  terminationReason: string;
}
