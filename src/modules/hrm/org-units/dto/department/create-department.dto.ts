import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  MaxLength,
} from 'class-validator';

/**
 * DTO for creating a new department
 */
export class CreateDepartmentDto {
  /**
   * Name of the department
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Phòng Kỹ thuật',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  /**
   * Description of the department
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về phòng ban',
    example: 'Phòng ban phụ trách các vấn đề kỹ thuật của công ty',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID of the department manager
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  managerId?: number;

  /**
   * ID of the parent department
   */
  @ApiProperty({
    description: 'ID của phòng ban cấp trên (nếu có)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  parentId?: number;
}
