import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing employee performance reviews
 */
@Entity('performance_reviews')
export class PerformanceReview {
  /**
   * Unique identifier for the performance review
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID of the employee being reviewed
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * ID of the reviewer (manager/supervisor)
   */
  @Column({ name: 'reviewer_id', type: 'integer', nullable: false })
  reviewerId: number;

  /**
   * Review period start date
   */
  @Column({ name: 'period_start', type: 'date', nullable: false })
  periodStart: Date;

  /**
   * Review period end date
   */
  @Column({ name: 'period_end', type: 'date', nullable: false })
  periodEnd: Date;

  /**
   * Review title/name
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * Review description
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Overall performance rating (1-5 scale)
   */
  @Column({
    name: 'overall_rating',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  overallRating: number | null;

  /**
   * Technical skills rating (1-5 scale)
   */
  @Column({
    name: 'technical_rating',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  technicalRating: number | null;

  /**
   * Communication skills rating (1-5 scale)
   */
  @Column({
    name: 'communication_rating',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  communicationRating: number | null;

  /**
   * Leadership skills rating (1-5 scale)
   */
  @Column({
    name: 'leadership_rating',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  leadershipRating: number | null;

  /**
   * Teamwork rating (1-5 scale)
   */
  @Column({
    name: 'teamwork_rating',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  teamworkRating: number | null;

  /**
   * Goals achieved during review period
   */
  @Column({ name: 'goals_achieved', type: 'text', nullable: true })
  goalsAchieved: string | null;

  /**
   * Areas for improvement
   */
  @Column({ name: 'areas_for_improvement', type: 'text', nullable: true })
  areasForImprovement: string | null;

  /**
   * Goals for next period
   */
  @Column({ name: 'next_period_goals', type: 'text', nullable: true })
  nextPeriodGoals: string | null;

  /**
   * Reviewer comments
   */
  @Column({ name: 'reviewer_comments', type: 'text', nullable: true })
  reviewerComments: string | null;

  /**
   * Employee self-assessment comments
   */
  @Column({ name: 'employee_comments', type: 'text', nullable: true })
  employeeComments: string | null;

  /**
   * Review status (draft, submitted, approved, completed)
   */
  @Column({ type: 'varchar', length: 50, nullable: false, default: 'draft' })
  status: string;

  /**
   * Date when review was submitted
   */
  @Column({ name: 'submitted_at', type: 'bigint', nullable: true })
  submittedAt: number | null;

  /**
   * Date when review was approved
   */
  @Column({ name: 'approved_at', type: 'bigint', nullable: true })
  approvedAt: number | null;

  /**
   * ID of the approver
   */
  @Column({ name: 'approved_by', type: 'integer', nullable: true })
  approvedBy: number | null;

  /**
   * Due date for review completion
   */
  @Column({ name: 'due_date', type: 'date', nullable: true })
  dueDate: Date | null;

  /**
   * Whether this is a self-review
   */
  @Column({ name: 'is_self_review', type: 'boolean', default: false })
  isSelfReview: boolean;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID of the user who created this record
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID of the user who last updated this record
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: false })
  updatedBy: number;

  /**
   * Tenant ID for multi-tenancy support
   * REQUIRED for tenant isolation
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;
}
