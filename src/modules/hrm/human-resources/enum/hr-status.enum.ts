/**
 * Enum for performance review status
 */
export enum PerformanceReviewStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
}

/**
 * Enum for training status
 */
export enum TrainingStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended',
}

/**
 * Enum for training type
 */
export enum TrainingType {
  ONLINE = 'online',
  CLASSROOM = 'classroom',
  WORKSHOP = 'workshop',
  SEMINAR = 'seminar',
  WEBINAR = 'webinar',
  CONFERENCE = 'conference',
  ON_THE_JOB = 'on_the_job',
}

/**
 * Enum for training category
 */
export enum TrainingCategory {
  TECHNICAL = 'technical',
  SOFT_SKILLS = 'soft_skills',
  COMPLIANCE = 'compliance',
  LEADERSHIP = 'leadership',
  SAFETY = 'safety',
  ORIENTATION = 'orientation',
  PROFESSIONAL_DEVELOPMENT = 'professional_development',
}

/**
 * Enum for employee training status
 */
export enum EmployeeTrainingStatus {
  ENROLLED = 'enrolled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  WITHDRAWN = 'withdrawn',
}

/**
 * Enum for benefit category
 */
export enum BenefitCategory {
  HEALTH = 'health',
  INSURANCE = 'insurance',
  RETIREMENT = 'retirement',
  WELLNESS = 'wellness',
  TIME_OFF = 'time_off',
  FINANCIAL = 'financial',
  EDUCATION = 'education',
  TRANSPORTATION = 'transportation',
}

/**
 * Enum for benefit type
 */
export enum BenefitType {
  MEDICAL = 'medical',
  DENTAL = 'dental',
  VISION = 'vision',
  LIFE_INSURANCE = 'life_insurance',
  DISABILITY_INSURANCE = 'disability_insurance',
  RETIREMENT_401K = 'retirement_401k',
  PENSION = 'pension',
  HEALTH_SAVINGS_ACCOUNT = 'health_savings_account',
  FLEXIBLE_SPENDING_ACCOUNT = 'flexible_spending_account',
  PAID_TIME_OFF = 'paid_time_off',
  SICK_LEAVE = 'sick_leave',
  MATERNITY_LEAVE = 'maternity_leave',
  PATERNITY_LEAVE = 'paternity_leave',
  TUITION_REIMBURSEMENT = 'tuition_reimbursement',
  GYM_MEMBERSHIP = 'gym_membership',
  COMMUTER_BENEFITS = 'commuter_benefits',
}

/**
 * Enum for benefit status
 */
export enum BenefitStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired',
}

/**
 * Enum for employee benefit enrollment status
 */
export enum EmployeeBenefitStatus {
  ENROLLED = 'enrolled',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired',
}

/**
 * Enum for benefit coverage level
 */
export enum BenefitCoverageLevel {
  INDIVIDUAL = 'individual',
  EMPLOYEE_SPOUSE = 'employee_spouse',
  EMPLOYEE_CHILDREN = 'employee_children',
  FAMILY = 'family',
}

/**
 * Helper functions to get all enum values
 */
export function getAllPerformanceReviewStatuses(): string[] {
  return Object.values(PerformanceReviewStatus);
}

export function getAllTrainingStatuses(): string[] {
  return Object.values(TrainingStatus);
}

export function getAllTrainingTypes(): string[] {
  return Object.values(TrainingType);
}

export function getAllTrainingCategories(): string[] {
  return Object.values(TrainingCategory);
}

export function getAllEmployeeTrainingStatuses(): string[] {
  return Object.values(EmployeeTrainingStatus);
}

export function getAllBenefitCategories(): string[] {
  return Object.values(BenefitCategory);
}

export function getAllBenefitTypes(): string[] {
  return Object.values(BenefitType);
}

export function getAllBenefitStatuses(): string[] {
  return Object.values(BenefitStatus);
}

export function getAllEmployeeBenefitStatuses(): string[] {
  return Object.values(EmployeeBenefitStatus);
}

export function getAllBenefitCoverageLevels(): string[] {
  return Object.values(BenefitCoverageLevel);
}

/**
 * Helper functions to validate enum values
 */
export function isValidPerformanceReviewStatus(status: string): boolean {
  return Object.values(PerformanceReviewStatus).includes(
    status as PerformanceReviewStatus,
  );
}

export function isValidTrainingStatus(status: string): boolean {
  return Object.values(TrainingStatus).includes(status as TrainingStatus);
}

export function isValidTrainingType(type: string): boolean {
  return Object.values(TrainingType).includes(type as TrainingType);
}

export function isValidTrainingCategory(category: string): boolean {
  return Object.values(TrainingCategory).includes(category as TrainingCategory);
}

export function isValidEmployeeTrainingStatus(status: string): boolean {
  return Object.values(EmployeeTrainingStatus).includes(
    status as EmployeeTrainingStatus,
  );
}

export function isValidBenefitCategory(category: string): boolean {
  return Object.values(BenefitCategory).includes(category as BenefitCategory);
}

export function isValidBenefitType(type: string): boolean {
  return Object.values(BenefitType).includes(type as BenefitType);
}

export function isValidBenefitStatus(status: string): boolean {
  return Object.values(BenefitStatus).includes(status as BenefitStatus);
}

export function isValidEmployeeBenefitStatus(status: string): boolean {
  return Object.values(EmployeeBenefitStatus).includes(
    status as EmployeeBenefitStatus,
  );
}

export function isValidBenefitCoverageLevel(level: string): boolean {
  return Object.values(BenefitCoverageLevel).includes(
    level as BenefitCoverageLevel,
  );
}
