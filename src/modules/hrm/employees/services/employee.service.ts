import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '../repositories/employee.repository';
import { Employee } from '../entities/employee.entity';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { UpdateEmployeeStatusDto } from '../dto/update-employee-status.dto';
import { BulkDeleteEmployeeDto } from '../dto/bulk-delete-employee.dto';
import { BulkDeleteEmployeeResponseDto } from '../dto/bulk-delete-employee-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { DepartmentService } from '../../org-units/services/department.service';
import { EmployeeWithDepartmentResponseDto, DepartmentInfoDto } from '../dto/employee-with-department-response.dto';
import { generateNextEmployeeCode } from '@/shared/utils/generators/employee-code-generator.util';
import { EmployeeOverviewResponseDto } from '../dto/employee-overview-response.dto';
import { UserRepository } from '@/modules/auth/repositories/user.repository';

/**
 * Service for employee management
 */
@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly departmentService: DepartmentService,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Find all employees with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of employees
   */
  async findAll(
    tenantId: number,
    query: EmployeeQueryDto,
  ): Promise<PaginatedResult<Employee>> {
    try {
      return await this.employeeRepository.findAll(tenantId, query);
    } catch (error) {
      this.logger.error(
        `Error finding employees: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to find employees: ${error.message}`,
      );
    }
  }

  /**
   * Find all employees with department information
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of employees with department information
   */
  async findAllWithDepartment(
    tenantId: number,
    query: EmployeeQueryDto,
  ): Promise<PaginatedResult<EmployeeWithDepartmentResponseDto>> {
    try {
      // Lấy danh sách employees
      const employeesResult = await this.employeeRepository.findAll(tenantId, query);

      // Lấy danh sách unique department IDs
      const departmentIds = [
        ...new Set(
          employeesResult.items
            .map(emp => emp.departmentId)
            .filter(id => id !== null)
        )
      ] as number[];

      // Lấy thông tin departments
      const departmentMap = new Map<number, DepartmentInfoDto>();
      if (departmentIds.length > 0) {
        for (const departmentId of departmentIds) {
          try {
            const department = await this.departmentService.findById(tenantId, departmentId);
            departmentMap.set(departmentId, {
              id: department.id,
              name: department.name,
              description: department.description,
              managerId: department.managerId,
              parentId: department.parentId,
            });
          } catch (error) {
            // Nếu không tìm thấy department, bỏ qua
            this.logger.warn(`Department with ID ${departmentId} not found: ${error.message}`);
          }
        }
      }

      // Map employees sang EmployeeWithDepartmentResponseDto
      const items: EmployeeWithDepartmentResponseDto[] = employeesResult.items.map(employee => ({
        id: employee.id,
        employeeCode: employee.employeeCode,
        employeeName: employee.employeeName,
        dateOfBirth: employee.dateOfBirth,
        gender: employee.gender,
        departmentId: employee.departmentId,
        department: employee.departmentId ? departmentMap.get(employee.departmentId) || null : null,
        jobTitle: employee.jobTitle,
        jobLevel: employee.jobLevel,
        managerId: employee.managerId,
        employmentType: employee.employmentType,
        status: employee.status,
        hireDate: employee.hireDate,
        email: null, // Employee entity không có trường email
        phoneNumber: null, // Employee entity không có trường phoneNumber
        address: null, // Employee entity không có trường address
        createdAt: employee.createdAt,
        updatedAt: employee.updatedAt,
      }));

      return {
        items,
        meta: employeesResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding employees with department: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to find employees with department: ${error.message}`,
      );
    }
  }

  /**
   * Find employee by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns Employee
   * @throws AppException if employee not found
   */
  async findById(tenantId: number, id: number): Promise<Employee> {
    try {
      const employee = await this.employeeRepository.findById(tenantId, id);
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Employee with ID ${id} not found`,
        );
      }
      return employee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding employee by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to find employee: ${error.message}`,
      );
    }
  }

  /**
   * Create a new employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createEmployeeDto Employee data
   * @param userId ID of the user creating the employee
   * @returns Created employee
   * @throws AppException if employee code generation fails
   */
  async create(
    tenantId: number,
    createEmployeeDto: CreateEmployeeDto,
    userId: number,
  ): Promise<Employee> {
    try {
      // Tìm mã nhân viên lớn nhất hiện có và tạo mã mới tăng dần
      const maxEmployeeNumber = await this.employeeRepository.findMaxEmployeeCodeNumber(tenantId);
      const employeeCode = generateNextEmployeeCode(maxEmployeeNumber);

      // Tạo nhân viên với mã đã generate
      const now = Date.now();
      const employee = await this.employeeRepository.create(tenantId, {
        ...createEmployeeDto,
        employeeCode, // Sử dụng mã nhân viên đã tự động tạo
        status: createEmployeeDto.status || EmployeeStatus.ACTIVE,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
      });

      this.logger.log(`Đã tạo nhân viên thành công với mã: ${employeeCode}`);
      return employee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating employee: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_CREATE_FAILED,
        `Failed to create employee: ${error.message}`,
      );
    }
  }

  /**
   * Update employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param updateEmployeeDto Updated employee data
   * @param userId ID of the user updating the employee
   * @returns Updated employee
   * @throws AppException if employee not found
   */
  async update(
    tenantId: number,
    id: number,
    updateEmployeeDto: UpdateEmployeeDto,
    userId: number,
  ): Promise<Employee> {
    try {
      // Check if employee exists
      const employee = await this.findById(tenantId, id);

      // Update employee
      const now = Date.now();
      const updatedEmployee = await this.employeeRepository.update(
        tenantId,
        id,
        {
          ...updateEmployeeDto,
          updatedAt: now,
          updatedBy: userId,
        },
      );

      if (!updatedEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
          `Failed to update employee with ID ${id}`,
        );
      }

      return updatedEmployee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating employee: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Failed to update employee: ${error.message}`,
      );
    }
  }

  /**
   * Update employee status
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param updateStatusDto Status update data
   * @param userId ID of the user updating the status
   * @returns Updated employee
   * @throws AppException if employee not found
   */
  async updateStatus(
    tenantId: number,
    id: number,
    updateStatusDto: UpdateEmployeeStatusDto,
    userId: number,
  ): Promise<Employee> {
    try {
      // Check if employee exists
      const employee = await this.findById(tenantId, id);

      // Handle termination
      if (updateStatusDto.status === EmployeeStatus.TERMINATED) {
        if (!updateStatusDto.reason) {
          throw new AppException(
            HRM_ERROR_CODES.EMPLOYEE_TERMINATION_REASON_REQUIRED,
            'Termination reason is required',
          );
        }

        // Update employee with termination details
        const now = Date.now();
        const terminationDate = new Date();
        const updatedEmployee = await this.employeeRepository.update(
          tenantId,
          id,
          {
            status: EmployeeStatus.TERMINATED,
            terminationDate,
            terminationReason: updateStatusDto.reason,
            updatedAt: now,
            updatedBy: userId,
          },
        );

        if (!updatedEmployee) {
          throw new AppException(
            HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
            `Failed to update employee status with ID ${id}`,
          );
        }

        return updatedEmployee;
      }

      // Update status for other cases
      const now = Date.now();
      const updatedEmployee = await this.employeeRepository.update(
        tenantId,
        id,
        {
          status: updateStatusDto.status,
          notes: employee.notes
            ? `${employee.notes}\n${new Date().toISOString()}: Status changed to ${updateStatusDto.status}${updateStatusDto.reason ? ` - Reason: ${updateStatusDto.reason}` : ''}`
            : `${new Date().toISOString()}: Status changed to ${updateStatusDto.status}${updateStatusDto.reason ? ` - Reason: ${updateStatusDto.reason}` : ''}`,
          updatedAt: now,
          updatedBy: userId,
        },
      );

      if (!updatedEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
          `Failed to update employee status with ID ${id}`,
        );
      }

      return updatedEmployee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating employee status: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Failed to update employee status: ${error.message}`,
      );
    }
  }

  /**
   * Assign employee to department
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param departmentId Department ID
   * @param userId ID of the user making the assignment
   * @returns Updated employee
   * @throws AppException if employee not found
   */
  async assignToDepartment(
    tenantId: number,
    id: number,
    departmentId: number,
    userId: number,
  ): Promise<Employee> {
    try {
      // Check if employee exists
      const employee = await this.findById(tenantId, id);

      // Update employee
      const now = Date.now();
      const updatedEmployee = await this.employeeRepository.update(
        tenantId,
        id,
        {
          departmentId,
          updatedAt: now,
          updatedBy: userId,
        },
      );

      if (!updatedEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
          `Failed to assign employee with ID ${id} to department`,
        );
      }

      return updatedEmployee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error assigning employee to department: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Failed to assign employee to department: ${error.message}`,
      );
    }
  }

  /**
   * Assign manager to employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param managerId Manager ID
   * @param userId ID of the user making the assignment
   * @returns Updated employee
   * @throws AppException if employee not found or if trying to assign self as manager
   */
  async assignManager(
    tenantId: number,
    id: number,
    managerId: number,
    userId: number,
  ): Promise<Employee> {
    try {
      // Check if employee exists
      const employee = await this.findById(tenantId, id);

      // Check if trying to assign self as manager
      if (id === managerId) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_SELF_MANAGER,
          'Employee cannot be their own manager',
        );
      }

      // Update employee
      const now = Date.now();
      const updatedEmployee = await this.employeeRepository.update(
        tenantId,
        id,
        {
          managerId,
          updatedAt: now,
          updatedBy: userId,
        },
      );

      if (!updatedEmployee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
          `Failed to assign manager to employee with ID ${id}`,
        );
      }

      return updatedEmployee;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error assigning manager to employee: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Failed to assign manager to employee: ${error.message}`,
      );
    }
  }

  /**
   * Delete employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns True if deleted successfully
   * @throws AppException if employee not found or delete fails
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    try {
      // Check if employee exists
      await this.findById(tenantId, id);

      // Delete employee
      const deleted = await this.employeeRepository.delete(tenantId, id);
      if (!deleted) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_DELETE_FAILED,
          `Failed to delete employee with ID ${id}`,
        );
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error deleting employee: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_DELETE_FAILED,
        `Failed to delete employee: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto DTO chứa danh sách ID nhân viên cần xóa
   * @returns Kết quả xóa nhiều nhân viên
   */
  async bulkDelete(
    tenantId: number,
    dto: BulkDeleteEmployeeDto,
  ): Promise<BulkDeleteEmployeeResponseDto> {
    const { ids } = dto;

    // Tìm tất cả nhân viên theo danh sách ID
    const employees = await this.employeeRepository.findByIds(tenantId, ids);
    const foundIds = employees.map((emp) => emp.id);
    const notFoundIds = ids.filter((id) => !foundIds.includes(id));

    const result: BulkDeleteEmployeeResponseDto = {
      totalRequested: ids.length,
      successCount: 0,
      failureCount: 0,
      deletedIds: [],
      failures: [],
    };

    // Thêm lỗi cho các nhân viên không tồn tại
    notFoundIds.forEach((id) => {
      result.failures.push({
        id,
        reason: 'Nhân viên không tồn tại',
      });
    });

    // Kiểm tra các nhân viên có thể xóa được không
    const deletableIds: number[] = [];

    for (const employee of employees) {
      try {
        // Kiểm tra xem nhân viên có đang quản lý nhân viên khác không
        const subordinates = await this.employeeRepository.findByManagerId(
          tenantId,
          employee.id,
        );

        if (subordinates.length > 0) {
          result.failures.push({
            id: employee.id,
            reason: `Nhân viên đang quản lý ${subordinates.length} nhân viên khác. Vui lòng chuyển nhượng quyền quản lý trước khi xóa.`,
          });
        } else {
          deletableIds.push(employee.id);
        }
      } catch (error) {
        result.failures.push({
          id: employee.id,
          reason: `Lỗi khi kiểm tra: ${error.message}`,
        });
      }
    }

    // Thực hiện xóa các nhân viên có thể xóa được
    if (deletableIds.length > 0) {
      try {
        const deletedCount = await this.employeeRepository.bulkDelete(
          tenantId,
          deletableIds,
        );
        result.successCount = deletedCount;
        result.deletedIds = deletableIds.slice(0, deletedCount);

        this.logger.log(
          `Bulk delete employees: ${deletedCount} nhân viên đã được xóa`,
        );
      } catch (error) {
        this.logger.error(
          `Error in bulk delete employees: ${error.message}`,
          error.stack,
        );

        // Nếu có lỗi khi xóa, thêm tất cả vào failures
        deletableIds.forEach((id) => {
          result.failures.push({
            id,
            reason: `Lỗi khi xóa: ${error.message}`,
          });
        });
      }
    }

    result.failureCount = result.failures.length;

    return result;
  }

  /**
   * Tìm kiếm nhân viên theo tên cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param name Tên cần tìm kiếm
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách nhân viên
   */
  async searchByName(
    tenantId: number,
    name: string,
    options: {
      limit?: number;
      departmentId?: number;
    } = {},
  ): Promise<Employee[]> {
    try {
      const query: EmployeeQueryDto = {
        page: 1,
        limit: options.limit || 10,
        search: name,
        departmentId: options.departmentId,
      };

      const result = await this.findAll(tenantId, query);
      return result.items;
    } catch (error) {
      this.logger.error(
        `Error searching employees by name: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to search employees by name: ${error.message}`,
      );
    }
  }

  /**
   * Tìm nhân viên theo userId cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @returns Nhân viên
   */
  async findByUserId(tenantId: number, userId: number): Promise<Employee | null> {
    try {
      // TODO: Cần tích hợp với bảng users để tìm employee theo userId
      // Hiện tại sử dụng search để tìm kiếm tạm thời
      const query: EmployeeQueryDto = {
        page: 1,
        limit: 100,
        search: `user_${userId}`, // Tạm thời search theo pattern
      };

      const result = await this.findAll(tenantId, query);
      // Tìm employee có liên kết với userId (cần implement logic này)
      return result.items.length > 0 ? result.items[0] : null;
    } catch (error) {
      this.logger.error(
        `Error finding employee by userId: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to find employee by userId: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách nhân viên đi muộn cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param _date Ngày kiểm tra (timestamp) - chưa sử dụng
   * @param options Tùy chọn
   * @returns Danh sách nhân viên đi muộn
   */
  async getLateEmployees(
    tenantId: number,
    _date: number,
    options: {
      departmentId?: number;
      limit?: number;
    } = {},
  ): Promise<Employee[]> {
    try {
      // TODO: Tích hợp với hệ thống chấm công để lấy danh sách nhân viên đi muộn
      // Hiện tại trả về danh sách rỗng vì chưa có module chấm công
      this.logger.warn('getLateEmployees: Attendance module not implemented yet');

      // Tạm thời trả về danh sách nhân viên active để demo
      const query: EmployeeQueryDto = {
        page: 1,
        limit: options.limit || 10,
        departmentId: options.departmentId,
        status: EmployeeStatus.ACTIVE,
      };

      const result = await this.findAll(tenantId, query);
      return result.items.slice(0, Math.floor(Math.random() * 3)); // Random để demo
    } catch (error) {
      this.logger.error(
        `Error getting late employees: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to get late employees: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách nhân viên cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn lọc
   * @returns Danh sách nhân viên
   */
  async getEmployeeList(
    tenantId: number,
    options: {
      departmentId?: number;
      position?: string;
      status?: string;
      limit?: number;
    } = {},
  ): Promise<Employee[]> {
    try {
      const query: EmployeeQueryDto = {
        page: 1,
        limit: options.limit || 20,
        departmentId: options.departmentId,
        // Note: position không có trong EmployeeQueryDto, sử dụng search thay thế
        search: options.position,
        status: options.status as EmployeeStatus,
      };

      const result = await this.findAll(tenantId, query);
      return result.items;
    } catch (error) {
      this.logger.error(
        `Error getting employee list: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to get employee list: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thống kê tổng quan nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Thống kê tổng quan nhân viên
   */
  async getOverview(tenantId: number): Promise<EmployeeOverviewResponseDto> {
    try {
      // Lấy thống kê nhân viên từ repository
      const employeeStats = await this.employeeRepository.getEmployeeOverviewStats(tenantId);

      // Lấy số lượng tài khoản người dùng có liên kết với nhân viên
      const totalUserAccounts = await this.userRepository.countUsersWithEmployee(tenantId);

      // Tính toán phần trăm
      const activeEmployeePercentage = employeeStats.totalEmployees > 0
        ? (employeeStats.activeEmployees / employeeStats.totalEmployees) * 100
        : 0;

      const accountCoveragePercentage = employeeStats.totalEmployees > 0
        ? (totalUserAccounts / employeeStats.totalEmployees) * 100
        : 0;

      const overview: EmployeeOverviewResponseDto = {
        totalEmployees: employeeStats.totalEmployees,
        totalUserAccounts,
        activeEmployees: employeeStats.activeEmployees,
        inactiveEmployees: employeeStats.inactiveEmployees,
        newEmployeesThisMonth: employeeStats.newEmployeesThisMonth,
        probationEmployees: employeeStats.probationEmployees,
        activeEmployeePercentage: Math.round(activeEmployeePercentage * 100) / 100, // Làm tròn 2 chữ số thập phân
        accountCoveragePercentage: Math.round(accountCoveragePercentage * 100) / 100,
      };

      this.logger.log(`Employee overview stats retrieved for tenant ${tenantId}: ${JSON.stringify(overview)}`);
      return overview;
    } catch (error) {
      this.logger.error(
        `Error getting employee overview: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_FIND_FAILED,
        `Failed to get employee overview: ${error.message}`,
      );
    }
  }
}
