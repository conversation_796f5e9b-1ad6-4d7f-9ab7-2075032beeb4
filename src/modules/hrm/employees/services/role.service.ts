import { Injectable, Logger } from '@nestjs/common';
import { RoleRepository } from '../repositories/role.repository';
import {
  RoleQueryDto,
  RoleListItemDto,
  RoleDetailDto,
} from '../dto/role/role-list.dto';
import {
  UserRolesResponseDto,
  UserRoleItemDto,
} from '../dto/role/user-roles.dto';
import { BulkDeleteRoleDto } from '../dto/role/bulk-delete-role.dto';
import {
  BulkDeleteRoleResponseDto,
  RoleDeleteFailureDto,
} from '../dto/role/bulk-delete-role-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { plainToInstance } from 'class-transformer';

/**
 * Service quản lý vai trò
 */
@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);

  constructor(private readonly roleRepository: RoleRepository) {}

  /**
   * Lấy danh sách vai trò với phân trang và tìm kiếm
   * @param query Tham số truy vấn
   * @returns Danh sách vai trò phân trang
   */
  async findAll(
    query: RoleQueryDto,
  ): Promise<PaginatedResult<RoleListItemDto>> {
    try {
      const result = await this.roleRepository.findAll(query);

      // Chuyển đổi sang DTO
      const items = result.items.map((role) =>
        plainToInstance(
          RoleListItemDto,
          {
            id: role.id,
            name: role.name,
            description: role.description,
            type: role.type,
            createdAt: role.createdAt,
          },
          { excludeExtraneousValues: true },
        ),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error finding roles: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.ROLE_FETCH_FAILED,
        `Không thể lấy danh sách vai trò: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của vai trò bao gồm danh sách quyền
   * @param id ID của vai trò
   * @returns Thông tin chi tiết của vai trò
   */
  async findById(id: number): Promise<RoleDetailDto> {
    try {
      // Lấy thông tin vai trò
      const role = await this.roleRepository.findById(id);
      if (!role) {
        throw new AppException(
          HRM_ERROR_CODES.ROLE_NOT_FOUND,
          `Không tìm thấy vai trò với ID ${id}`,
        );
      }

      // Lấy danh sách quyền của vai trò
      const { permissionIds, permissions } =
        await this.roleRepository.getRolePermissions(id);

      // Tạo DTO
      return plainToInstance(
        RoleDetailDto,
        {
          id: role.id,
          name: role.name,
          description: role.description,
          type: role.type,
          createdAt: role.createdAt,
          permissionIds,
          permissions,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding role: ${error.message}`, error.stack);
      throw new AppException(
        HRM_ERROR_CODES.ROLE_FETCH_FAILED,
        `Không thể lấy thông tin vai trò: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vai trò của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách vai trò của người dùng
   */
  async getUserRoles(userId: number): Promise<UserRolesResponseDto> {
    try {
      // Lấy danh sách vai trò của người dùng
      const roles = await this.roleRepository.getUserRoles(userId);

      // Chuyển đổi sang DTO
      const roleItems = roles.map((role) =>
        plainToInstance(
          UserRoleItemDto,
          {
            id: role.id,
            name: role.name,
            description: role.description,
            type: role.type,
          },
          { excludeExtraneousValues: true },
        ),
      );

      return {
        userId,
        roles: roleItems,
      };
    } catch (error) {
      this.logger.error(
        `Error getting user roles: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.ROLE_FETCH_FAILED,
        `Không thể lấy danh sách vai trò của người dùng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều vai trò
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto DTO chứa danh sách ID vai trò cần xóa
   * @returns Kết quả xóa nhiều vai trò
   */
  async bulkDelete(
    tenantId: number,
    dto: BulkDeleteRoleDto,
  ): Promise<BulkDeleteRoleResponseDto> {
    const { ids } = dto;

    // Tìm tất cả vai trò theo danh sách ID
    const roles = await this.roleRepository.findByIds(tenantId, ids);
    const foundIds = roles.map((role) => role.id);
    const notFoundIds = ids.filter((id) => !foundIds.includes(id));

    const result: BulkDeleteRoleResponseDto = {
      totalRequested: ids.length,
      successCount: 0,
      failureCount: 0,
      deletedIds: [],
      failures: [],
    };

    // Thêm các vai trò không tìm thấy vào danh sách lỗi
    notFoundIds.forEach((id) => {
      result.failures.push({
        id,
        reason: 'Vai trò không tồn tại',
      });
    });

    // Xử lý xóa từng vai trò được tìm thấy
    for (const role of roles) {
      try {
        // Kiểm tra vai trò có đang được sử dụng không
        const isInUse = await this.roleRepository.isRoleInUse(role.id);
        if (isInUse) {
          result.failures.push({
            id: role.id,
            reason: 'Vai trò đang được sử dụng bởi người dùng, không thể xóa',
          });
          continue;
        }

        // Xóa vai trò
        const deleted = await this.roleRepository.deleteRole(tenantId, role.id);
        if (deleted) {
          result.deletedIds.push(role.id);
          result.successCount++;
        } else {
          result.failures.push({
            id: role.id,
            reason: 'Xóa vai trò thất bại',
          });
        }
      } catch (error) {
        this.logger.error(
          `Error deleting role ${role.id}: ${error.message}`,
          error.stack,
        );
        result.failures.push({
          id: role.id,
          reason: `Lỗi khi xóa vai trò: ${error.message}`,
        });
      }
    }

    result.failureCount = result.failures.length;

    return result;
  }
}
