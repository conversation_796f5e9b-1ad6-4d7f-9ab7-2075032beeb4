import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Patch,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { EmployeeService } from '../services/employee.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { EmployeeResponseDto } from '../dto/employee-response.dto';
import { UpdateEmployeeStatusDto } from '../dto/update-employee-status.dto';
import { AssignDepartmentDto } from '../dto/assign-department.dto';
import { AssignManagerDto } from '../dto/assign-manager.dto';
import { BulkDeleteEmployeeDto } from '../dto/bulk-delete-employee.dto';
import { BulkDeleteEmployeeResponseDto } from '../dto/bulk-delete-employee-response.dto';
import { EmployeeWithDepartmentResponseDto } from '../dto/employee-with-department-response.dto';
import { EmployeeOverviewResponseDto } from '../dto/employee-overview-response.dto';
import { Employee } from '../entities/employee.entity';

/**
 * Controller for employee management
 */
@ApiTags(SWAGGER_API_TAG.HRM)
@ApiExtraModels(ApiResponseDto, EmployeeResponseDto, EmployeeWithDepartmentResponseDto, EmployeeOverviewResponseDto)
@Controller('api/hrm/employees')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  /**
   * Get employee overview statistics
   */
  @Get('overview')
  @ApiOperation({ summary: 'Get employee overview statistics' })
  @ApiResponse({
    status: 200,
    description: 'Employee overview statistics',
    schema: ApiResponseDto.getSchema(EmployeeOverviewResponseDto),
  })
  async getOverview(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<EmployeeOverviewResponseDto>> {
    const overview = await this.employeeService.getOverview(Number(user.tenantId));
    return ApiResponseDto.success(overview);
  }

  /**
   * Get all employees with pagination and filtering (includes department information)
   */
  @Get()
  @ApiOperation({ summary: 'Get all employees with department information' })
  @ApiResponse({
    status: 200,
    description: 'List of employees with department information',
    schema: ApiResponseDto.getPaginatedSchema(EmployeeWithDepartmentResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: EmployeeQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<EmployeeWithDepartmentResponseDto>>> {
    const employees = await this.employeeService.findAllWithDepartment(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.success(employees);
  }

  /**
   * Get employee by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get employee by ID' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee details',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.findById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Create a new employee
   */
  @Post()
  @ApiOperation({ summary: 'Create a new employee' })
  @ApiResponse({
    status: 201,
    description: 'Employee created successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async create(
    @Body() createEmployeeDto: CreateEmployeeDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.create(
      Number(user.tenantId),
      createEmployeeDto,
      user.id,
    );
    return ApiResponseDto.created(employee);
  }

  /**
   * Update employee
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee updated successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.update(
      Number(user.tenantId),
      id,
      updateEmployeeDto,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Update employee status
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Update employee status' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee status updated successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateEmployeeStatusDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.updateStatus(
      Number(user.tenantId),
      id,
      updateStatusDto,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Assign employee to department
   */
  @Patch(':id/department')
  @ApiOperation({ summary: 'Assign employee to department' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee assigned to department successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async assignToDepartment(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignDepartmentDto: AssignDepartmentDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.assignToDepartment(
      Number(user.tenantId),
      id,
      assignDepartmentDto.departmentId,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Assign manager to employee
   */
  @Patch(':id/manager')
  @ApiOperation({ summary: 'Assign manager to employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Manager assigned to employee successfully',
    schema: ApiResponseDto.getSchema(EmployeeResponseDto),
  })
  async assignManager(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignManagerDto: AssignManagerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<Employee>> {
    const employee = await this.employeeService.assignManager(
      Number(user.tenantId),
      id,
      assignManagerDto.managerId,
      user.id,
    );
    return ApiResponseDto.success(employee);
  }

  /**
   * Bulk delete employees
   */
  @Post('bulk-delete')
  @ApiOperation({ summary: 'Bulk delete employees' })
  @ApiResponse({
    status: 200,
    description: 'Bulk delete employees completed',
    schema: ApiResponseDto.getSchema(BulkDeleteEmployeeResponseDto),
  })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() dto: BulkDeleteEmployeeDto,
  ): Promise<ApiResponseDto<BulkDeleteEmployeeResponseDto>> {
    const result = await this.employeeService.bulkDelete(
      Number(user.tenantId),
      dto,
    );
    return ApiResponseDto.success(result, 'Xóa nhiều nhân viên hoàn tất');
  }

  /**
   * Delete employee
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete employee' })
  @ApiParam({ name: 'id', description: 'Employee ID', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Employee deleted successfully',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async remove(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    await this.employeeService.delete(Number(user.tenantId), id);
    return ApiResponseDto.success(true);
  }
}
