import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * DTO for updating employee status
 */
export class UpdateEmployeeStatusDto {
  /**
   * New employee status
   * @example "inactive"
   */
  @ApiProperty({
    description: 'New employee status',
    enum: EmployeeStatus,
    example: EmployeeStatus.INACTIVE,
  })
  @IsNotEmpty()
  @IsEnum(EmployeeStatus)
  status: EmployeeStatus;

  /**
   * Reason for status change
   * @example "Employee requested leave of absence"
   */
  @ApiProperty({
    required: false,
    description: 'Reason for status change',
    example: 'Employee requested leave of absence',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}
