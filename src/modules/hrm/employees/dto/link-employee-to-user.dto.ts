import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc gắn nhân viên với tài khoản người dùng
 */
export class LinkEmployeeToUserDto {
  /**
   * ID của nhân viên
   * @example 1
   */
  @ApiProperty({ description: 'ID của nhân viên', example: 1 })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  employeeId: number;

  /**
   * ID của người dùng
   * @example 1
   */
  @ApiProperty({ description: 'ID của người dùng', example: 1 })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  userId: number;
}
