import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsPositive } from 'class-validator';

/**
 * DTO để cập nhật vai trò cho nhân viên
 */
export class UpdateEmployeeRoleDto {
  /**
   * ID của nhân viên cần cập nhật vai trò
   */
  @ApiProperty({
    description: 'ID của nhân viên cần cập nhật vai trò',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  employeeId: number;

  /**
   * Danh sách ID của các vai trò sẽ được gán cho nhân viên
   */
  @ApiProperty({
    description: 'Danh sách ID của các vai trò sẽ được gán cho nhân viên',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsInt({ each: true })
  @IsPositive({ each: true })
  roleIds: number[];
}

/**
 * DTO phản hồi sau khi cập nhật vai trò cho nhân viên
 */
export class UpdateEmployeeRoleResponseDto {
  /**
   * ID của nhân viên đã được cập nhật vai trò
   */
  @ApiProperty({
    description: 'ID của nhân viên đã được cập nhật vai trò',
    example: 1,
  })
  employeeId: number;

  /**
   * ID của người dùng liên kết với nhân viên
   */
  @ApiProperty({
    description: 'ID của người dùng liên kết với nhân viên',
    example: 5,
  })
  userId: number;

  /**
   * Danh sách ID của các vai trò đã được gán cho nhân viên
   */
  @ApiProperty({
    description: 'Danh sách ID của các vai trò đã được gán cho nhân viên',
    example: [1, 2, 3],
    type: [Number],
  })
  roleIds: number[];
}
