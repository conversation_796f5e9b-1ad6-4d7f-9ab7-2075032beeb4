import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Mã lỗi cho module Calendar
 * Phạm vi: 14000-14099
 */
export const CALENDAR_ERROR_CODES = {
  // Calendar errors (14000-14019)
  CALENDAR_EVENT_NOT_FOUND: new ErrorCode(
    14000,
    'Không tìm thấy sự kiện lịch',
    HttpStatus.NOT_FOUND,
  ),
  CALENDAR_EVENT_CREATE_FAILED: new ErrorCode(
    14001,
    'Tạo sự kiện lịch thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CALENDAR_EVENT_UPDATE_FAILED: new ErrorCode(
    14002,
    'Cập nhật sự kiện lịch thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CALENDAR_EVENT_DELETE_FAILED: new ErrorCode(
    14003,
    '<PERSON><PERSON><PERSON> sự kiện lịch thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  CALENDAR_INVALID_DATE_RANGE: new ErrorCode(
    14004,
    'Khoảng thời gian không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  CALENDAR_PERMISSION_DENIED: new ErrorCode(
    14005,
    'Không có quyền thực hiện thao tác này',
    HttpStatus.FORBIDDEN,
  ),
  CALENDAR_REFERENCE_NOT_FOUND: new ErrorCode(
    14006,
    'Không tìm thấy tham chiếu',
    HttpStatus.NOT_FOUND,
  ),
  CALENDAR_INVALID_REFERENCE_TYPE: new ErrorCode(
    14007,
    'Loại tham chiếu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};
