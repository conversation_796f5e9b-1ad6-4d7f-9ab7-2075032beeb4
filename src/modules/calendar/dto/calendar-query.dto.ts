import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsInt, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { ReferenceType } from '../enum/reference-type.enum';

/**
 * DTO cho truy vấn lịch
 */
export class CalendarQueryDto extends QueryDto {
  /**
   * Thời gian bắt đầu khoảng thời gian cần xem (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu khoảng thời gian cần xem (Unix timestamp)',
    example: 1620000000000,
    required: true,
  })
  @IsInt()
  @Type(() => Number)
  startDate: number;

  /**
   * Thời gian kết thúc khoảng thời gian cần xem (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc khoảng thời gian cần xem (Unix timestamp)',
    example: 1622678400000,
    required: true,
  })
  @IsInt()
  @Type(() => Number)
  endDate: number;

  /**
   * Các loại sự kiện cần lấy
   */
  @ApiProperty({
    description: 'Các loại sự kiện cần lấy',
    enum: ReferenceType,
    isArray: true,
    example: [
      ReferenceType.CALENDAR,
      ReferenceType.TODO,
      ReferenceType.LEAVE_REQUEST,
    ],
    required: false,
  })
  @IsOptional()
  @IsEnum(ReferenceType, { each: true })
  @IsArray()
  types?: ReferenceType[];

  /**
   * ID của người dùng cần xem lịch (chỉ admin mới có thể xem lịch của người khác)
   */
  @ApiProperty({
    description:
      'ID của người dùng cần xem lịch (chỉ admin mới có thể xem lịch của người khác)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  userId?: number;
}
