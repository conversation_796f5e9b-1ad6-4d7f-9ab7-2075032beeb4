-- T<PERSON><PERSON><PERSON> các trường tham chiếu vào bảng calendars
ALTER TABLE calendars 
ADD COLUMN IF NOT EXISTS reference_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS reference_id INTEGER,
ADD COLUMN IF NOT EXISTS end_time BIGINT;

-- Thê<PERSON> comment cho các trường mới
COMMENT ON COLUMN calendars.reference_type IS 'Loại tham chiếu (todo, leave_request, event, v.v.)';
COMMENT ON COLUMN calendars.reference_id IS 'ID của bản ghi trong bảng tương ứng với loại tham chiếu';
COMMENT ON COLUMN calendars.end_time IS 'Thời gian kết thúc sự kiện (Unix timestamp)';

-- Tạo index để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_calendars_reference ON calendars(reference_type, reference_id);
CREATE INDEX IF NOT EXISTS idx_calendars_time_range ON calendars(start_time, end_time);
