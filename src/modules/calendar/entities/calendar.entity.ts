import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho các sự kiện lịch trong hệ thống
 */
@Entity('calendars')
export class Calendar {
  /**
   * <PERSON><PERSON><PERSON> danh duy nhất của sự kiện lịch
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Thời gian bắt đầu sự kiện (Unix timestamp)
   */
  @Column({ name: 'start_time', type: 'bigint', nullable: false })
  startTime: number;

  /**
   * Thời gian kết thúc sự kiện (Unix timestamp)
   */
  @Column({ name: 'end_time', type: 'bigint', nullable: true })
  endTime: number | null;

  /**
   * Mô tả chi tiết về sự kiện
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Thông tin bổ sung về sự kiện (JSON)
   */
  @Column({ type: 'json', nullable: true })
  info: any;

  /**
   * Loại tham chiếu (todo, leave_request, event, v.v.)
   */
  @Column({
    name: 'reference_type',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  referenceType: string | null;

  /**
   * ID của bản ghi trong bảng tương ứng với loại tham chiếu
   */
  @Column({ name: 'reference_id', type: 'integer', nullable: true })
  referenceId: number | null;

  /**
   * Thời điểm tạo sự kiện (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời điểm cập nhật sự kiện (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * ID của người cập nhật sự kiện
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID của công ty/tổ chức sở hữu bản ghi
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
