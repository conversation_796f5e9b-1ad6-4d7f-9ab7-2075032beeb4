// Entities
export * from './entities/chat-conversation.entity';
export * from './entities/chat-message.entity';
export * from './entities/facebook-page-config.entity';

// Controllers
export * from './controllers/facebook-webhook.controller';
export * from './controllers/facebook-integration.controller';
export * from './controllers/webapp-chat.controller';

// Services
export * from './services/facebook.service';
export * from './services/message-processor.service';
export * from './services/ai-orchestrator.service';
export * from './services/rag.service';
export * from './services/webapp-chat.service';
export * from './services/tool-registry.service';
export * from './services/enhanced-openai.service';

// Tools
export * from './tools/business-tools';

// Interfaces
export * from './interfaces/tool.interface';

// Repositories
export * from './repositories/facebook-page-config.repository';
export * from './repositories/conversation.repository';
export * from './repositories/message.repository';

// Gateways
export * from './gateways/webapp-chat.gateway';

// DTOs
export * from './dto';

// Module
export * from './chat.module';

// Types and Interfaces
export interface FacebookWebhookMessage {
  sender: { id: string };
  recipient: { id: string };
  timestamp: number;
  message?: {
    mid: string;
    text?: string;
    attachments?: any[];
    quick_reply?: { payload: string };
  };
  postback?: {
    payload: string;
    title: string;
  };
}

export interface AIResponse {
  text: string;
  intent: string;
  confidence: number;
  context: any;
  entities: any[];
  quickReplies?: Array<{ title: string; payload: string }>;
  requiresHumanHandoff?: boolean;
}

export interface VectorSearchResult {
  content: string;
  metadata: any;
  score: number;
  source: string;
}

export interface RAGContext {
  relevantData: VectorSearchResult[];
  businessMetrics: any;
  userContext: any;
  timestamp: number;
}

// Constants
export const CHAT_CONSTANTS = {
  MESSAGE_TYPES: {
    TEXT: 'text',
    IMAGE: 'image',
    FILE: 'file',
    AUDIO: 'audio',
    VIDEO: 'video',
    QUICK_REPLY: 'quick_reply',
    POSTBACK: 'postback',
  },

  CONVERSATION_STATUS: {
    ACTIVE: 'active',
    CLOSED: 'closed',
    WAITING: 'waiting',
    TRANSFERRED: 'transferred',
  },

  CONVERSATION_TYPES: {
    AUTO: 'auto',
    MANUAL: 'manual',
    MIXED: 'mixed',
  },

  SENDER_TYPES: {
    USER: 'user',
    BOT: 'bot',
    AGENT: 'agent',
  },

  MESSAGE_DIRECTIONS: {
    INCOMING: 'incoming',
    OUTGOING: 'outgoing',
  },

  INTENTS: {
    GREETING: 'greeting',
    GET_TODO_STATISTICS: 'get_todo_statistics',
    GET_EMPLOYEE_INFO: 'get_employee_info',
    GET_LATE_EMPLOYEES: 'get_late_employees',
    GET_OVERDUE_TASKS: 'get_overdue_tasks',
    GENERAL_QUESTION: 'general_question',
    UNKNOWN: 'unknown',
  },

  CATEGORIES: {
    HRM: 'hrm',
    TODO: 'todo',
    GENERAL: 'general',
    STATISTICS: 'statistics',
  },
} as const;
