### Webapp Chat API Test Examples
### Sử dụng với REST Client extension trong VS Code

### Variables
@baseUrl = http://localhost:3000
@token = your_jwt_token_here

### 1. <PERSON><PERSON><PERSON> tra trạng thái AI agent
GET {{baseUrl}}/webapp-chat/agent/status
Authorization: Bearer {{token}}

### 2. <PERSON><PERSON><PERSON> cuộc hội thoại active (hoặc tạo mới)
GET {{baseUrl}}/webapp-chat/conversations/active
Authorization: Bearer {{token}}

### 3. T<PERSON><PERSON> cuộc hội thoại mới
POST {{baseUrl}}/webapp-chat/conversations
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "language": "vi",
  "metadata": {
    "source": "webapp",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  }
}

### 4. <PERSON><PERSON><PERSON> tin nhắn text đơn giản
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "<PERSON>n chào, tôi cần hỗ trợ",
  "type": "text"
}

### 5. G<PERSON>i tin nhắn với conversationId cụ thể
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Cho tôi biết thống kê công việc hôm nay",
  "type": "text",
  "conversationId": 1
}

### 6. Gửi tin nhắn với file đính kèm
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Tôi gửi file báo cáo này",
  "type": "file",
  "metadata": {
    "fileUrl": "https://example.com/report.pdf",
    "fileName": "monthly-report.pdf",
    "fileSize": 1024000,
    "mimeType": "application/pdf"
  }
}

### 7. Lấy lịch sử cuộc hội thoại (trang 1)
GET {{baseUrl}}/webapp-chat/conversations/1/history?page=1&limit=20
Authorization: Bearer {{token}}

### 8. Lấy lịch sử cuộc hội thoại (trang 2)
GET {{baseUrl}}/webapp-chat/conversations/1/history?page=2&limit=20
Authorization: Bearer {{token}}

### Expected Response Format for conversation history:
# {
#   "conversationId": 1,
#   "status": "active",
#   "language": "vi",
#   "createdAt": 1640995200000,
#   "lastMessageAt": 1640995800000,
#   "messages": [
#     {
#       "id": 123,
#       "content": "Xin chào",
#       "type": "text",
#       "isAiGenerated": false,
#       "timestamp": 1640995200000
#     }
#   ],
#   "pagination": {
#     "page": 1,
#     "limit": 20,
#     "total": 50,
#     "totalPages": 3
#   }
# }

### 9. Đóng cuộc hội thoại
POST {{baseUrl}}/webapp-chat/conversations/1/close
Authorization: Bearer {{token}}

### 10. Test với các loại câu hỏi khác nhau

# Câu hỏi về nhân viên
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Có bao nhiêu nhân viên đi muộn hôm nay?",
  "type": "text"
}

###

# Câu hỏi về công việc
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Danh sách công việc quá hạn của tôi",
  "type": "text"
}

###

# Câu hỏi về thống kê
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Thống kê hoàn thành công việc tháng này",
  "type": "text"
}

###

# Câu hỏi chung
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Hướng dẫn sử dụng hệ thống ERP",
  "type": "text"
}

###

# Test với ngôn ngữ tiếng Anh
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "What is the current status of my tasks?",
  "type": "text"
}

### 11. Test error cases

# Gửi tin nhắn với conversationId không tồn tại
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Test message",
  "type": "text",
  "conversationId": 99999
}

###

# Lấy lịch sử với conversationId không tồn tại
GET {{baseUrl}}/webapp-chat/conversations/99999/history
Authorization: Bearer {{token}}

###

# Đóng cuộc hội thoại không tồn tại
POST {{baseUrl}}/webapp-chat/conversations/99999/close
Authorization: Bearer {{token}}

###

# Test không có token
GET {{baseUrl}}/webapp-chat/agent/status

###

# Test token không hợp lệ
GET {{baseUrl}}/webapp-chat/agent/status
Authorization: Bearer invalid_token

### 12. WebSocket Test (JavaScript code for browser console)

/*
// Kết nối WebSocket
const socket = io('/webapp-chat', {
  auth: {
    token: 'your_jwt_token_here'
  }
});

// Lắng nghe sự kiện kết nối
socket.on('webapp_chat:connection_status', (data) => {
  console.log('Connection status:', data);
});

// Lắng nghe phản hồi AI
socket.on('webapp_chat:ai_response', (response) => {
  console.log('AI Response:', response);
});

// Lắng nghe trạng thái gõ của AI
socket.on('webapp_chat:ai_typing', (data) => {
  console.log('AI typing:', data.isTyping);
});

// Lắng nghe sự kiện tham gia cuộc hội thoại
socket.on('webapp_chat:conversation_joined', (data) => {
  console.log('Conversation joined:', data);
});

// Lắng nghe lỗi
socket.on('webapp_chat:error', (error) => {
  console.error('Chat error:', error);
});

// Tham gia cuộc hội thoại
socket.emit('webapp_chat:join_conversation', {});

// Gửi tin nhắn
socket.emit('webapp_chat:send_message', {
  content: 'Xin chào từ WebSocket!',
  type: 'text'
});

// Bắt đầu gõ
socket.emit('webapp_chat:typing_start', {
  conversationId: 1
});

// Dừng gõ
socket.emit('webapp_chat:typing_stop', {
  conversationId: 1
});

// Rời cuộc hội thoại
socket.emit('webapp_chat:leave_conversation', {
  conversationId: 1
});
*/
