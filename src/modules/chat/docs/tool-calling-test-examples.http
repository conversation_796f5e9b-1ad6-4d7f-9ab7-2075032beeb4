### Tool Calling Test Examples
### Test các tính năng Tool Calling mới

### Variables
@baseUrl = http://localhost:3000
@token = your_jwt_token_here

### 1. Test Todo Statistics Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Cho tôi xem thống kê công việc tuần này",
  "type": "text"
}

### Expected: AI sẽ gọi get_todo_statistics tool với dateRange="week"

### 2. Test Employee Info Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Thông tin nhân viên Nguyễn Văn A",
  "type": "text"
}

### Expected: AI sẽ gọi get_employee_info tool với employeeName="Nguyễn Văn A"

### 3. Test Late Employees Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Có bao nhiêu nhân viên đi muộn hôm nay?",
  "type": "text"
}

### Expected: AI sẽ gọi get_late_employees tool với date=today

### 4. Test Overdue Tasks Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Danh sách công việc quá hạn khẩn cấp",
  "type": "text"
}

### Expected: AI sẽ gọi get_overdue_tasks tool với priority="urgent"

### 5. Test User Tasks Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Công việc đang chờ xử lý của tôi",
  "type": "text"
}

### Expected: AI sẽ gọi get_user_tasks tool với status="pending"

### 6. Test Team Statistics Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Thống kê hiệu suất team phát triển tháng này",
  "type": "text"
}

### Expected: AI sẽ gọi get_team_statistics tool

### 7. Test Employee List Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Danh sách nhân viên phòng IT đang hoạt động",
  "type": "text"
}

### Expected: AI sẽ gọi get_employee_list tool với status="active"

### 8. Test Task Details Tool
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Chi tiết công việc số 123",
  "type": "text"
}

### Expected: AI sẽ gọi get_task_details tool với taskId=123

### 9. Test Multi-tool Query
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Có bao nhiêu nhân viên đi muộn và bao nhiêu công việc quá hạn hôm nay?",
  "type": "text"
}

### Expected: AI sẽ gọi cả get_late_employees và get_overdue_tasks tools

### 10. Test No Tool Needed
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Xin chào, bạn có khỏe không?",
  "type": "text"
}

### Expected: AI sẽ trả lời thông thường không gọi tool

### 11. Test Complex Business Query
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Phân tích hiệu suất làm việc của team trong tháng này, bao gồm số lượng công việc hoàn thành, quá hạn và nhân viên có hiệu suất cao nhất",
  "type": "text"
}

### Expected: AI sẽ gọi multiple tools để thu thập dữ liệu

### 12. Test Error Handling
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Thông tin nhân viên ID 99999",
  "type": "text"
}

### Expected: AI sẽ gọi tool nhưng handle error gracefully

### 13. Test Vietnamese Natural Language
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Tôi muốn biết có bao nhiêu việc tôi cần làm trong tuần tới",
  "type": "text"
}

### Expected: AI hiểu tiếng Việt và gọi đúng tool

### 14. Test English Query
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Show me my pending tasks for this week",
  "type": "text"
}

### Expected: AI hiểu tiếng Anh và gọi đúng tool

### 15. Test Performance Query
POST {{baseUrl}}/webapp-chat/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Báo cáo tổng quan về tình hình công việc và nhân sự hôm nay",
  "type": "text"
}

### Expected: AI sẽ gọi multiple tools và tổng hợp kết quả

### WebSocket Test Examples

### 16. WebSocket Tool Calling Test
/*
// Kết nối WebSocket
const socket = io('/webapp-chat', {
  auth: { token: 'your_jwt_token_here' }
});

// Test tool calling qua WebSocket
socket.emit('webapp_chat:send_message', {
  content: 'Thống kê công việc tuần này',
  type: 'text'
});

// Lắng nghe phản hồi
socket.on('webapp_chat:ai_response', (response) => {
  console.log('AI Response with Tool Results:', response);
  // Expected: response.content chứa kết quả từ get_todo_statistics tool
});

// Test typing indicator với tool execution
socket.on('webapp_chat:ai_typing', (data) => {
  if (data.isTyping) {
    console.log('AI đang xử lý với tools...');
  } else {
    console.log('AI đã hoàn thành xử lý tools');
  }
});
*/

### Expected Response Format

### Tool Calling Response Structure:
# {
#   "messageId": "msg_123",
#   "content": "Dựa trên thống kê tuần này, bạn có:\n- 15 công việc đã hoàn thành\n- 8 công việc đang thực hiện\n- 3 công việc quá hạn\n- Tỷ lệ hoàn thành: 75%",
#   "type": "text",
#   "confidence": 0.95,
#   "intent": "function_call_processed",
#   "timestamp": 1640995200000,
#   "conversationId": 123,
#   "metadata": {
#     "toolsUsed": ["get_todo_statistics"],
#     "executionResults": [
#       {
#         "toolName": "get_todo_statistics",
#         "success": true,
#         "data": {
#           "completed": 15,
#           "inProgress": 8,
#           "overdue": 3,
#           "completionRate": 0.75
#         },
#         "executionTime": 1250
#       }
#     ]
#   }
# }

### Performance Metrics to Monitor:
# - Tool execution time < 2 seconds
# - Cache hit rate > 80%
# - Success rate > 95%
# - Function calling accuracy > 90%
# - User satisfaction with responses

### Debug Information:
# Check logs for:
# - "Registered X business tools"
# - "Processing message with X tools available"
# - "Suggested function calls: tool1, tool2"
# - "Tool 'tool_name' executed successfully in Xms"
