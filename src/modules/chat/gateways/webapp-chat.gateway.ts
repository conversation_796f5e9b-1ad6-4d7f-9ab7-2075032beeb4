import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
  WsException,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Logger, UseGuards, Injectable } from '@nestjs/common';
import { SocketClient } from '@/shared/socket/interfaces/socket-client.interface';
import { SocketAuthGuard } from '@/shared/socket/guards/socket-auth.guard';
import { SocketUser } from '@/shared/socket/decorators/socket-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  WebappChatMessageDto,
  TypingStatusDto,
  ConversationHistoryDto,
  WebappChatEventDto,
} from '../dto/webapp-chat.dto';
import { BusinessToolsProvider, WebappChatService } from '@modules/chat';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { ChatOpenAI } from '@langchain/openai';
import { MemorySaver } from '@langchain/langgraph';

/**
 * Enum định nghĩa các sự kiện WebSocket cho webapp chat
 * Tất cả events đều có prefix 'webapp_chat:' để tránh conflict
 */
export enum WebappChatEvents {
  // Sự kiện tin nhắn - xử lý gửi/nhận tin nhắn
  SEND_MESSAGE = 'webapp_chat:send_message',        // Client gửi tin nhắn
  MESSAGE_RECEIVED = 'webapp_chat:message_received', // Server confirm nhận tin nhắn
  AI_RESPONSE = 'webapp_chat:ai_response',          // Server gửi phản hồi AI

  // Sự kiện cuộc hội thoại - quản lý join/leave conversation
  JOIN_CONVERSATION = 'webapp_chat:join_conversation',     // Client join conversation
  LEAVE_CONVERSATION = 'webapp_chat:leave_conversation',   // Client leave conversation
  CONVERSATION_JOINED = 'webapp_chat:conversation_joined', // Server confirm joined
  CONVERSATION_LEFT = 'webapp_chat:conversation_left',     // Server confirm left

  // Sự kiện trạng thái - typing indicators
  TYPING_START = 'webapp_chat:typing_start',  // Client bắt đầu gõ
  TYPING_STOP = 'webapp_chat:typing_stop',    // Client dừng gõ
  USER_TYPING = 'webapp_chat:user_typing',    // Broadcast user đang gõ
  AI_TYPING = 'webapp_chat:ai_typing',        // AI đang xử lý

  // Sự kiện streaming - AI streaming response
  AI_STREAM_CHUNK = 'webapp_chat:ai_stream_chunk',     // Streaming chunk từ AI
  AI_STREAM_END = 'webapp_chat:ai_stream_end',         // Kết thúc streaming

  // Sự kiện lỗi - error handling
  ERROR = 'webapp_chat:error',

  // Sự kiện hệ thống - system status
  CONNECTION_STATUS = 'webapp_chat:connection_status', // Trạng thái kết nối
  AGENT_STATUS = 'webapp_chat:agent_status',           // Trạng thái AI agent
}

/**
 * WebSocket Gateway cho webapp chat với AI agent
 * Xử lý real-time communication giữa webapp và AI agent
 * Namespace: '/webapp-chat' để tách biệt với các chat khác
 */
@WebSocketGateway({
  cors: {
    origin: '*', // Cho phép tất cả origins (có thể restrict trong production)
    methods: ['GET', 'POST'],
    credentials: true,
  },
  namespace: 'webapp-chat', // Namespace riêng cho webapp chat
})
@Injectable()
export class WebappChatGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private agent: ReturnType<typeof createReactAgent>;
  private readonly logger = new Logger(WebappChatGateway.name);

  @WebSocketServer()
  server: Server; // Socket.IO server instance

  constructor(
    // Inject WebappChatService để xử lý business logic
    private readonly businessTools: BusinessToolsProvider,
    private readonly webappChatService: WebappChatService,

  ) {
    this.agent = createReactAgent({
      name: 'Trợ lý hệ thống RedAI AIERP',
      prompt: 'Bạn là trợ lý AI hữu ích cho hệ thống ERP. Hãy trả lời câu hỏi một cách ngắn gọn và chính xác.',
      llm: new ChatOpenAI({
        model: 'o4-mini'
      }),
      tools: [],
      checkpointer: new MemorySaver()
    });
  }

  /**
   * Khởi tạo gateway
   * @param server Server Socket.IO
   */
  afterInit(server: Server) {
    this.logger.log('Webapp Chat WebSocket Gateway initialized');
  }

  /**
   * Xử lý kết nối mới
   * @param client Client Socket.IO
   */
  handleConnection(client: SocketClient) {
    try {
      this.logger.log(`Webapp chat client connected: ${client.id}`);
      
      // Gửi thông báo kết nối thành công
      client.emit(WebappChatEvents.CONNECTION_STATUS, {
        status: 'connected',
        message: 'Connected to webapp chat',
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`Error handling webapp chat connection: ${error.message}`);
    }
  }

  /**
   * Xử lý ngắt kết nối
   * @param client Client Socket.IO
   */
  handleDisconnect(client: SocketClient) {
    try {
      this.logger.log(`Webapp chat client disconnected: ${client.id}`);
    } catch (error) {
      this.logger.error(`Error handling webapp chat disconnect: ${error.message}`);
    }
  }

  /**
   * Xử lý sự kiện gửi tin nhắn với streaming
   * @param client Client Socket.IO
   * @param data Dữ liệu tin nhắn
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(WebappChatEvents.SEND_MESSAGE)
  async handleSendMessage(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: WebappChatMessageDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      const tenantId = Number(user.tenantId);

      this.logger.log(
        `Processing webapp message from user ${user.id}: ${data.content}`,
      );

      // // Gửi thông báo đang xử lý
      client.emit(WebappChatEvents.AI_TYPING, {
        conversationId: data.conversationId,
        isTyping: true,
        timestamp: Date.now(),
      });

      // Xử lý tin nhắn với AI streaming
      const response =
        await this.webappChatService.processWebappMessageWithStreaming(
          user.id,
          tenantId,
          `${data.conversationId}`,
          this.agent,
          data,
          // Callback để gửi streaming chunks
          (chunk: string, messageId: number, conversationId: number) => {
            client.emit(WebappChatEvents.AI_STREAM_CHUNK, {
              messageId,
              conversationId,
              chunk,
              timestamp: Date.now(),
            });
          },
        );

      // // Gửi thông báo kết thúc streaming
      client.emit(WebappChatEvents.AI_STREAM_END, {
        messageId: response.messageId,
        conversationId: response.conversationId,
        fullContent: response.content,
        timestamp: Date.now(),
      });


      this.logger.log(
        `Sent AI streaming response to user ${user.id}, conversation ${response.conversationId}`,
      );

      return {
        event: WebappChatEvents.MESSAGE_RECEIVED,
        data: {
          status: 'success',
          messageId: response.messageId,
          conversationId: response.conversationId,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error processing webapp message for user ${user.id}: ${error.message}`,
      );

      // Dừng thông báo đang gõ nếu có lỗi
      client.emit(WebappChatEvents.AI_TYPING, {
        conversationId: data.conversationId,
        isTyping: false,
        timestamp: Date.now(),
      });

      // Gửi thông báo lỗi
      client.emit(WebappChatEvents.ERROR, {
        error: 'Failed to process message',
        message: error.message,
        timestamp: Date.now(),
      });

      throw new WsException('Failed to process message');
    }
  }

  /**
   * Xử lý sự kiện tham gia cuộc hội thoại
   * @param client Client Socket.IO
   * @param data Dữ liệu tham gia cuộc hội thoại
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(WebappChatEvents.JOIN_CONVERSATION)
  async handleJoinConversation(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: { conversationId?: number },
    @SocketUser() user: JwtPayload,
  ) {
    try {
      const tenantId = Number(user.tenantId);
      
      // Lấy hoặc tạo cuộc hội thoại
      const conversation = await this.webappChatService.getOrCreateActiveConversation(
        user.id,
        tenantId,
      );

      // Tham gia room cuộc hội thoại
      const roomId = `conversation_${conversation.id}`;
      await client.join(roomId);

      // Thông báo đã tham gia
      client.emit(WebappChatEvents.CONVERSATION_JOINED, {
        conversationId: conversation.id,
        roomId,
        status: conversation.status,
        language: conversation.language,
        timestamp: Date.now(),
      });

      this.logger.log(
        `User ${user.id} joined conversation ${conversation.id}`,
      );

      return {
        event: WebappChatEvents.CONVERSATION_JOINED,
        data: {
          conversationId: conversation.id,
          status: 'joined',
        },
      };
    } catch (error) {
      this.logger.error(
        `Error joining conversation for user ${user.id}: ${error.message}`,
      );

      client.emit(WebappChatEvents.ERROR, {
        error: 'Failed to join conversation',
        message: error.message,
        timestamp: Date.now(),
      });

      throw new WsException('Failed to join conversation');
    }
  }

  /**
   * Xử lý sự kiện rời cuộc hội thoại
   * @param client Client Socket.IO
   * @param data Dữ liệu rời cuộc hội thoại
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(WebappChatEvents.LEAVE_CONVERSATION)
  async handleLeaveConversation(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: { conversationId: number },
    @SocketUser() user: JwtPayload,
  ) {
    try {
      // Rời room cuộc hội thoại
      const roomId = `conversation_${data.conversationId}`;
      await client.leave(roomId);

      // Thông báo đã rời
      client.emit(WebappChatEvents.CONVERSATION_LEFT, {
        conversationId: data.conversationId,
        roomId,
        timestamp: Date.now(),
      });

      this.logger.log(
        `User ${user.id} left conversation ${data.conversationId}`,
      );

      return {
        event: WebappChatEvents.CONVERSATION_LEFT,
        data: {
          conversationId: data.conversationId,
          status: 'left',
        },
      };
    } catch (error) {
      this.logger.error(
        `Error leaving conversation for user ${user.id}: ${error.message}`,
      );

      throw new WsException('Failed to leave conversation');
    }
  }

  /**
   * Xử lý sự kiện bắt đầu gõ
   * @param client Client Socket.IO
   * @param data Dữ liệu trạng thái gõ
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(WebappChatEvents.TYPING_START)
  handleTypingStart(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: TypingStatusDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      // Thông báo cho các client khác trong room
      const roomId = `conversation_${data.conversationId}`;
      client.to(roomId).emit(WebappChatEvents.USER_TYPING, {
        userId: user.id,
        username: user.username,
        conversationId: data.conversationId,
        isTyping: true,
        timestamp: Date.now(),
      });

      return {
        event: WebappChatEvents.USER_TYPING,
        data: { status: 'typing_started' },
      };
    } catch (error) {
      this.logger.error(`Error handling typing start: ${error.message}`);
      throw new WsException('Failed to handle typing start');
    }
  }

  /**
   * Xử lý sự kiện dừng gõ
   * @param client Client Socket.IO
   * @param data Dữ liệu trạng thái gõ
   * @param user Thông tin người dùng
   */
  @UseGuards(SocketAuthGuard)
  @SubscribeMessage(WebappChatEvents.TYPING_STOP)
  handleTypingStop(
    @ConnectedSocket() client: SocketClient,
    @MessageBody() data: TypingStatusDto,
    @SocketUser() user: JwtPayload,
  ) {
    try {
      // Thông báo cho các client khác trong room
      const roomId = `conversation_${data.conversationId}`;
      client.to(roomId).emit(WebappChatEvents.USER_TYPING, {
        userId: user.id,
        username: user.username,
        conversationId: data.conversationId,
        isTyping: false,
        timestamp: Date.now(),
      });

      return {
        event: WebappChatEvents.USER_TYPING,
        data: { status: 'typing_stopped' },
      };
    } catch (error) {
      this.logger.error(`Error handling typing stop: ${error.message}`);
      throw new WsException('Failed to handle typing stop');
    }
  }
}
