import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { AuthModule } from '@/modules/auth/auth.module';
import { ServicesModule } from '@shared/services/services.module';

// Entities
import { ChatConversation } from './entities/chat-conversation.entity';
import { ChatMessage } from './entities/chat-message.entity';
import { FacebookPageConfig } from './entities/facebook-page-config.entity';
// import { ChatSession } from './entities/chat-session.entity';

// Controllers
// import { ChatController } from './controllers/chat.controller';
import { FacebookWebhookController } from './controllers/facebook-webhook.controller';
import { FacebookIntegrationController } from './controllers/facebook-integration.controller';
import { WebappChatController } from './controllers/webapp-chat.controller';
// import { ChatManagementController } from './controllers/chat-management.controller';

// Services
// import { ChatService } from './services/chat.service';
import { FacebookService } from './services/facebook.service';
import { MessageProcessorService } from './services/message-processor.service';
import { AIOrchestatorService } from './services/ai-orchestrator.service';
import { RAGService } from './services/rag.service';
import { WebappChatService } from './services/webapp-chat.service';
import { ToolRegistryService } from './services/tool-registry.service';
import { EnhancedOpenAiService } from './services/enhanced-openai.service';
import { BusinessToolsProvider } from './tools/business-tools';
// import { PromptManagerService } from './services/prompt-manager.service';

// Repositories
// import { ChatConversationRepository } from './repositories/chat-conversation.repository';
// import { ChatMessageRepository } from './repositories/chat-message.repository';
import { FacebookPageConfigRepository } from './repositories/facebook-page-config.repository';
import { ConversationRepository } from './repositories/conversation.repository';
import { MessageRepository } from './repositories/message.repository';
// import { ChatSessionRepository } from './repositories/chat-session.repository';

// Gateways
import { WebappChatGateway } from './gateways/webapp-chat.gateway';
import { EmailModule } from '@modules/email/email.module';
import { OkrsModule } from '@modules/okrs/okrs.module';
import { TodolistsModule } from '@modules/todolists';
import { EmployeesModule } from '@modules/hrm/employees/employees.module';
import { HrmModule } from '@modules/hrm/hrm.module';

/**
 * Module quản lý chat tích hợp Facebook Page và AI
 */
@Global()
@Module({
  imports: [
    AuthModule,
    ServicesModule,
    EmailModule,
    OkrsModule,
    TodolistsModule,
    HrmModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    TypeOrmModule.forFeature([
      ChatConversation,
      ChatMessage,
      FacebookPageConfig,
      // ChatSession,
    ]),
  ],
  controllers: [
    // ChatController,
    FacebookWebhookController,
    FacebookIntegrationController,
    WebappChatController,
    // ChatManagementController,
  ],
  providers: [
    // Repositories
    // ChatConversationRepository,
    // ChatMessageRepository,
    FacebookPageConfigRepository,
    ConversationRepository,
    MessageRepository,
    // ChatSessionRepository,

    // Services
    // ChatService,
    FacebookService,
    MessageProcessorService,
    AIOrchestatorService,
    RAGService,
    WebappChatService,
    ToolRegistryService,
    EnhancedOpenAiService,
    BusinessToolsProvider,
    // PromptManagerService,

    // Gateways
    WebappChatGateway,
  ],
  exports: [
    // ChatService,
    FacebookService,
    MessageProcessorService,
    AIOrchestatorService,
    RAGService,
    WebappChatService,
    ToolRegistryService,
    EnhancedOpenAiService,
    BusinessToolsProvider,
    ConversationRepository,
    MessageRepository,
    WebappChatGateway,
  ],
})
export class ChatModule {}
