import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Entity đại diện cho tin nhắn trong cuộc hội thoại
 */
@Entity('chat_messages')
@Index(['conversationId', 'createdAt'])
@Index(['messageType', 'status'])
export class ChatMessage {
  /**
   * Unique identifier for the message
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID của cuộc hội thoại
   */
  @Column({ name: 'conversation_id', type: 'integer', nullable: false })
  conversationId: number;

  /**
   * Facebook Message ID (nếu có)
   */
  @Column({
    name: 'facebook_message_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  facebookMessageId: string | null;

  /**
   * Loại tin nhắn: text, image, file, audio, video, quick_reply, postback
   */
  @Column({
    name: 'message_type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  messageType: string;

  /**
   * <PERSON>ội dung tin nhắn
   */
  @Column({ type: 'text', nullable: true })
  content: string | null;

  /**
   * Attachments (JSON array)
   */
  @Column({ type: 'jsonb', nullable: true })
  attachments: any;

  /**
   * Hướng tin nhắn: incoming (từ user), outgoing (từ bot/agent)
   */
  @Column({ type: 'varchar', length: 20, nullable: false })
  direction: string;

  /**
   * Người gửi: user, bot, agent
   */
  @Column({ name: 'sender_type', type: 'varchar', length: 20, nullable: false })
  senderType: string;

  /**
   * ID của agent gửi (nếu sender_type = agent)
   */
  @Column({ name: 'sender_agent_id', type: 'integer', nullable: true })
  senderAgentId: number | null;

  /**
   * Trạng thái tin nhắn: sent, delivered, read, failed
   */
  @Column({ type: 'varchar', length: 20, default: 'sent' })
  status: string;

  /**
   * Có phải tin nhắn được tạo bởi AI không
   */
  @Column({ name: 'is_ai_generated', type: 'boolean', default: false })
  isAiGenerated: boolean;

  /**
   * Context AI sử dụng để tạo tin nhắn (nếu có)
   */
  @Column({ name: 'ai_context', type: 'jsonb', nullable: true })
  aiContext: any;

  /**
   * Confidence score của AI (0-1)
   */
  @Column({
    name: 'ai_confidence',
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
  })
  aiConfidence: number | null;

  /**
   * Intent được AI nhận diện
   */
  @Column({
    name: 'detected_intent',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  detectedIntent: string | null;

  /**
   * Entities được AI trích xuất
   */
  @Column({ name: 'extracted_entities', type: 'jsonb', nullable: true })
  extractedEntities: any;

  /**
   * Metadata bổ sung
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  /**
   * Thời gian tạo tin nhắn
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * ID của tenant/company
   */
  @Column({ name: 'tenant_id', type: 'bigint', nullable: false })
  tenantId: number;
}
