import {
  IsString,
  IsOptional,
  IsEnum,
  IsNumber,
  IsObject,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum định nghĩa các loại tin nhắn webapp chat
 */
export enum WebappMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  AUDIO = 'audio',
  VIDEO = 'video',
  SYSTEM = 'system',
}

/**
 * Enum định nghĩa trạng thái cuộc hội thoại
 */
export enum ConversationStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
  WAITING = 'waiting',
  TRANSFERRED = 'transferred',
}

/**
 * DTO cho metadata tin nhắn
 */
export class MessageMetadataDto {
  @ApiProperty({ description: 'URL file đính kèm', required: false })
  @IsOptional()
  @IsString()
  fileUrl?: string;

  @ApiProperty({ description: 'Tên file', required: false })
  @IsOptional()
  @IsString()
  fileName?: string;

  @ApiProperty({ description: '<PERSON>ích thước file (bytes)', required: false })
  @IsOptional()
  @IsNumber()
  fileSize?: number;

  @ApiProperty({ description: 'Loại MIME', required: false })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiProperty({ description: 'Thông tin bổ sung', required: false })
  @IsOptional()
  @IsObject()
  extra?: any;
}

/**
 * DTO cho tin nhắn từ webapp
 */
export class WebappChatMessageDto {
  @ApiProperty({ description: 'Nội dung tin nhắn' })
  @IsString()
  content: string;

  @ApiProperty({
    enum: WebappMessageType,
    description: 'Loại tin nhắn',
    default: WebappMessageType.TEXT,
  })
  @IsEnum(WebappMessageType)
  @IsOptional()
  type?: WebappMessageType = WebappMessageType.TEXT;

  @ApiProperty({
    type: MessageMetadataDto,
    description: 'Metadata của tin nhắn',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MessageMetadataDto)
  metadata?: MessageMetadataDto;

  @ApiProperty({ description: 'ID cuộc hội thoại (nếu có)', required: false })
  @IsOptional()
  @IsNumber()
  conversationId?: number;
}

/**
 * DTO cho phản hồi từ AI agent
 */
export class ChatResponseDto {
  @ApiProperty({ description: 'ID tin nhắn' })
  @IsString()
  messageId: string;

  @ApiProperty({ description: 'Nội dung phản hồi' })
  @IsString()
  content: string;

  @ApiProperty({
    enum: WebappMessageType,
    description: 'Loại tin nhắn phản hồi',
  })
  @IsEnum(WebappMessageType)
  type: WebappMessageType;

  @ApiProperty({ description: 'Độ tin cậy của AI (0-1)' })
  @IsNumber()
  confidence: number;

  @ApiProperty({ description: 'Intent được nhận diện', required: false })
  @IsOptional()
  @IsString()
  intent?: string;

  @ApiProperty({ description: 'Có cần chuyển cho human không' })
  @IsOptional()
  requiresHumanHandoff?: boolean;

  @ApiProperty({ description: 'Thời gian tạo' })
  @IsNumber()
  timestamp: number;

  @ApiProperty({ description: 'ID cuộc hội thoại' })
  @IsNumber()
  conversationId: number;

  @ApiProperty({
    type: MessageMetadataDto,
    description: 'Metadata phản hồi',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MessageMetadataDto)
  metadata?: MessageMetadataDto;
}

/**
 * DTO cho tạo cuộc hội thoại mới
 */
export class CreateConversationDto {
  @ApiProperty({ description: 'Ngôn ngữ cuộc hội thoại', default: 'vi' })
  @IsOptional()
  @IsString()
  language?: string = 'vi';

  @ApiProperty({ description: 'Metadata bổ sung', required: false })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO cho lịch sử cuộc hội thoại
 */
export class ConversationHistoryDto {
  @ApiProperty({ description: 'ID cuộc hội thoại' })
  @IsNumber()
  conversationId: number;

  @ApiProperty({ description: 'Số trang', default: 1 })
  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: 'Số tin nhắn mỗi trang', default: 50 })
  @IsOptional()
  @IsNumber()
  limit?: number = 50;
}

/**
 * DTO cho phản hồi lịch sử cuộc hội thoại
 */
export class ConversationHistoryResponseDto {
  @ApiProperty({ description: 'ID cuộc hội thoại' })
  conversationId: number;

  @ApiProperty({ description: 'Trạng thái cuộc hội thoại' })
  status: ConversationStatus;

  @ApiProperty({ description: 'Ngôn ngữ' })
  language: string;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: number;

  @ApiProperty({ description: 'Thời gian tin nhắn cuối' })
  lastMessageAt: number | null;

  @ApiProperty({ description: 'Danh sách tin nhắn', type: [Object] })
  messages: Array<{
    id: number;
    content: string;
    type: WebappMessageType;
    isAiGenerated: boolean;
    aiConfidence?: number;
    detectedIntent?: string;
    timestamp: number;
    metadata?: any;
  }>;

  @ApiProperty({ description: 'Thông tin phân trang' })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * DTO cho sự kiện WebSocket
 */
export class WebappChatEventDto {
  @ApiProperty({ description: 'Loại sự kiện' })
  @IsString()
  event: string;

  @ApiProperty({ description: 'Dữ liệu sự kiện' })
  @IsObject()
  data: any;

  @ApiProperty({ description: 'Thời gian sự kiện' })
  @IsNumber()
  timestamp: number;

  @ApiProperty({ description: 'ID cuộc hội thoại', required: false })
  @IsOptional()
  @IsNumber()
  conversationId?: number;
}

/**
 * DTO cho trạng thái đang gõ
 */
export class TypingStatusDto {
  @ApiProperty({ description: 'ID cuộc hội thoại' })
  @IsNumber()
  conversationId: number;

  @ApiProperty({ description: 'Có đang gõ không' })
  @IsOptional()
  isTyping?: boolean = true;
}
