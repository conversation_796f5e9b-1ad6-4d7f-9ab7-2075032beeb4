import { Injectable } from '@nestjs/common';
import { TodoService } from '@/modules/todolists/services/todo.service';
import { EmployeeService } from '@/modules/hrm/employees/services/employee.service';
import { StatisticsService } from '@/modules/todolists/services/statistics.service';
import { ToolCategory, ToolContext, ToolDefinition, ToolResult } from '../interfaces/tool.interface';
import { EmailService } from '@modules/email/services';
import { KeyResultService, KeyResultSupportService, ObjectiveService, OkrCycleService } from '@modules/okrs/services';
import {
  EventService,
  ProjectService,
  TodoAttachmentService,
  TodoCollaboratorService,
  TodoCommentService,
  TodoTagService,
} from '@modules/todolists';
import { AttendanceService } from '@modules/hrm/attendance-management/services/attendance.service';
import { DepartmentService } from '@modules/hrm/org-units/services/department.service';
import { DepartmentMembersService } from '@modules/hrm/org-units/services/department-members.service';
import { RunContext, tool } from '@openai/agents';
import { z } from 'zod';

/**
 * Business Tools Provider cho ERP system
 * Cung cấp tất cả tools liên quan đến business logic
 * Mỗi tool tương ứng với một chức năng cụ thể trong ERP
 */

export interface RunContextShape {
  tenantId: string;
  userId: string;
}
@Injectable()
export class BusinessToolsProvider {
  constructor(
    // email
    private readonly emailService: EmailService,
    // okr
    private readonly keyResultService: KeyResultService,
    private readonly keyResultSupportService: KeyResultSupportService,
    private readonly objectiveService: ObjectiveService,
    private readonly okrCycleService: OkrCycleService,
    // todolist
    private readonly eventService: EventService,
    private readonly projectService: ProjectService,
    private readonly statisticsService: StatisticsService,
    private readonly todoService: TodoService,
    private readonly todoAttachmentService: TodoAttachmentService,
    private readonly todoCollaboratorService: TodoCollaboratorService,
    private readonly todoCommentService: TodoCommentService,
    private readonly todoTagService: TodoTagService,
    // hrm
    private readonly attendanceService: AttendanceService,
    private readonly employeeService: EmployeeService,
    private readonly departmentService: DepartmentService,
    private readonly departmentServiceMember: DepartmentMembersService,
  ) {}

  /**
   * Khởi tạo và trả về tất cả business tools đã định nghĩa
   * Đây là entry point để sử dụng các tools trong hệ thống
   */
  getEmailTools() {
    const emailService = this.emailService;
    return [
      tool({
        name: 'send_email',
        description: 'Gửi email tới người dùng',
        parameters: z.object({
          to: z.string().nonempty().email().describe('Địa chỉ email người nhận'),
          subject: z.string().nonempty().describe('Tiêu đề email'),
          body: z.string().nonempty().describe('Nội dung email'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            // use tenantId if needed
            const tenantId = runContext?.['tenantId'];
            // use userId if needed
            const userId = runContext?.['userId'];
            const { to, subject, body } = _args;
            await emailService.sendEmail({ to, subject, body });
            return 'Email sent successfully';
          } catch (error) {
            return `Failed to send email: ${error.message}`;
          }
        },
      })
    ]
  }

  getTodoTools() {
    const todoService = this.todoService;
    return [
      tool({
        name: 'create_todo',
        description: 'Tạo công việc mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề công việc'),
          description: z.string().optional().describe('Mô tả chi tiết công việc'),
          priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().default('medium').describe('Mức độ ưu tiên'),
          dueDate: z.string().optional().describe('Ngày hết hạn (YYYY-MM-DD)'),
          assigneeId: z.number().optional().describe('ID người được giao việc'),
          projectId: z.number().optional().describe('ID dự án'),
          parentId: z.number().optional().describe('ID công việc cha (nếu là subtask)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createTodoDto = {
              title: _args.title,
              description: _args.description,
              priority: _args.priority,
              dueDate: _args.dueDate ? new Date(_args.dueDate).getTime() : undefined,
              assigneeId: _args.assigneeId || userId,
              projectId: _args.projectId,
              parentId: _args.parentId,
              createdBy: userId,
            };

            const todo = await todoService.createTodo(tenantId, createTodoDto);
            return `Công việc "${todo.title}" đã được tạo thành công với ID: ${todo.id}`;
          } catch (error) {
            return `Tạo công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_user_todos',
        description: 'Lấy danh sách công việc của người dùng',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Trạng thái công việc'),
          priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Mức độ ưu tiên'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const tasks = await todoService.getUserTasks(tenantId, userId, {
              status: _args.status,
              priority: _args.priority,
              limit: _args.limit,
            });

            return `Tìm thấy ${tasks.length} công việc của người dùng`;
          } catch (error) {
            return `Lấy danh sách công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_overdue_todos',
        description: 'Lấy danh sách công việc quá hạn',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const overdueTasks = await todoService.getOverdueTasks(tenantId, {
              assigneeId: userId,
              limit: _args.limit,
            });

            return `Tìm thấy ${overdueTasks.length} công việc quá hạn`;
          } catch (error) {
            return `Lấy danh sách công việc quá hạn thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_todo_details',
        description: 'Lấy chi tiết công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          includeComments: z.boolean().optional().default(true).describe('Bao gồm bình luận'),
          includeSubtasks: z.boolean().optional().default(false).describe('Bao gồm công việc con'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const taskDetails = await todoService.getTaskDetails(tenantId, _args.todoId, {
              includeComments: _args.includeComments,
              includeSubtasks: _args.includeSubtasks,
              includeAttachments: false,
            });

            if (!taskDetails) {
              return 'Không tìm thấy công việc';
            }

            return `Chi tiết công việc "${taskDetails.title}" đã được lấy thành công`;
          } catch (error) {
            return `Lấy chi tiết công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_todo_status',
        description: 'Cập nhật trạng thái công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).describe('Trạng thái mới'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              status: _args.status,
              updatedBy: userId,
            };

            const updatedTodo = await todoService.updateTodo(tenantId, _args.todoId, updateDto);
            return `Trạng thái công việc "${updatedTodo.title}" đã được cập nhật thành ${_args.status}`;
          } catch (error) {
            return `Cập nhật trạng thái công việc thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getEmployeeTools() {
    const employeeService = this.employeeService;
    return [
      tool({
        name: 'get_employee_info',
        description: 'Lấy thông tin chi tiết nhân viên',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên'),
          employeeName: z.string().optional().describe('Tên nhân viên (tìm kiếm gần đúng)'),
          includeStats: z.boolean().optional().default(false).describe('Bao gồm thống kê hiệu suất'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            let employee;

            if (_args.employeeId) {
              employee = await employeeService.findById(tenantId, _args.employeeId);
            } else if (_args.employeeName) {
              const employees = await employeeService.searchByName(tenantId, _args.employeeName);
              employee = employees[0];
            } else {
              employee = await employeeService.findByUserId(tenantId, userId);
            }

            if (!employee) {
              return 'Không tìm thấy nhân viên';
            }

            return `Thông tin nhân viên ${employee.fullName} đã được lấy thành công`;
          } catch (error) {
            return `Lấy thông tin nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_employee_list',
        description: 'Lấy danh sách nhân viên theo bộ lọc',
        parameters: z.object({
          departmentId: z.number().optional().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          status: z.enum(['active', 'inactive', 'on_leave']).optional().default('active').describe('Trạng thái nhân viên'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const employees = await employeeService.getEmployeeList(tenantId, {
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status,
              limit: _args.limit,
            });

            return `Tìm thấy ${employees.length} nhân viên`;
          } catch (error) {
            return `Lấy danh sách nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_late_employees',
        description: 'Lấy danh sách nhân viên đi muộn',
        parameters: z.object({
          date: z.string().optional().describe('Ngày kiểm tra (YYYY-MM-DD), mặc định là hôm nay'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const date = _args.date || new Date().toISOString().split('T')[0];

            const lateEmployees = await employeeService.getLateEmployees(tenantId, date, {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_employee',
        description: 'Tạo nhân viên mới',
        parameters: z.object({
          fullName: z.string().nonempty().describe('Họ và tên đầy đủ'),
          email: z.string().email().describe('Email nhân viên'),
          phoneNumber: z.string().optional().describe('Số điện thoại'),
          departmentId: z.number().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          startDate: z.string().optional().describe('Ngày bắt đầu làm việc (YYYY-MM-DD)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createEmployeeDto = {
              fullName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              startDate: _args.startDate ? new Date(_args.startDate).getTime() : Date.now(),
              createdBy: userId,
            };

            const employee = await employeeService.create(tenantId, createEmployeeDto);
            return `Nhân viên "${employee.fullName}" đã được tạo thành công với ID: ${employee.id}`;
          } catch (error) {
            return `Tạo nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_employee',
        description: 'Cập nhật thông tin nhân viên',
        parameters: z.object({
          employeeId: z.number().describe('ID nhân viên'),
          fullName: z.string().optional().describe('Họ và tên đầy đủ'),
          email: z.string().email().optional().describe('Email nhân viên'),
          phoneNumber: z.string().optional().describe('Số điện thoại'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          status: z.enum(['active', 'inactive', 'on_leave']).optional().describe('Trạng thái nhân viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateEmployeeDto = {
              fullName: _args.fullName,
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status,
              updatedBy: userId,
            };

            const employee = await employeeService.update(tenantId, _args.employeeId, updateEmployeeDto);
            return `Thông tin nhân viên "${employee.fullName}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật thông tin nhân viên thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getStatisticsTools() {
    const statisticsService = this.statisticsService;
    return [
      tool({
        name: 'get_todo_statistics',
        description: 'Lấy thống kê công việc của người dùng hoặc team',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          teamId: z.number().optional().describe('ID team'),
          dateRange: z.enum(['today', 'week', 'month', 'quarter', 'year']).optional().default('week').describe('Khoảng thời gian thống kê'),
          includeCompleted: z.boolean().optional().default(true).describe('Bao gồm công việc đã hoàn thành'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const stats = await statisticsService.getTodoStatistics(tenantId, userId, {
              dateRange: _args.dateRange,
              teamId: _args.teamId,
              includeCompleted: _args.includeCompleted,
            });

            return `Thống kê công việc ${_args.dateRange} đã được lấy thành công`;
          } catch (error) {
            return `Lấy thống kê công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_team_statistics',
        description: 'Lấy thống kê hiệu suất team/phòng ban',
        parameters: z.object({
          teamId: z.number().describe('ID team/phòng ban'),
          dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
          includeIndividual: z.boolean().optional().default(false).describe('Bao gồm thống kê từng cá nhân'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const teamStats = await statisticsService.getTeamStatistics(tenantId, _args.teamId, {
              dateRange: _args.dateRange,
              includeIndividual: _args.includeIndividual,
            });

            return `Thống kê team đã được lấy thành công`;
          } catch (error) {
            return `Lấy thống kê team thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_employee_stats',
        description: 'Lấy thống kê hiệu suất nhân viên',
        parameters: z.object({
          employeeId: z.number().describe('ID nhân viên'),
          dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const stats = await statisticsService.getEmployeeStats(tenantId, _args.employeeId);

            return `Thống kê nhân viên đã được lấy thành công`;
          } catch (error) {
            return `Lấy thống kê nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_statistics',
        description: 'Lấy thống kê hiệu suất dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          includeMembers: z.boolean().optional().default(false).describe('Bao gồm thống kê từng thành viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const projectStats = await statisticsService.getProjectPerformance(tenantId, _args.projectId);

            return `Thống kê dự án đã được lấy thành công`;
          } catch (error) {
            return `Lấy thống kê dự án thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getOkrTools() {
    const keyResultService = this.keyResultService;
    const objectiveService = this.objectiveService;
    const okrCycleService = this.okrCycleService;

    return [
      tool({
        name: 'create_objective',
        description: 'Tạo Objective mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề Objective'),
          description: z.string().optional().describe('Mô tả chi tiết'),
          cycleId: z.number().describe('ID chu kỳ OKR'),
          ownerId: z.number().optional().describe('ID người sở hữu (mặc định là người tạo)'),
          parentId: z.number().optional().describe('ID Objective cha (nếu có)'),
          weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createObjectiveDto = {
              title: _args.title,
              description: _args.description,
              cycleId: _args.cycleId,
              ownerId: _args.ownerId || userId,
              parentId: _args.parentId,
              weight: _args.weight,
              createdBy: userId,
            };

            const objective = await objectiveService.create(tenantId, createObjectiveDto);
            return `Objective "${objective.title}" đã được tạo thành công với ID: ${objective.id}`;
          } catch (error) {
            return `Tạo Objective thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_key_result',
        description: 'Tạo Key Result mới',
        parameters: z.object({
          objectiveId: z.number().describe('ID Objective liên quan'),
          title: z.string().nonempty().describe('Tiêu đề Key Result'),
          description: z.string().optional().describe('Mô tả chi tiết'),
          targetValue: z.number().describe('Giá trị mục tiêu'),
          currentValue: z.number().optional().default(0).describe('Giá trị hiện tại'),
          unit: z.string().optional().describe('Đơn vị đo lường'),
          weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createKeyResultDto = {
              objectiveId: _args.objectiveId,
              title: _args.title,
              description: _args.description,
              targetValue: _args.targetValue,
              currentValue: _args.currentValue,
              unit: _args.unit,
              weight: _args.weight,
              createdBy: userId,
            };

            const keyResult = await keyResultService.create(tenantId, createKeyResultDto);
            return `Key Result "${keyResult.title}" đã được tạo thành công với ID: ${keyResult.id}`;
          } catch (error) {
            return `Tạo Key Result thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_key_result_progress',
        description: 'Cập nhật tiến độ Key Result',
        parameters: z.object({
          keyResultId: z.number().describe('ID Key Result'),
          currentValue: z.number().describe('Giá trị hiện tại mới'),
          note: z.string().optional().describe('Ghi chú về cập nhật'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              currentValue: _args.currentValue,
              note: _args.note,
              updatedBy: userId,
            };

            const keyResult = await keyResultService.update(tenantId, _args.keyResultId, updateDto);
            return `Tiến độ Key Result "${keyResult.title}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật tiến độ Key Result thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_okr_cycle_objectives',
        description: 'Lấy danh sách Objectives trong chu kỳ OKR',
        parameters: z.object({
          cycleId: z.number().describe('ID chu kỳ OKR'),
          ownerId: z.number().optional().describe('ID người sở hữu (lọc theo owner)'),
          includeKeyResults: z.boolean().optional().default(true).describe('Bao gồm Key Results'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const objectives = await objectiveService.findByCycle(tenantId, _args.cycleId, {
              ownerId: _args.ownerId,
              includeKeyResults: _args.includeKeyResults,
            });

            return `Tìm thấy ${objectives.length} Objectives trong chu kỳ OKR`;
          } catch (error) {
            return `Lấy danh sách Objectives thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_okr_cycle',
        description: 'Tạo chu kỳ OKR mới',
        parameters: z.object({
          name: z.string().nonempty().describe('Tên chu kỳ OKR'),
          description: z.string().optional().describe('Mô tả chu kỳ'),
          startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
          quarter: z.number().optional().describe('Quý (1-4)'),
          year: z.number().describe('Năm'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createCycleDto = {
              name: _args.name,
              description: _args.description,
              startDate: new Date(_args.startDate).getTime(),
              endDate: new Date(_args.endDate).getTime(),
              quarter: _args.quarter,
              year: _args.year,
              createdBy: userId,
            };

            const cycle = await okrCycleService.create(tenantId, createCycleDto);
            return `Chu kỳ OKR "${cycle.name}" đã được tạo thành công với ID: ${cycle.id}`;
          } catch (error) {
            return `Tạo chu kỳ OKR thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getProjectTools() {
    const projectService = this.projectService;
    return [
      tool({
        name: 'create_project',
        description: 'Tạo dự án mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề dự án'),
          description: z.string().optional().describe('Mô tả dự án'),
          startDate: z.string().optional().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().optional().describe('Ngày kết thúc (YYYY-MM-DD)'),
          managerId: z.number().optional().describe('ID người quản lý dự án'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().default('medium').describe('Mức độ ưu tiên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createProjectDto = {
              title: _args.title,
              description: _args.description,
              startDate: _args.startDate ? new Date(_args.startDate).getTime() : undefined,
              endDate: _args.endDate ? new Date(_args.endDate).getTime() : undefined,
              managerId: _args.managerId || userId,
              departmentId: _args.departmentId,
              priority: _args.priority,
              createdBy: userId,
            };

            const project = await projectService.create(tenantId, createProjectDto);
            return `Dự án "${project.title}" đã được tạo thành công với ID: ${project.id}`;
          } catch (error) {
            return `Tạo dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_list',
        description: 'Lấy danh sách dự án',
        parameters: z.object({
          managerId: z.number().optional().describe('ID người quản lý'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          status: z.enum(['active', 'completed', 'cancelled', 'on_hold']).optional().describe('Trạng thái dự án'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const projects = await projectService.findAll(tenantId, {
              managerId: _args.managerId,
              departmentId: _args.departmentId,
              status: _args.status,
              limit: _args.limit,
            });

            return `Tìm thấy ${projects.length} dự án`;
          } catch (error) {
            return `Lấy danh sách dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_details',
        description: 'Lấy chi tiết dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          includeTasks: z.boolean().optional().default(true).describe('Bao gồm danh sách công việc'),
          includeMembers: z.boolean().optional().default(true).describe('Bao gồm danh sách thành viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const project = await projectService.findById(tenantId, _args.projectId, {
              includeTasks: _args.includeTasks,
              includeMembers: _args.includeMembers,
            });

            if (!project) {
              return 'Không tìm thấy dự án';
            }

            return `Chi tiết dự án "${project.title}" đã được lấy thành công`;
          } catch (error) {
            return `Lấy chi tiết dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_project_status',
        description: 'Cập nhật trạng thái dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          status: z.enum(['active', 'completed', 'cancelled', 'on_hold']).describe('Trạng thái mới'),
          note: z.string().optional().describe('Ghi chú về thay đổi'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              status: _args.status,
              note: _args.note,
              updatedBy: userId,
            };

            const project = await projectService.update(tenantId, _args.projectId, updateDto);
            return `Trạng thái dự án "${project.title}" đã được cập nhật thành ${_args.status}`;
          } catch (error) {
            return `Cập nhật trạng thái dự án thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getDepartmentTools() {
    const departmentService = this.departmentService;
    const departmentMembersService = this.departmentServiceMember;

    return [
      tool({
        name: 'create_department',
        description: 'Tạo phòng ban mới',
        parameters: z.object({
          name: z.string().nonempty().describe('Tên phòng ban'),
          description: z.string().optional().describe('Mô tả phòng ban'),
          managerId: z.number().optional().describe('ID trưởng phòng'),
          parentId: z.number().optional().describe('ID phòng ban cha'),
          code: z.string().optional().describe('Mã phòng ban'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createDepartmentDto = {
              name: _args.name,
              description: _args.description,
              managerId: _args.managerId,
              parentId: _args.parentId,
              code: _args.code,
              createdBy: userId,
            };

            const department = await departmentService.create(tenantId, createDepartmentDto);
            return `Phòng ban "${department.name}" đã được tạo thành công với ID: ${department.id}`;
          } catch (error) {
            return `Tạo phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_department_list',
        description: 'Lấy danh sách phòng ban',
        parameters: z.object({
          parentId: z.number().optional().describe('ID phòng ban cha (lấy phòng ban con)'),
          managerId: z.number().optional().describe('ID trưởng phòng'),
          includeMembers: z.boolean().optional().default(false).describe('Bao gồm danh sách thành viên'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const departments = await departmentService.findAll(tenantId, {
              parentId: _args.parentId,
              managerId: _args.managerId,
              includeMembers: _args.includeMembers,
              limit: _args.limit,
            });

            return `Tìm thấy ${departments.length} phòng ban`;
          } catch (error) {
            return `Lấy danh sách phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'add_department_member',
        description: 'Thêm thành viên vào phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          employeeId: z.number().describe('ID nhân viên'),
          role: z.string().optional().default('member').describe('Vai trò trong phòng ban'),
          isManager: z.boolean().optional().default(false).describe('Có phải là trưởng phòng không'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const addMemberDto = {
              departmentId: _args.departmentId,
              employeeId: _args.employeeId,
              role: _args.role,
              isManager: _args.isManager,
              createdBy: userId,
            };

            await departmentMembersService.addMember(tenantId, addMemberDto);
            return `Nhân viên đã được thêm vào phòng ban thành công`;
          } catch (error) {
            return `Thêm thành viên vào phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_department_member',
        description: 'Xóa thành viên khỏi phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          employeeId: z.number().describe('ID nhân viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            await departmentMembersService.removeMember(tenantId, _args.departmentId, _args.employeeId);
            return `Nhân viên đã được xóa khỏi phòng ban thành công`;
          } catch (error) {
            return `Xóa thành viên khỏi phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_department_members',
        description: 'Lấy danh sách thành viên phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          role: z.string().optional().describe('Lọc theo vai trò'),
          includeManagers: z.boolean().optional().default(true).describe('Bao gồm trưởng phòng'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const members = await departmentMembersService.getMembers(tenantId, _args.departmentId, {
              role: _args.role,
              includeManagers: _args.includeManagers,
            });

            return `Tìm thấy ${members.length} thành viên trong phòng ban`;
          } catch (error) {
            return `Lấy danh sách thành viên phòng ban thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getAttendanceTools() {
    const attendanceService = this.attendanceService;
    return [
      tool({
        name: 'check_in',
        description: 'Chấm công vào làm',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          location: z.string().optional().describe('Vị trí chấm công'),
          note: z.string().optional().describe('Ghi chú'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const checkInDto = {
              employeeId: _args.employeeId || userId,
              location: _args.location,
              note: _args.note,
              checkInTime: Date.now(),
            };

            const attendance = await attendanceService.checkIn(tenantId, checkInDto);
            return `Chấm công vào làm thành công lúc ${new Date(attendance.checkInTime).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công vào làm thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'check_out',
        description: 'Chấm công ra về',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          location: z.string().optional().describe('Vị trí chấm công'),
          note: z.string().optional().describe('Ghi chú'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const checkOutDto = {
              employeeId: _args.employeeId || userId,
              location: _args.location,
              note: _args.note,
              checkOutTime: Date.now(),
            };

            const attendance = await attendanceService.checkOut(tenantId, checkOutDto);
            return `Chấm công ra về thành công lúc ${new Date(attendance.checkOutTime).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công ra về thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_attendance_today',
        description: 'Lấy thông tin chấm công hôm nay',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');
            const employeeId = _args.employeeId || userId;

            const today = new Date().toISOString().split('T')[0];
            const attendance = await attendanceService.getAttendanceByDate(tenantId, employeeId, today);

            if (!attendance) {
              return 'Chưa có thông tin chấm công hôm nay';
            }

            return `Thông tin chấm công hôm nay đã được lấy thành công`;
          } catch (error) {
            return `Lấy thông tin chấm công thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_attendance_report',
        description: 'Lấy báo cáo chấm công',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
          departmentId: z.number().optional().describe('ID phòng ban (để lấy báo cáo toàn phòng ban)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const reportOptions = {
              employeeId: _args.employeeId || userId,
              startDate: _args.startDate,
              endDate: _args.endDate,
              departmentId: _args.departmentId,
            };

            const report = await attendanceService.getAttendanceReport(tenantId, reportOptions);
            return `Báo cáo chấm công từ ${_args.startDate} đến ${_args.endDate} đã được tạo thành công`;
          } catch (error) {
            return `Tạo báo cáo chấm công thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_late_employees_today',
        description: 'Lấy danh sách nhân viên đi muộn hôm nay',
        parameters: z.object({
          departmentId: z.number().optional().describe('ID phòng ban'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const today = new Date().toISOString().split('T')[0];

            const lateEmployees = await attendanceService.getLateEmployees(tenantId, today, {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn hôm nay`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }


  /**
   * Lấy tất cả business tools đã định nghĩa
   * Method này sẽ được gọi khi khởi tạo để register tools
   */
  getAllBusinessTools(): ToolDefinition[] {
    return [
      this.getTodoStatisticsTool(), // Thống kê công việc
      this.getEmployeeInfoTool(), // Thông tin nhân viên
      this.getLateEmployeesTool(), // Nhân viên đi muộn
      this.getOverdueTasksTool(), // Công việc quá hạn
      this.getUserTasksTool(), // Công việc của user
      this.getTeamStatisticsTool(), // Thống kê team
      this.getEmployeeListTool(), // Danh sách nhân viên
      this.getTaskDetailsTool(), // Chi tiết công việc
    ];
  }

  /**
   * Tool: Lấy thống kê công việc
   * Cung cấp thống kê tổng quan về công việc trong khoảng thời gian
   */
  private getTodoStatisticsTool(): ToolDefinition {
    return {
      name: 'get_todo_statistics',
      description:
        'Lấy thống kê tổng quan về công việc (todo) của người dùng hoặc team trong khoảng thời gian nhất định',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description:
              'ID người dùng cần lấy thống kê (optional, mặc định là người dùng hiện tại)',
          },
          teamId: {
            type: 'number',
            description: 'ID team cần lấy thống kê (optional)',
          },
          dateRange: {
            type: 'string',
            description: 'Khoảng thời gian thống kê',
            enum: ['today', 'week', 'month', 'quarter', 'year'],
            default: 'week',
          },
          includeCompleted: {
            type: 'boolean',
            description: 'Có bao gồm công việc đã hoàn thành không',
            default: true,
          },
        },
        required: [], // Không có tham số bắt buộc
      },
      // Handler function - logic chính của tool
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          // Lấy userId từ params hoặc context (user hiện tại)
          const userId = params.userId || context.userId;
          const dateRange = params.dateRange || 'week';

          // Gọi service để lấy thống kê
          const stats = await this.statisticsService.getTodoStatistics(
            context.tenantId, // Đảm bảo tenant isolation
            userId,
            {
              dateRange,
              teamId: params.teamId,
              includeCompleted: params.includeCompleted ?? true,
            },
          );

          // Trả về kết quả thành công
          return {
            success: true,
            data: stats,
            message: `Thống kê công việc ${dateRange} đã được lấy thành công`,
            metadata: {
              userId,
              dateRange,
              teamId: params.teamId,
            },
          };
        } catch (error) {
          // Xử lý lỗi và trả về error response
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thống kê công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true, // Cần tenant isolation
      rateLimit: 30, // Tối đa 30 calls/minute
      cacheable: true, // Có thể cache kết quả
      cacheTtl: 300, // Cache 5 phút
    };
  }

  /**
   * Tool: Lấy thông tin nhân viên
   */
  private getEmployeeInfoTool(): ToolDefinition {
    return {
      name: 'get_employee_info',
      description: 'Lấy thông tin chi tiết của nhân viên theo ID hoặc tên',
      parameters: {
        type: 'object',
        properties: {
          employeeId: {
            type: 'number',
            description: 'ID nhân viên',
          },
          employeeName: {
            type: 'string',
            description: 'Tên nhân viên (tìm kiếm gần đúng)',
          },
          includeStats: {
            type: 'boolean',
            description: 'Có bao gồm thống kê hiệu suất không',
            default: false,
          },
        },
        required: [],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          let employee;

          if (params.employeeId) {
            employee = await this.employeeService.findById(
              context.tenantId,
              params.employeeId,
            );
          } else if (params.employeeName) {
            const employees = await this.employeeService.searchByName(
              context.tenantId,
              params.employeeName,
            );
            employee = employees[0]; // Lấy kết quả đầu tiên
          } else {
            // Lấy thông tin nhân viên hiện tại
            employee = await this.employeeService.findByUserId(
              context.tenantId,
              context.userId,
            );
          }

          if (!employee) {
            return {
              success: false,
              message: 'Không tìm thấy nhân viên',
            };
          }

          let stats: any = null;
          if (params.includeStats) {
            stats = await this.statisticsService.getEmployeeStats(
              context.tenantId,
              employee.id,
            );
          }

          return {
            success: true,
            data: {
              employee,
              stats,
            },
            message: `Thông tin nhân viên ${employee.fullName} đã được lấy thành công`,
            metadata: {
              employeeId: employee.id,
              includeStats: params.includeStats,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thông tin nhân viên',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 600, // 10 minutes
    };
  }

  /**
   * Tool: Lấy danh sách nhân viên đi muộn
   */
  private getLateEmployeesTool(): ToolDefinition {
    return {
      name: 'get_late_employees',
      description:
        'Lấy danh sách nhân viên đi muộn trong ngày hoặc khoảng thời gian nhất định',
      parameters: {
        type: 'object',
        properties: {
          date: {
            type: 'string',
            description: 'Ngày cần kiểm tra (YYYY-MM-DD), mặc định là hôm nay',
          },
          departmentId: {
            type: 'number',
            description: 'ID phòng ban (optional)',
          },
          minLateMinutes: {
            type: 'number',
            description: 'Số phút đi muộn tối thiểu để tính',
            default: 1,
          },
        },
        required: [],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const date = params.date || new Date().toISOString().split('T')[0];

          const lateEmployees = await this.employeeService.getLateEmployees(
            context.tenantId,
            date,
            {
              departmentId: params.departmentId,
              limit: params.limit || 10,
            },
          );

          return {
            success: true,
            data: lateEmployees,
            message: `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}`,
            metadata: {
              date,
              departmentId: params.departmentId,
              count: lateEmployees.length,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách nhân viên đi muộn',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 300, // 5 minutes
    };
  }

  /**
   * Tool: Lấy công việc quá hạn
   */
  private getOverdueTasksTool(): ToolDefinition {
    return {
      name: 'get_overdue_tasks',
      description: 'Lấy danh sách công việc quá hạn của người dùng hoặc team',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description:
              'ID người dùng (optional, mặc định là người dùng hiện tại)',
          },
          teamId: {
            type: 'number',
            description: 'ID team (optional)',
          },
          priority: {
            type: 'string',
            description: 'Mức độ ưu tiên',
            enum: ['low', 'medium', 'high', 'urgent'],
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả trả về',
            default: 20,
            maximum: 100,
          },
        },
        required: [],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const userId = params.userId || context.userId;

          const overdueTasks = await this.todoService.getOverdueTasks(
            context.tenantId,
            {
              assigneeId: userId,
              departmentId: params.teamId,
              limit: params.limit || 20,
            },
          );

          return {
            success: true,
            data: overdueTasks,
            message: `Tìm thấy ${overdueTasks.length} công việc quá hạn`,
            metadata: {
              userId,
              teamId: params.teamId,
              count: overdueTasks.length,
            },
            suggestions:
              overdueTasks.length > 0
                ? [
                    {
                      title: 'Xem chi tiết công việc đầu tiên',
                      action: 'get_task_details',
                      parameters: { taskId: overdueTasks[0].id },
                    },
                  ]
                : [],
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách công việc quá hạn',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 180, // 3 minutes
    };
  }

  /**
   * Tool: Lấy công việc của người dùng
   */
  private getUserTasksTool(): ToolDefinition {
    return {
      name: 'get_user_tasks',
      description: 'Lấy danh sách công việc của người dùng theo trạng thái',
      parameters: {
        type: 'object',
        properties: {
          userId: {
            type: 'number',
            description:
              'ID người dùng (optional, mặc định là người dùng hiện tại)',
          },
          status: {
            type: 'string',
            description: 'Trạng thái công việc',
            enum: ['pending', 'in_progress', 'completed', 'cancelled'],
            default: 'pending',
          },
          priority: {
            type: 'string',
            description: 'Mức độ ưu tiên',
            enum: ['low', 'medium', 'high', 'urgent'],
          },
          dueDate: {
            type: 'string',
            description: 'Ngày deadline (YYYY-MM-DD)',
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả',
            default: 10,
            maximum: 50,
          },
        },
        required: [],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const userId = params.userId || context.userId;

          const tasks = await this.todoService.getUserTasks(
            context.tenantId,
            userId,
            {
              status: params.status || 'pending',
              priority: params.priority,
              dueDate: params.dueDate,
              limit: params.limit || 10,
            },
          );

          return {
            success: true,
            data: tasks,
            message: `Tìm thấy ${tasks.length} công việc`,
            metadata: {
              userId,
              status: params.status,
              count: tasks.length,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 120, // 2 minutes
    };
  }

  /**
   * Tool: Lấy thống kê team
   */
  private getTeamStatisticsTool(): ToolDefinition {
    return {
      name: 'get_team_statistics',
      description: 'Lấy thống kê hiệu suất của team/phòng ban',
      parameters: {
        type: 'object',
        properties: {
          teamId: {
            type: 'number',
            description: 'ID team/phòng ban',
          },
          dateRange: {
            type: 'string',
            description: 'Khoảng thời gian thống kê',
            enum: ['week', 'month', 'quarter'],
            default: 'month',
          },
          includeIndividual: {
            type: 'boolean',
            description: 'Có bao gồm thống kê từng cá nhân không',
            default: false,
          },
        },
        required: ['teamId'],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const teamStats = await this.statisticsService.getTeamStatistics(
            context.tenantId,
            params.teamId,
            {
              dateRange: params.dateRange || 'month',
              includeIndividual: params.includeIndividual || false,
            },
          );

          return {
            success: true,
            data: teamStats,
            message: `Thống kê team đã được lấy thành công`,
            metadata: {
              teamId: params.teamId,
              dateRange: params.dateRange,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy thống kê team',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 20,
      cacheable: true,
      cacheTtl: 600, // 10 minutes
    };
  }

  /**
   * Tool: Lấy danh sách nhân viên
   */
  private getEmployeeListTool(): ToolDefinition {
    return {
      name: 'get_employee_list',
      description: 'Lấy danh sách nhân viên theo bộ lọc',
      parameters: {
        type: 'object',
        properties: {
          departmentId: {
            type: 'number',
            description: 'ID phòng ban',
          },
          position: {
            type: 'string',
            description: 'Chức vụ',
          },
          status: {
            type: 'string',
            description: 'Trạng thái nhân viên',
            enum: ['active', 'inactive', 'on_leave'],
            default: 'active',
          },
          limit: {
            type: 'number',
            description: 'Số lượng tối đa kết quả',
            default: 20,
            maximum: 100,
          },
        },
        required: [],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const employees = await this.employeeService.getEmployeeList(
            context.tenantId,
            {
              departmentId: params.departmentId,
              position: params.position,
              status: params.status || 'active',
              limit: params.limit || 20,
            },
          );

          return {
            success: true,
            data: employees,
            message: `Tìm thấy ${employees.length} nhân viên`,
            metadata: {
              count: employees.length,
              departmentId: params.departmentId,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy danh sách nhân viên',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 30,
      cacheable: true,
      cacheTtl: 300, // 5 minutes
    };
  }

  /**
   * Tool: Lấy chi tiết công việc
   */
  private getTaskDetailsTool(): ToolDefinition {
    return {
      name: 'get_task_details',
      description: 'Lấy thông tin chi tiết của một công việc',
      parameters: {
        type: 'object',
        properties: {
          taskId: {
            type: 'number',
            description: 'ID công việc',
          },
          includeComments: {
            type: 'boolean',
            description: 'Có bao gồm comments không',
            default: true,
          },
          includeHistory: {
            type: 'boolean',
            description: 'Có bao gồm lịch sử thay đổi không',
            default: false,
          },
        },
        required: ['taskId'],
      },
      handler: async (
        params: any,
        context: ToolContext,
      ): Promise<ToolResult> => {
        try {
          const taskDetails = await this.todoService.getTaskDetails(
            context.tenantId,
            params.taskId,
            {
              includeComments: params.includeComments ?? true,
              includeSubtasks: params.includeSubtasks ?? false,
              includeAttachments: params.includeAttachments ?? false,
            },
          );

          if (!taskDetails) {
            return {
              success: false,
              message: 'Không tìm thấy công việc',
            };
          }

          return {
            success: true,
            data: taskDetails,
            message: `Chi tiết công việc "${taskDetails.title}" đã được lấy thành công`,
            metadata: {
              taskId: params.taskId,
              includeComments: params.includeComments,
              includeHistory: params.includeHistory,
            },
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            message: 'Không thể lấy chi tiết công việc',
          };
        }
      },
      category: ToolCategory.BUSINESS,
      tenantIsolated: true,
      rateLimit: 60,
      cacheable: true,
      cacheTtl: 180, // 3 minutes
    };
  }
}
