import { Injectable } from '@nestjs/common';
import { TodoService } from '@/modules/todolists/services/todo.service';
import { EmployeeService } from '@/modules/hrm/employees/services/employee.service';
import { StatisticsService } from '@/modules/todolists/services/statistics.service';
import { ToolCategory, ToolContext, ToolDefinition, ToolResult } from '../interfaces/tool.interface';
import { EmailService } from '@modules/email/services';
import { KeyResultService, KeyResultSupportService, ObjectiveService, OkrCycleService } from '@modules/okrs/services';
import {
  EventService,
  ProjectService,
  TodoAttachmentService,
  TodoCollaboratorService,
  TodoCommentService,
  TodoTagService,
} from '@modules/todolists';
import { AttendanceService } from '@modules/hrm/attendance-management/services/attendance.service';
import { DepartmentService } from '@modules/hrm/org-units/services/department.service';
import { DepartmentMembersService } from '@modules/hrm/org-units/services/department-members.service';
import { RunContext, tool } from '@openai/agents';
import { z } from 'zod';
import { TodoPriority } from '@modules/todolists/enum/todo-priority.enum';
import { TodoStatus } from '@modules/todolists/enum/todo-status.enum';
import { EmployeeStatus } from '@modules/hrm/employees/enum/employee-status.enum';
import { ObjectiveType } from '@modules/okrs/enum/objective-type.enum';

/**
 * Business Tools Provider cho ERP system
 * Cung cấp tất cả tools liên quan đến business logic
 * Mỗi tool tương ứng với một chức năng cụ thể trong ERP
 */

export interface RunContextShape {
  tenantId: string;
  userId: string;
}
@Injectable()
export class BusinessToolsProvider {
  constructor(
    // email
    private readonly emailService: EmailService,
    // okr
    private readonly keyResultService: KeyResultService,
    private readonly keyResultSupportService: KeyResultSupportService,
    private readonly objectiveService: ObjectiveService,
    private readonly okrCycleService: OkrCycleService,
    // todolist
    private readonly eventService: EventService,
    private readonly projectService: ProjectService,
    private readonly statisticsService: StatisticsService,
    private readonly todoService: TodoService,
    private readonly todoAttachmentService: TodoAttachmentService,
    private readonly todoCollaboratorService: TodoCollaboratorService,
    private readonly todoCommentService: TodoCommentService,
    private readonly todoTagService: TodoTagService,
    // hrm
    private readonly attendanceService: AttendanceService,
    private readonly employeeService: EmployeeService,
    private readonly departmentService: DepartmentService,
    private readonly departmentServiceMember: DepartmentMembersService,
  ) {}

  /**
   * Khởi tạo và trả về tất cả business tools đã định nghĩa
   * Đây là entry point để sử dụng các tools trong hệ thống
   */
  getEmailTools() {
    const emailService = this.emailService;
    return [
      tool({
        name: 'send_email',
        description: 'Gửi email tới người dùng',
        parameters: z.object({
          to: z.string().nonempty().email().describe('Địa chỉ email người nhận'),
          subject: z.string().nonempty().describe('Tiêu đề email'),
          body: z.string().nonempty().describe('Nội dung email'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            // use tenantId if needed
            const tenantId = runContext?.['tenantId'];
            // use userId if needed
            const userId = runContext?.['userId'];
            const { to, subject, body } = _args;
            await emailService.sendEmail({ to, subject, body });
            return 'Email sent successfully';
          } catch (error) {
            return `Failed to send email: ${error.message}`;
          }
        },
      })
    ]
  }

  getTodoTools() {
    const todoService = this.todoService;
    return [
      tool({
        name: 'create_todo',
        description: 'Tạo công việc mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề công việc'),
          description: z.string().optional().describe('Mô tả chi tiết công việc'),
          priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().default('medium').describe('Mức độ ưu tiên'),
          dueDate: z.string().optional().describe('Ngày hết hạn (YYYY-MM-DD)'),
          assigneeId: z.number().optional().describe('ID người được giao việc'),
          projectId: z.number().optional().describe('ID dự án'),
          parentId: z.number().optional().describe('ID công việc cha (nếu là subtask)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createTodoDto = {
              title: _args.title,
              description: _args.description,
              priority: _args.priority as TodoPriority,
              dueDate: _args.dueDate ? new Date(_args.dueDate).getTime() : undefined,
              assigneeId: _args.assigneeId || userId,
              projectId: _args.projectId,
              parentId: _args.parentId,
              createdBy: userId,
            };

            const todo = await todoService.createTodo(tenantId, userId, createTodoDto);
            return `Công việc "${todo.title}" đã được tạo thành công với ID: ${todo.id}`;
          } catch (error) {
            return `Tạo công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_user_todos',
        description: 'Lấy danh sách công việc của người dùng',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional().describe('Trạng thái công việc'),
          priority: z.enum(['low', 'medium', 'high', 'urgent']).optional().describe('Mức độ ưu tiên'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const tasks = await todoService.getUserTasks(tenantId, userId, {
              status: _args.status,
              priority: _args.priority,
              limit: _args.limit,
            });

            return `Tìm thấy ${tasks.length} công việc của người dùng:\n${JSON.stringify(tasks, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_overdue_todos',
        description: 'Lấy danh sách công việc quá hạn',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const overdueTasks = await todoService.getOverdueTasks(tenantId, {
              assigneeId: userId,
              limit: _args.limit,
            });

            return `Tìm thấy ${overdueTasks.length} công việc quá hạn:\n${JSON.stringify(overdueTasks, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách công việc quá hạn thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_todo_details',
        description: 'Lấy chi tiết công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          includeComments: z.boolean().optional().default(true).describe('Bao gồm bình luận'),
          includeSubtasks: z.boolean().optional().default(false).describe('Bao gồm công việc con'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const taskDetails = await todoService.getTaskDetails(tenantId, _args.todoId, {
              includeComments: _args.includeComments,
              includeSubtasks: _args.includeSubtasks,
              includeAttachments: false,
            });

            if (!taskDetails) {
              return 'Không tìm thấy công việc';
            }

            return `Chi tiết công việc "${taskDetails.title}":\n${JSON.stringify(taskDetails, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_todo_status',
        description: 'Cập nhật trạng thái công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).describe('Trạng thái mới'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              status: _args.status as TodoStatus,
              updatedBy: userId,
            };

            const updatedTodo = await todoService.updateTodo(tenantId, _args.todoId, userId, updateDto);
            return `Trạng thái công việc "${updatedTodo?.title || 'N/A'}" đã được cập nhật thành ${_args.status}`;
          } catch (error) {
            return `Cập nhật trạng thái công việc thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getEmployeeTools() {
    const employeeService = this.employeeService;
    return [
      tool({
        name: 'get_employee_info',
        description: 'Lấy thông tin chi tiết nhân viên',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên'),
          employeeName: z.string().optional().describe('Tên nhân viên (tìm kiếm gần đúng)'),
          includeStats: z.boolean().optional().default(false).describe('Bao gồm thống kê hiệu suất'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            let employee;

            if (_args.employeeId) {
              employee = await employeeService.findById(tenantId, _args.employeeId);
            } else if (_args.employeeName) {
              const employees = await employeeService.searchByName(tenantId, _args.employeeName);
              employee = employees[0];
            } else {
              employee = await employeeService.findByUserId(tenantId, userId);
            }

            if (!employee) {
              return 'Không tìm thấy nhân viên';
            }

            return `Thông tin nhân viên ${employee.employeeName || 'N/A'}:\n${JSON.stringify(employee, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_employee_list',
        description: 'Lấy danh sách nhân viên theo bộ lọc',
        parameters: z.object({
          departmentId: z.number().optional().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          status: z.enum(['active', 'inactive', 'on_leave']).optional().default('active').describe('Trạng thái nhân viên'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const employees = await employeeService.getEmployeeList(tenantId, {
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status,
              limit: _args.limit,
            });

            return `Tìm thấy ${employees.length} nhân viên:\n${JSON.stringify(employees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_late_employees',
        description: 'Lấy danh sách nhân viên đi muộn',
        parameters: z.object({
          date: z.string().optional().describe('Ngày kiểm tra (YYYY-MM-DD), mặc định là hôm nay'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const date = _args.date || new Date().toISOString().split('T')[0];

            const lateEmployees = await employeeService.getLateEmployees(tenantId, new Date(date).getTime(), {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn ngày ${date}:\n${JSON.stringify(lateEmployees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_employee',
        description: 'Tạo nhân viên mới',
        parameters: z.object({
          fullName: z.string().nonempty().describe('Họ và tên đầy đủ'),
          email: z.string().email().describe('Email nhân viên'),
          phoneNumber: z.string().optional().describe('Số điện thoại'),
          departmentId: z.number().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          startDate: z.string().optional().describe('Ngày bắt đầu làm việc (YYYY-MM-DD)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createEmployeeDto = {
              employeeName: _args.fullName, // Sử dụng employeeName thay vì fullName
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              startDate: _args.startDate ? new Date(_args.startDate).getTime() : Date.now(),
              createdBy: userId,
            };

            const employee = await employeeService.create(tenantId, createEmployeeDto, userId);
            return `Nhân viên "${employee.employeeName || 'N/A'}" đã được tạo thành công với ID: ${employee.id}`;
          } catch (error) {
            return `Tạo nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_employee',
        description: 'Cập nhật thông tin nhân viên',
        parameters: z.object({
          employeeId: z.number().describe('ID nhân viên'),
          fullName: z.string().optional().describe('Họ và tên đầy đủ'),
          email: z.string().email().optional().describe('Email nhân viên'),
          phoneNumber: z.string().optional().describe('Số điện thoại'),
          departmentId: z.number().optional().describe('ID phòng ban'),
          position: z.string().optional().describe('Chức vụ'),
          status: z.enum(['active', 'inactive', 'on_leave']).optional().describe('Trạng thái nhân viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateEmployeeDto = {
              employeeName: _args.fullName, // Sử dụng employeeName thay vì fullName
              email: _args.email,
              phoneNumber: _args.phoneNumber,
              departmentId: _args.departmentId,
              position: _args.position,
              status: _args.status as EmployeeStatus, // Cast to EmployeeStatus
              updatedBy: userId,
            };

            const employee = await employeeService.update(tenantId, _args.employeeId, updateEmployeeDto, userId);
            return `Thông tin nhân viên "${employee.employeeName || 'N/A'}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật thông tin nhân viên thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getStatisticsTools() {
    const statisticsService = this.statisticsService;
    return [
      tool({
        name: 'get_todo_statistics',
        description: 'Lấy thống kê công việc của người dùng hoặc team',
        parameters: z.object({
          userId: z.number().optional().describe('ID người dùng (mặc định là người dùng hiện tại)'),
          teamId: z.number().optional().describe('ID team'),
          dateRange: z.enum(['today', 'week', 'month', 'quarter', 'year']).optional().default('week').describe('Khoảng thời gian thống kê'),
          includeCompleted: z.boolean().optional().default(true).describe('Bao gồm công việc đã hoàn thành'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = _args.userId || parseInt(runContext?.['userId'] || '0');

            const stats = await statisticsService.getTodoStatistics(tenantId, userId, {
              dateRange: _args.dateRange,
              teamId: _args.teamId,
              includeCompleted: _args.includeCompleted,
            });

            return `Thống kê công việc ${_args.dateRange}:\n${JSON.stringify(stats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê công việc thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_team_statistics',
        description: 'Lấy thống kê hiệu suất team/phòng ban',
        parameters: z.object({
          teamId: z.number().describe('ID team/phòng ban'),
          dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
          includeIndividual: z.boolean().optional().default(false).describe('Bao gồm thống kê từng cá nhân'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const teamStats = await statisticsService.getTeamStatistics(tenantId, _args.teamId, {
              dateRange: _args.dateRange,
              includeIndividual: _args.includeIndividual,
            });

            return `Thống kê team:\n${JSON.stringify(teamStats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê team thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_employee_stats',
        description: 'Lấy thống kê hiệu suất nhân viên',
        parameters: z.object({
          employeeId: z.number().describe('ID nhân viên'),
          dateRange: z.enum(['week', 'month', 'quarter']).optional().default('month').describe('Khoảng thời gian thống kê'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const stats = await statisticsService.getEmployeeStats(tenantId, _args.employeeId);

            return `Thống kê nhân viên:\n${JSON.stringify(stats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê nhân viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_statistics',
        description: 'Lấy thống kê hiệu suất dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          includeMembers: z.boolean().optional().default(false).describe('Bao gồm thống kê từng thành viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const projectStats = await statisticsService.getProjectPerformance(tenantId, _args.projectId);

            return `Thống kê dự án:\n${JSON.stringify(projectStats, null, 2)}`;
          } catch (error) {
            return `Lấy thống kê dự án thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getOkrTools() {
    const keyResultService = this.keyResultService;
    const objectiveService = this.objectiveService;
    const okrCycleService = this.okrCycleService;

    return [
      tool({
        name: 'create_objective',
        description: 'Tạo Objective mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề Objective'),
          description: z.string().optional().describe('Mô tả chi tiết'),
          cycleId: z.number().describe('ID chu kỳ OKR'),
          ownerId: z.number().optional().describe('ID người sở hữu (mặc định là người tạo)'),
          parentId: z.number().optional().describe('ID Objective cha (nếu có)'),
          weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createObjectiveDto = {
              title: _args.title,
              description: _args.description,
              cycleId: _args.cycleId,
              ownerId: _args.ownerId || userId,
              parentId: _args.parentId,
              weight: _args.weight,
              type: ObjectiveType.INDIVIDUAL, // Thêm type bắt buộc
              startDate: new Date().toISOString(), // Thêm startDate bắt buộc
              endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // Thêm endDate bắt buộc (90 ngày)
              createdBy: userId,
            };

            const objective = await objectiveService.create(tenantId, userId, createObjectiveDto);
            return `Objective "${objective.title}" đã được tạo thành công với ID: ${objective.id}`;
          } catch (error) {
            return `Tạo Objective thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_key_result',
        description: 'Tạo Key Result mới',
        parameters: z.object({
          objectiveId: z.number().describe('ID Objective liên quan'),
          title: z.string().nonempty().describe('Tiêu đề Key Result'),
          description: z.string().optional().describe('Mô tả chi tiết'),
          targetValue: z.number().describe('Giá trị mục tiêu'),
          currentValue: z.number().optional().default(0).describe('Giá trị hiện tại'),
          unit: z.string().optional().describe('Đơn vị đo lường'),
          weight: z.number().optional().default(1).describe('Trọng số (1-10)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createKeyResultDto = {
              objectiveId: _args.objectiveId,
              title: _args.title,
              description: _args.description,
              targetValue: _args.targetValue,
              currentValue: _args.currentValue,
              unit: _args.unit,
              weight: _args.weight,
              createdBy: userId,
            };

            const keyResult = await keyResultService.create(tenantId, createKeyResultDto);
            return `Key Result "${keyResult.title}" đã được tạo thành công với ID: ${keyResult.id}`;
          } catch (error) {
            return `Tạo Key Result thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_key_result_progress',
        description: 'Cập nhật tiến độ Key Result',
        parameters: z.object({
          keyResultId: z.number().describe('ID Key Result'),
          currentValue: z.number().describe('Giá trị hiện tại mới'),
          note: z.string().optional().describe('Ghi chú về cập nhật'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              currentValue: _args.currentValue,
              note: _args.note,
              updatedBy: userId,
            };

            const keyResult = await keyResultService.update(tenantId, _args.keyResultId, updateDto);
            return `Tiến độ Key Result "${keyResult.title}" đã được cập nhật thành công`;
          } catch (error) {
            return `Cập nhật tiến độ Key Result thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_okr_cycle_objectives',
        description: 'Lấy danh sách Objectives trong chu kỳ OKR',
        parameters: z.object({
          cycleId: z.number().describe('ID chu kỳ OKR'),
          ownerId: z.number().optional().describe('ID người sở hữu (lọc theo owner)'),
          includeKeyResults: z.boolean().optional().default(true).describe('Bao gồm Key Results'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const objectives = await objectiveService.findAll(tenantId, {
              cycleId: _args.cycleId,
              ownerId: _args.ownerId,
              page: 1,
              limit: 20,
            });

            return `Tìm thấy ${objectives.items?.length || 0} Objectives trong chu kỳ OKR:\n${JSON.stringify(objectives.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách Objectives thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'create_okr_cycle',
        description: 'Tạo chu kỳ OKR mới',
        parameters: z.object({
          name: z.string().nonempty().describe('Tên chu kỳ OKR'),
          description: z.string().optional().describe('Mô tả chu kỳ'),
          startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
          quarter: z.number().optional().describe('Quý (1-4)'),
          year: z.number().describe('Năm'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createCycleDto = {
              name: _args.name,
              description: _args.description,
              startDate: _args.startDate,
              endDate: _args.endDate,
              quarter: _args.quarter,
              year: _args.year,
              createdBy: userId,
            };

            const cycle = await okrCycleService.create(tenantId, userId, createCycleDto);
            return `Chu kỳ OKR "${cycle.name}" đã được tạo thành công với ID: ${cycle.id}`;
          } catch (error) {
            return `Tạo chu kỳ OKR thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getProjectTools() {
    const projectService = this.projectService;
    return [
      tool({
        name: 'create_project',
        description: 'Tạo dự án mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề dự án'),
          description: z.string().optional().describe('Mô tả dự án'),
          ownerId: z.number().optional().describe('ID người sở hữu dự án (mặc định là người tạo)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createProjectDto = {
              title: _args.title,
              description: _args.description,
              ownerId: _args.ownerId,
            };

            const project = await projectService.createProject(tenantId, userId, createProjectDto);
            return `Dự án "${project.title}" đã được tạo thành công với ID: ${project.id}:\n${JSON.stringify(project, null, 2)}`;
          } catch (error) {
            return `Tạo dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_list',
        description: 'Lấy danh sách dự án',
        parameters: z.object({
          isActive: z.boolean().optional().describe('Lọc theo trạng thái hoạt động'),
          page: z.number().optional().default(1).describe('Số trang'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
          search: z.string().optional().describe('Từ khóa tìm kiếm'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const query = {
              isActive: _args.isActive,
              page: _args.page || 1,
              limit: _args.limit || 10,
              search: _args.search,
            };

            const projects = await projectService.findAllProjects(tenantId, query);
            return `Tìm thấy ${projects.items?.length || 0} dự án:\n${JSON.stringify(projects, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_details',
        description: 'Lấy chi tiết dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const project = await projectService.findProjectById(tenantId, _args.projectId);
            return `Chi tiết dự án "${project.title}":\n${JSON.stringify(project, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_project',
        description: 'Cập nhật thông tin dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          title: z.string().optional().describe('Tiêu đề dự án mới'),
          description: z.string().optional().describe('Mô tả dự án mới'),
          isActive: z.boolean().optional().describe('Trạng thái hoạt động'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateDto = {
              title: _args.title,
              description: _args.description,
              isActive: _args.isActive,
            };

            const project = await projectService.updateProject(tenantId, _args.projectId, userId, updateDto);
            return `Dự án "${project?.title || 'N/A'}" đã được cập nhật thành công:\n${JSON.stringify(project, null, 2)}`;
          } catch (error) {
            return `Cập nhật dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'delete_project',
        description: 'Xóa dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const result = await projectService.deleteProject(tenantId, _args.projectId, userId);
            return result ? `Dự án đã được xóa thành công` : `Xóa dự án thất bại`;
          } catch (error) {
            return `Xóa dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'add_project_member',
        description: 'Thêm thành viên vào dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          userId: z.number().describe('ID người dùng'),
          role: z.enum(['admin', 'member', 'viewer']).describe('Vai trò trong dự án'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const currentUserId = parseInt(runContext?.['userId'] || '0');

            const createMemberDto = {
              userId: _args.userId,
              role: _args.role as any,
            };

            const member = await projectService.addProjectMember(tenantId, _args.projectId, currentUserId, createMemberDto);
            return `Thành viên đã được thêm vào dự án thành công:\n${JSON.stringify(member, null, 2)}`;
          } catch (error) {
            return `Thêm thành viên vào dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_project_members',
        description: 'Lấy danh sách thành viên dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          page: z.number().optional().default(1).describe('Số trang'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 10,
            };

            const members = await projectService.findProjectMembers(tenantId, _args.projectId, userId, query);
            return `Tìm thấy ${members.items?.length || 0} thành viên trong dự án:\n${JSON.stringify(members, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách thành viên dự án thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_project_member',
        description: 'Cập nhật vai trò thành viên dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          memberId: z.number().describe('ID thành viên'),
          role: z.enum(['admin', 'member', 'viewer']).describe('Vai trò mới'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateMemberDto = {
              role: _args.role as any,
            };

            const member = await projectService.updateProjectMember(tenantId, _args.projectId, _args.memberId, userId, updateMemberDto);
            return `Vai trò thành viên đã được cập nhật thành công:\n${JSON.stringify(member, null, 2)}`;
          } catch (error) {
            return `Cập nhật vai trò thành viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_project_member',
        description: 'Xóa thành viên khỏi dự án',
        parameters: z.object({
          projectId: z.number().describe('ID dự án'),
          memberId: z.number().describe('ID thành viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const result = await projectService.removeProjectMember(tenantId, _args.projectId, _args.memberId, userId);
            return result ? `Thành viên đã được xóa khỏi dự án thành công` : `Xóa thành viên khỏi dự án thất bại`;
          } catch (error) {
            return `Xóa thành viên khỏi dự án thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getDepartmentTools() {
    const departmentService = this.departmentService;
    const departmentMembersService = this.departmentServiceMember;

    return [
      tool({
        name: 'create_department',
        description: 'Tạo phòng ban mới',
        parameters: z.object({
          name: z.string().nonempty().describe('Tên phòng ban'),
          description: z.string().optional().describe('Mô tả phòng ban'),
          managerId: z.number().optional().describe('ID trưởng phòng'),
          parentId: z.number().optional().describe('ID phòng ban cha'),
          code: z.string().optional().describe('Mã phòng ban'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createDepartmentDto = {
              name: _args.name,
              description: _args.description,
              managerId: _args.managerId,
              parentId: _args.parentId,
              code: _args.code,
              createdBy: userId,
            };

            const department = await departmentService.create(tenantId, createDepartmentDto);
            return `Phòng ban "${department.name}" đã được tạo thành công với ID: ${department.id}`;
          } catch (error) {
            return `Tạo phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_department_list',
        description: 'Lấy danh sách phòng ban',
        parameters: z.object({
          parentId: z.number().optional().describe('ID phòng ban cha (lấy phòng ban con)'),
          managerId: z.number().optional().describe('ID trưởng phòng'),
          includeMembers: z.boolean().optional().default(false).describe('Bao gồm danh sách thành viên'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const departments = await departmentService.findAll(tenantId, {
              parentId: _args.parentId,
              managerId: _args.managerId,
              limit: _args.limit,
              page: 1, // Thêm page bắt buộc
            });

            return `Tìm thấy ${departments.items?.length || 0} phòng ban:\n${JSON.stringify(departments.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'add_department_member',
        description: 'Thêm thành viên vào phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          employeeId: z.number().describe('ID nhân viên'),
          role: z.string().optional().default('member').describe('Vai trò trong phòng ban'),
          isManager: z.boolean().optional().default(false).describe('Có phải là trưởng phòng không'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const addMemberDto = {
              departmentId: _args.departmentId,
              employeeId: _args.employeeId,
              role: _args.role,
              isManager: _args.isManager,
              createdBy: userId,
            };

            // DepartmentMembersService không có method addMember, cần implement
            return `Tính năng thêm thành viên vào phòng ban chưa được triển khai`;
          } catch (error) {
            return `Thêm thành viên vào phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_department_member',
        description: 'Xóa thành viên khỏi phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          employeeId: z.number().describe('ID nhân viên'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            // DepartmentMembersService không có method removeMember, cần implement
            return `Tính năng xóa thành viên khỏi phòng ban chưa được triển khai`;
          } catch (error) {
            return `Xóa thành viên khỏi phòng ban thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_department_members',
        description: 'Lấy danh sách thành viên phòng ban',
        parameters: z.object({
          departmentId: z.number().describe('ID phòng ban'),
          role: z.string().optional().describe('Lọc theo vai trò'),
          includeManagers: z.boolean().optional().default(true).describe('Bao gồm trưởng phòng'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            // DepartmentMembersService không có method getMembers, cần implement
            return `Tính năng lấy danh sách thành viên phòng ban chưa được triển khai`;
          } catch (error) {
            return `Lấy danh sách thành viên phòng ban thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getAttendanceTools() {
    const attendanceService = this.attendanceService;
    return [
      tool({
        name: 'check_in',
        description: 'Chấm công vào làm',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          location: z.string().optional().describe('Vị trí chấm công'),
          note: z.string().optional().describe('Ghi chú'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const checkInDto = {
              employeeId: _args.employeeId || userId,
              location: _args.location,
              note: _args.note,
              checkInTime: Date.now(),
            };

            const attendance = await attendanceService.checkIn(tenantId, _args.employeeId || userId, {
              location: _args.location,
              notes: _args.note,
            }, userId);
            return `Chấm công vào làm thành công lúc ${new Date(attendance.checkInTime || Date.now()).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công vào làm thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'check_out',
        description: 'Chấm công ra về',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          location: z.string().optional().describe('Vị trí chấm công'),
          note: z.string().optional().describe('Ghi chú'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const checkOutDto = {
              employeeId: _args.employeeId || userId,
              location: _args.location,
              note: _args.note,
              checkOutTime: Date.now(),
            };

            const attendance = await attendanceService.checkOut(tenantId, _args.employeeId || userId, {
              location: _args.location,
              notes: _args.note,
            }, userId);
            return `Chấm công ra về thành công lúc ${new Date(attendance.checkOutTime || Date.now()).toLocaleTimeString()}`;
          } catch (error) {
            return `Chấm công ra về thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_attendance_today',
        description: 'Lấy thông tin chấm công hôm nay',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');
            const employeeId = _args.employeeId || userId;

            const today = new Date().toISOString().split('T')[0];
            const attendance = await attendanceService.getAttendanceStats(tenantId, employeeId, new Date(today), new Date(today));

            if (!attendance) {
              return 'Chưa có thông tin chấm công hôm nay';
            }

            return `Thông tin chấm công hôm nay:\n${JSON.stringify(attendance, null, 2)}`;
          } catch (error) {
            return `Lấy thông tin chấm công thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_attendance_report',
        description: 'Lấy báo cáo chấm công',
        parameters: z.object({
          employeeId: z.number().optional().describe('ID nhân viên (mặc định là người dùng hiện tại)'),
          startDate: z.string().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().describe('Ngày kết thúc (YYYY-MM-DD)'),
          departmentId: z.number().optional().describe('ID phòng ban (để lấy báo cáo toàn phòng ban)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const reportOptions = {
              employeeId: _args.employeeId || userId,
              startDate: _args.startDate,
              endDate: _args.endDate,
              departmentId: _args.departmentId,
            };

            // AttendanceService không có method getAttendanceReport, sử dụng getAttendanceStats
            const report = await attendanceService.getAttendanceStats(
              tenantId,
              _args.employeeId || userId,
              new Date(_args.startDate),
              new Date(_args.endDate)
            );
            return `Báo cáo chấm công từ ${_args.startDate} đến ${_args.endDate}:\n${JSON.stringify(report, null, 2)}`;
          } catch (error) {
            return `Tạo báo cáo chấm công thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_late_employees_today',
        description: 'Lấy danh sách nhân viên đi muộn hôm nay',
        parameters: z.object({
          departmentId: z.number().optional().describe('ID phòng ban'),
          limit: z.number().optional().default(10).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const today = new Date().toISOString().split('T')[0];

            // AttendanceService không có method getLateEmployees, sử dụng employeeService
            const lateEmployees = await this.employeeService.getLateEmployees(tenantId, new Date(today).getTime(), {
              departmentId: _args.departmentId,
              limit: _args.limit,
            });

            return `Tìm thấy ${lateEmployees.length} nhân viên đi muộn hôm nay:\n${JSON.stringify(lateEmployees, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên đi muộn thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getTodoExtensionTools() {
    const todoCommentService = this.todoCommentService;
    const todoAttachmentService = this.todoAttachmentService;
    const todoCollaboratorService = this.todoCollaboratorService;
    const todoTagService = this.todoTagService;

    return [
      // Todo Comment Tools
      tool({
        name: 'add_todo_comment',
        description: 'Thêm bình luận vào công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          content: z.string().nonempty().describe('Nội dung bình luận'),
          parentId: z.number().optional().describe('ID bình luận cha (nếu là reply)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createCommentDto = {
              todoId: _args.todoId,
              contentHtml: _args.content, // Sử dụng contentHtml thay vì content
              parentId: _args.parentId,
              createdBy: userId,
            };

            const comment = await todoCommentService.createComment(tenantId, userId, createCommentDto);
            return `Bình luận đã được thêm thành công với ID: ${comment.id}`;
          } catch (error) {
            return `Thêm bình luận thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_todo_comments',
        description: 'Lấy danh sách bình luận của công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          includeReplies: z.boolean().optional().default(true).describe('Bao gồm các reply'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const comments = await todoCommentService.findByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${comments.length} bình luận cho công việc:\n${JSON.stringify(comments, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách bình luận thất bại: ${error.message}`;
          }
        },
      }),

      // Todo Collaborator Tools
      tool({
        name: 'add_todo_collaborator',
        description: 'Thêm cộng tác viên vào công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          userId: z.number().describe('ID người dùng'),
          role: z.enum(['viewer', 'editor', 'manager']).optional().default('editor').describe('Vai trò cộng tác'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const currentUserId = parseInt(runContext?.['userId'] || '0');

            const addCollaboratorDto = {
              todoId: _args.todoId,
              userId: _args.userId,
              role: _args.role,
              addedBy: currentUserId,
            };

            await todoCollaboratorService.addCollaborator(tenantId, currentUserId, addCollaboratorDto);
            return `Cộng tác viên đã được thêm vào công việc thành công`;
          } catch (error) {
            return `Thêm cộng tác viên thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_todo_collaborator',
        description: 'Xóa cộng tác viên khỏi công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          userId: z.number().describe('ID người dùng'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const currentUserId = parseInt(runContext?.['userId'] || '0');
            await todoCollaboratorService.removeCollaborator(tenantId, _args.todoId, _args.userId, currentUserId);
            return `Cộng tác viên đã được xóa khỏi công việc thành công`;
          } catch (error) {
            return `Xóa cộng tác viên thất bại: ${error.message}`;
          }
        },
      }),

      // Todo Tag Tools
      tool({
        name: 'add_todo_tag',
        description: 'Thêm tag vào công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          tagName: z.string().nonempty().describe('Tên tag'),
          color: z.string().optional().describe('Màu sắc tag (hex code)'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const addTagDto = {
              todoId: _args.todoId,
              tagName: _args.tagName,
              color: _args.color,
              createdBy: userId,
            };

            // TodoTagService không có method addTag, cần implement
            return `Tính năng thêm tag chưa được triển khai`;
          } catch (error) {
            return `Thêm tag thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_todo_tag',
        description: 'Xóa tag khỏi công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          tagId: z.number().describe('ID tag'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            // TodoTagService không có method removeTag, cần implement
            return `Tính năng xóa tag chưa được triển khai`;
          } catch (error) {
            return `Xóa tag thất bại: ${error.message}`;
          }
        },
      }),

      // Todo Attachment Tools
      tool({
        name: 'add_todo_attachment',
        description: 'Thêm tệp đính kèm vào công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
          fileName: z.string().nonempty().describe('Tên tệp'),
          fileUrl: z.string().nonempty().describe('URL tệp'),
          fileSize: z.number().optional().describe('Kích thước tệp (bytes)'),
          mimeType: z.string().optional().describe('Loại MIME của tệp'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const addAttachmentDto = {
              todoId: _args.todoId,
              filename: _args.fileName, // Sửa từ fileName thành filename
              url: _args.fileUrl, // Sửa từ fileUrl thành url
              fileSize: _args.fileSize,
              mimeType: _args.mimeType,
              uploadedBy: userId,
            };

            const attachment = await todoAttachmentService.addAttachment(tenantId, userId, addAttachmentDto);
            return `Tệp đính kèm "${_args.fileName}" đã được thêm thành công với ID: ${attachment.id}`;
          } catch (error) {
            return `Thêm tệp đính kèm thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_todo_attachments',
        description: 'Lấy danh sách tệp đính kèm của công việc',
        parameters: z.object({
          todoId: z.number().describe('ID công việc'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const attachments = await todoAttachmentService.findByTodoId(tenantId, _args.todoId);

            return `Tìm thấy ${attachments.length} tệp đính kèm cho công việc:\n${JSON.stringify(attachments, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tệp đính kèm thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }


  /**
   * Lấy tất cả business tools đã định nghĩa
   * Method này sẽ được gọi khi khởi tạo để register tools
   */
  getAllBusinessTools() {
    return [
      ...this.getEmailTools(),
      ...this.getTodoTools(),
      ...this.getEmployeeTools(),
      ...this.getStatisticsTools(),
      ...this.getOkrTools(),
      ...this.getProjectTools(),
      ...this.getDepartmentTools(),
      ...this.getAttendanceTools(),
      ...this.getTodoExtensionTools(),
      ...this.getEventTools(),
    ];
  }

  getKeyResultSupportTools() {
    const keyResultSupportService = this.keyResultSupportService;
    return [
      tool({
        name: 'add_key_result_supports',
        description: 'Thêm các Key Result hỗ trợ cho một Key Result cha',
        parameters: z.object({
          parentKeyResultId: z.number().describe('ID Key Result cha'),
          childKeyResultIds: z.array(z.number()).describe('Danh sách ID các Key Result hỗ trợ'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const createSupportDto = {
              childIds: _args.childKeyResultIds,
            };

            const supports = await keyResultSupportService.addSupports(
              tenantId,
              _args.parentKeyResultId,
              createSupportDto
            );

            return `Đã thêm ${supports.length} Key Result hỗ trợ thành công:\n${JSON.stringify(supports, null, 2)}`;
          } catch (error) {
            return `Thêm Key Result hỗ trợ thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_key_result_supports',
        description: 'Lấy danh sách Key Result hỗ trợ cho một Key Result cha',
        parameters: z.object({
          parentKeyResultId: z.number().describe('ID Key Result cha'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const supports = await keyResultSupportService.getSupports(
              tenantId,
              _args.parentKeyResultId
            );

            return `Tìm thấy ${supports.length} Key Result hỗ trợ:\n${JSON.stringify(supports, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách Key Result hỗ trợ thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'remove_key_result_support',
        description: 'Xóa một Key Result khỏi danh sách hỗ trợ',
        parameters: z.object({
          parentKeyResultId: z.number().describe('ID Key Result cha'),
          childKeyResultId: z.number().describe('ID Key Result con cần xóa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            const result = await keyResultSupportService.removeSupport(
              tenantId,
              _args.parentKeyResultId,
              _args.childKeyResultId
            );

            return result
              ? `Key Result hỗ trợ đã được xóa thành công`
              : `Xóa Key Result hỗ trợ thất bại`;
          } catch (error) {
            return `Xóa Key Result hỗ trợ thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

  getEventTools() {
    const eventService = this.eventService;
    return [
      tool({
        name: 'create_event',
        description: 'Tạo sự kiện mới',
        parameters: z.object({
          title: z.string().nonempty().describe('Tiêu đề sự kiện'),
          description: z.string().optional().describe('Mô tả sự kiện'),
          startTime: z.string().describe('Thời gian bắt đầu (ISO string)'),
          endTime: z.string().describe('Thời gian kết thúc (ISO string)'),
          location: z.string().optional().describe('Địa điểm'),
          attendees: z.array(z.number()).optional().describe('Danh sách ID người tham dự'),
          isAllDay: z.boolean().optional().default(false).describe('Sự kiện cả ngày'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const createEventDto = {
              title: _args.title,
              description: _args.description,
              startTime: new Date(_args.startTime).getTime(),
              endTime: new Date(_args.endTime).getTime(),
              location: _args.location,
              attendees: _args.attendees,
              isAllDay: _args.isAllDay,
              createdBy: userId,
            };

            // EventService không có method create, cần implement
            return `Tính năng tạo sự kiện chưa được triển khai`;
          } catch (error) {
            return `Tạo sự kiện thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'get_events',
        description: 'Lấy danh sách sự kiện',
        parameters: z.object({
          startDate: z.string().optional().describe('Ngày bắt đầu (YYYY-MM-DD)'),
          endDate: z.string().optional().describe('Ngày kết thúc (YYYY-MM-DD)'),
          userId: z.number().optional().describe('ID người dùng (lọc sự kiện của người dùng)'),
          limit: z.number().optional().default(20).describe('Số lượng kết quả tối đa'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');

            // EventService không có method findAll, cần implement
            return `Tính năng lấy danh sách sự kiện chưa được triển khai`;
          } catch (error) {
            return `Lấy danh sách sự kiện thất bại: ${error.message}`;
          }
        },
      }),

      tool({
        name: 'update_event',
        description: 'Cập nhật sự kiện',
        parameters: z.object({
          eventId: z.number().describe('ID sự kiện'),
          title: z.string().optional().describe('Tiêu đề sự kiện'),
          description: z.string().optional().describe('Mô tả sự kiện'),
          startTime: z.string().optional().describe('Thời gian bắt đầu (ISO string)'),
          endTime: z.string().optional().describe('Thời gian kết thúc (ISO string)'),
          location: z.string().optional().describe('Địa điểm'),
          status: z.enum(['scheduled', 'ongoing', 'completed', 'cancelled']).optional().describe('Trạng thái sự kiện'),
        }),
        execute: async (
          _args,
          runContext?: RunContext<RunContextShape>,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(runContext?.['tenantId'] || '0');
            const userId = parseInt(runContext?.['userId'] || '0');

            const updateEventDto = {
              title: _args.title,
              description: _args.description,
              startTime: _args.startTime ? new Date(_args.startTime).getTime() : undefined,
              endTime: _args.endTime ? new Date(_args.endTime).getTime() : undefined,
              location: _args.location,
              status: _args.status,
              updatedBy: userId,
            };

            // EventService không có method update, cần implement
            return `Tính năng cập nhật sự kiện chưa được triển khai`;
          } catch (error) {
            return `Cập nhật sự kiện thất bại: ${error.message}`;
          }
        },
      }),
    ];
  }

}
