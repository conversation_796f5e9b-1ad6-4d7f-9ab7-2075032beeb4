import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FacebookPageConfig } from '../entities/facebook-page-config.entity';

/**
 * Repository quản lý cấu hình Facebook Page
 */
@Injectable()
export class FacebookPageConfigRepository extends Repository<FacebookPageConfig> {
  private readonly logger = new Logger(FacebookPageConfigRepository.name);

  constructor(private dataSource: DataSource) {
    super(FacebookPageConfig, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho Facebook page config
   * @returns SelectQueryBuilder<FacebookPageConfig> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<FacebookPageConfig> {
    return this.createQueryBuilder('facebookPageConfig');
  }

  /**
   * <PERSON><PERSON><PERSON> c<PERSON><PERSON> hình Facebook Page theo pageId
   * @param pageId ID của Facebook Page
   * @returns Cấu hình của Page hoặc null nếu không tìm thấy
   */
  async findByPageId(pageId: string): Promise<FacebookPageConfig | null> {
    this.logger.log(`Tìm cấu hình Facebook Page với ID: ${pageId}`);

    const qb = this.createBaseQuery().where(
      'facebookPageConfig.page_id = :pageId',
      { pageId },
    );

    return qb.getOne();
  }

  /**
   * Tìm tất cả cấu hình Facebook Page của một tenant
   * @param tenantId ID của tenant
   * @returns Danh sách cấu hình Facebook Page
   */
  async findByTenantId(tenantId: number): Promise<FacebookPageConfig[]> {
    this.logger.log(`Tìm cấu hình Facebook Page của tenant ID: ${tenantId}`);

    const qb = this.createBaseQuery()
      .where('facebookPageConfig.tenant_id = :tenantId', { tenantId })
      .andWhere('facebookPageConfig.is_active = :isActive', { isActive: true });

    return qb.getMany();
  }

  /**
   * Tìm tất cả cấu hình Facebook Page của một user
   * @param userId ID của user
   * @returns Danh sách cấu hình Facebook Page
   */
  async findByUserId(userId: number): Promise<FacebookPageConfig[]> {
    this.logger.log(`Tìm cấu hình Facebook Page của user ID: ${userId}`);

    const qb = this.createBaseQuery()
      .where('facebookPageConfig.user_id = :userId', { userId })
      .andWhere('facebookPageConfig.is_active = :isActive', { isActive: true });

    return qb.getMany();
  }

  /**
   * Lưu cấu hình Facebook Page mới
   * @param pageConfig Cấu hình Page cần lưu
   * @returns Cấu hình Page đã lưu
   */
  async savePageConfig(
    pageConfig: FacebookPageConfig,
  ): Promise<FacebookPageConfig> {
    this.logger.log(`Lưu cấu hình Facebook Page: ${pageConfig.pageId}`);
    return this.save(pageConfig);
  }

  /**
   * Cập nhật trạng thái kích hoạt webhook
   * @param pageId ID của Facebook Page
   * @param timestamp Thời gian kích hoạt webhook
   * @returns Số bản ghi đã cập nhật
   */
  async updateWebhookEnabled(
    pageId: string,
    timestamp: number,
  ): Promise<boolean> {
    this.logger.log(`Cập nhật trạng thái webhook cho Page: ${pageId}`);

    const result = await this.update(
      { pageId },
      { webhookEnabledAt: timestamp },
    );

    return result.affected ? result.affected > 0 : false;
  }
}
