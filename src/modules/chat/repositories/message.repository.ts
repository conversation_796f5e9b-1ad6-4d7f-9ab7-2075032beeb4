import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatMessage } from '../entities/chat-message.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';

export interface MessageQueryDto {
  page?: number;
  limit?: number;
  conversationId?: number;
  messageType?: string;
  direction?: string;
  senderType?: string;
  status?: string;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  startDate?: number;
  endDate?: number;
}

/**
 * Repository for ChatMessage entity
 */
@Injectable()
export class MessageRepository {
  private readonly logger = new Logger(MessageRepository.name);

  constructor(
    @InjectRepository(ChatMessage)
    private readonly repository: Repository<ChatMessage>,
  ) {}

  /**
   * Find all messages with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of messages
   */
  async findAll(
    tenantId: number,
    query: MessageQueryDto,
  ): Promise<PaginatedResult<ChatMessage>> {
    const {
      page = 1,
      limit = 10,
      conversationId,
      messageType,
      direction,
      senderType,
      status,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      startDate,
      endDate,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('message');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.where('message.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (conversationId) {
      queryBuilder.andWhere('message.conversationId = :conversationId', {
        conversationId,
      });
    }

    if (messageType) {
      queryBuilder.andWhere('message.messageType = :messageType', {
        messageType,
      });
    }

    if (direction) {
      queryBuilder.andWhere('message.direction = :direction', { direction });
    }

    if (senderType) {
      queryBuilder.andWhere('message.senderType = :senderType', { senderType });
    }

    if (status) {
      queryBuilder.andWhere('message.status = :status', { status });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere(
        'message.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('message.content ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`message.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find message by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Message ID
   * @returns Message or null if not found
   */
  async findById(tenantId: number, id: number): Promise<ChatMessage | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find messages by conversation ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param conversationId Conversation ID
   * @param limit Number of messages to retrieve
   * @param offset Offset for pagination
   * @returns List of messages
   */
  async findByConversationId(
    tenantId: number,
    conversationId: number,
    limit: number = 50,
    offset: number = 0,
  ): Promise<ChatMessage[]> {
    return this.repository.find({
      where: { conversationId, tenantId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Find messages by conversation with pagination
   * @param tenantId ID tenant (required for tenant isolation)
   * @param conversationId Conversation ID
   * @param query Query parameters
   * @returns Paginated list of messages
   */
  async findByConversation(
    tenantId: number,
    conversationId: number,
    query: MessageQueryDto,
  ): Promise<PaginatedResult<ChatMessage>> {
    const {
      page = 1,
      limit = 50,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('message');

    // Add tenantId and conversationId filtering
    queryBuilder
      .where('message.tenantId = :tenantId', { tenantId })
      .andWhere('message.conversationId = :conversationId', { conversationId });

    // Apply sorting
    queryBuilder.orderBy(`message.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find message by Facebook message ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param facebookMessageId Facebook message ID
   * @returns Message or null if not found
   */
  async findByFacebookMessageId(
    tenantId: number,
    facebookMessageId: string,
  ): Promise<ChatMessage | null> {
    return this.repository.findOne({
      where: { facebookMessageId, tenantId },
    });
  }

  /**
   * Create a new message
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Message data
   * @returns Created message
   */
  async create(
    tenantId: number,
    data: Partial<ChatMessage>,
  ): Promise<ChatMessage> {
    const message = this.repository.create({ ...data, tenantId });
    return this.repository.save(message);
  }

  /**
   * Update message
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Message ID
   * @param data Updated message data
   * @returns Updated message or null if not found
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<ChatMessage>,
  ): Promise<ChatMessage | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete message
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Message ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Count messages by conversation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param conversationId Conversation ID
   * @returns Number of messages
   */
  async countByConversation(
    tenantId: number,
    conversationId: number,
  ): Promise<number> {
    return this.repository.count({
      where: { conversationId, tenantId },
    });
  }

  /**
   * Get latest message in conversation
   * @param tenantId ID tenant (required for tenant isolation)
   * @param conversationId Conversation ID
   * @returns Latest message or null if no messages
   */
  async getLatestMessage(
    tenantId: number,
    conversationId: number,
  ): Promise<ChatMessage | null> {
    return this.repository.findOne({
      where: { conversationId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Search messages by content
   * @param tenantId ID tenant (required for tenant isolation)
   * @param searchTerm Search term
   * @param limit Number of results to return
   * @returns List of matching messages
   */
  async searchMessages(
    tenantId: number,
    searchTerm: string,
    limit: number = 20,
  ): Promise<ChatMessage[]> {
    return this.repository
      .createQueryBuilder('message')
      .where('message.tenantId = :tenantId', { tenantId })
      .andWhere('message.content ILIKE :searchTerm', {
        searchTerm: `%${searchTerm}%`,
      })
      .orderBy('message.createdAt', 'DESC')
      .limit(limit)
      .getMany();
  }
}
