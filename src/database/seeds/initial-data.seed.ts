import { DataSource } from 'typeorm';
import { CompanyAccount } from '../../modules/auth/entities/company-account.entity';
import { User } from '../../modules/auth/entities/user.entity';
import { CompanyStatus } from '../../modules/auth/enum/company-status.enum';
import { UserStatus } from '../../modules/auth/enum/user-status.enum';
import * as bcrypt from 'bcrypt';

// Hàm tạo dữ liệu mẫu
export async function seedInitialData(dataSource: DataSource) {
  try {
    // Tạo dữ liệu mẫu cho company
    const companyRepository = dataSource.getRepository(CompanyAccount);
    const companies = [
      {
        id: 1,
        companyName: 'Công ty TNHH ABC',
        subdomain: 'abc',
        taxCode: '0*********',
        companyEmail: '<EMAIL>',
        password: await bcrypt.hash('Company@123', 10),
        phoneNumber: '0*********',
        address: '123 Đường ABC, Quận 1, TP.HCM',
        status: CompanyStatus.ACTIVE,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 2,
        companyName: 'Công ty TNHH XYZ',
        subdomain: 'xyz',
        taxCode: '**********',
        companyEmail: '<EMAIL>',
        password: await bcrypt.hash('Company@123', 10),
        phoneNumber: '0*********',
        address: '456 Đường XYZ, Quận 2, TP.HCM',
        status: CompanyStatus.ACTIVE,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ];

    // Lưu companies vào database
    await companyRepository.save(companies);

    // Tạo dữ liệu mẫu cho user
    const userRepository = dataSource.getRepository(User);
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('Admin@123', 10),
        fullName: 'Nguyễn Văn A',
        departmentId: 1,
        status: UserStatus.ACTIVE,
        position: 'Quản trị viên',
        createdAt: Date.now(),
        address: '123 Đường ABC, Quận 1, TP.HCM',
        phoneNumber: '0*********',
        birthDate: new Date('1990-01-01'),
        gender: 'Nam',
        avatarUrl: null,
        idCardNumber: '*********',
        idCardIssueDate: new Date('2010-01-01'),
        idCardIssuePlace: 'TP.HCM',
        bankAccountNumber: '*********0',
        bankName: 'Vietcombank',
        taxCode: '*********',
        insuranceNumber: '*********',
        userType: 'FULL_TIME',
        tenantId: 1,
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        password: await bcrypt.hash('Manager@123', 10),
        fullName: 'Trần Thị B',
        departmentId: 1,
        status: UserStatus.ACTIVE,
        position: 'Quản lý',
        createdAt: Date.now(),
        address: '456 Đường XYZ, Quận 2, TP.HCM',
        phoneNumber: '0*********',
        birthDate: new Date('1992-02-02'),
        gender: 'Nữ',
        avatarUrl: null,
        idCardNumber: '*********',
        idCardIssueDate: new Date('2012-02-02'),
        idCardIssuePlace: 'TP.HCM',
        bankAccountNumber: '0*********',
        bankName: 'Techcombank',
        taxCode: '*********',
        insuranceNumber: '*********',
        userType: 'FULL_TIME',
        tenantId: 1,
      },
    ];

    // Lưu users vào database
    await userRepository.save(users);

    console.log('Đã thêm dữ liệu mẫu thành công!');
  } catch (error) {
    console.error('Lỗi khi thêm dữ liệu mẫu:', error);
    throw error;
  }
}
